{"name": "ball", "server": "", "platform": "android", "buildPath": "project://build", "debug": true, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": true, "sourceMaps": "inline", "overwriteProjectSettings": {"macroConfig": {"cleanupImageCache": "inherit-project-setting"}, "includeModules": {"physics": "inherit-project-setting", "physics-2d": "inherit-project-setting", "gfx-webgl2": "off"}}, "nativeCodeBundleMode": "wasm", "polyfills": {"targets": "chrome 80"}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": ["/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/android/data/src/effect.bin"], "includes": ["/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/android/data/application.js"], "replaceOnly": ["/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/android/data/main.js"], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "db://assets/scene/main.scene", "outputName": "android", "taskName": "android", "scenes": [{"url": "db://assets/scene/test.scene", "uuid": "0463653e-daaa-46af-960d-ef0eef23f62b"}, {"url": "db://assets/scene/main.scene", "uuid": "45599e55-94c9-434a-bab1-ba87aad4bc26"}], "wasmCompressionMode": false, "packages": {"android": {"packageName": "com.dominostar001.ds001.game", "resizeableActivity": true, "maxAspectRatio": "2.4", "orientation": {"landscapeRight": true, "landscapeLeft": true, "portrait": true, "upsideDown": false}, "apiLevel": 26, "appABIs": ["arm64-v8a", "armeabi-v7a"], "useDebugKeystore": true, "keystorePath": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/keystore/debug.keystore", "keystorePassword": "123456", "keystoreAlias": "debug_keystore", "keystoreAliasPassword": "123456", "appBundle": false, "androidInstant": false, "inputSDK": false, "remoteUrl": "", "sdkPath": "/Users/<USER>/Library/Android/sdk", "ndkPath": "/Users/<USER>/Library/Android/sdk/ndk/25.2.9519653", "javaHome": "", "javaPath": "", "swappy": false, "renderBackEnd": {"vulkan": false, "gles3": true, "gles2": true}}, "native": {"encrypted": false, "xxteaKey": "f8qEwFrNZHsZu6to", "compressZip": false, "JobSystem": "none", "__version__": "1.0.2"}, "cocos-service": {"configID": "def159", "services": [], "__version__": "3.0.8"}}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/android5-26-2025 17-32.log", "useCache": true, "includeModules": ["2d", "3d", "animation", "base", "custom-pipeline", "intersection-2d", "meshopt", "physics-2d-box2d", "primitive", "profiler", "spine", "tween", "ui", "websocket", "webview", "custom-pipeline-builtin-scripts"], "designResolution": {"width": 750, "height": 1334, "fitWidth": true, "fitHeight": true}, "renderPipeline": "fd8ec536-a354-4a17-9c74-4f3883c378c8", "physicsConfig": {"gravity": {"x": 0, "y": -10, "z": 0}, "allowSleep": true, "sleepThreshold": 0.1, "autoSimulation": true, "fixedTimeStep": 0.0166667, "maxSubSteps": 1, "defaultMaterial": "ba21476f-2866-4f81-9c4d-6e359316e448", "collisionGroups": [{"index": 1, "name": "balls"}, {"index": 2, "name": "ballPacket"}, {"index": 3, "name": "tableSide"}], "collisionMatrix": {"0": 14, "1": 15, "2": 3, "3": 3}}, "flags": {"LOAD_BULLET_MANUALLY": false, "LOAD_SPINE_MANUALLY": false}, "customLayers": [], "sortingLayers": [], "macroConfig": {"ENABLE_TRANSPARENT_CANVAS": true, "ENABLE_MULTI_TOUCH": false, "ENABLE_TILEDMAP_CULLING": false, "ENABLE_WEBGL_ANTIALIAS": false}, "customPipeline": true, "useBuildAssetCache": true, "useBuildEngineCache": true, "useBuildTextureCompressCache": true, "useBuildAutoAtlasCache": true, "resolution": {"width": 750, "height": 1334, "policy": 2}, "engineInfo": {"typescript": {"type": "builtin", "custom": "", "builtin": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine", "path": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine"}, "native": {"type": "builtin", "custom": "", "builtin": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native", "path": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native"}}, "appTemplateData": {"debugMode": true, "renderMode": false, "showFPS": true, "resolution": {"width": 750, "height": 1334, "policy": 2}, "md5Cache": false, "cocosTemplate": "", "settingsJsonPath": "src/settings.json", "hasPhysicsAmmo": false, "versionTips": "使用的 application.ejs 版本低于当前编辑器使用的版本，请检查并升级", "customVersion": "1.0.0", "versionCheckTemplate": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/templates/launcher/version-check.ejs"}, "buildEngineParam": {"debug": true, "mangleProperties": false, "inlineEnum": true, "sourceMaps": "inline", "includeModules": ["2d", "3d", "animation", "base", "custom-pipeline", "intersection-2d", "meshopt", "physics-2d-box2d", "primitive", "profiler", "spine", "tween", "ui", "websocket", "webview", "custom-pipeline-builtin-scripts"], "engineVersion": "3.8.5", "md5Map": [], "engineName": "src/cocos-js", "platform": "ANDROID", "useCache": true, "nativeCodeBundleMode": "wasm", "wasmCompressionMode": false, "output": "/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/android/data/src/cocos-js", "targets": "chrome 80", "flags": {"DEBUG": true, "LOAD_BULLET_MANUALLY": false, "LOAD_SPINE_MANUALLY": false}, "entry": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine"}, "buildScriptParam": {"experimentalEraseModules": false, "outputName": "project", "flags": {"DEBUG": true, "LOAD_BULLET_MANUALLY": false, "LOAD_SPINE_MANUALLY": false}, "polyfills": {"targets": "chrome 80"}, "platform": "ANDROID", "commonDir": "/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/android/data/src/chunks", "bundleCommonChunk": false, "targets": "chrome 80", "system": {"preset": "commonjs-like"}}, "assetSerializeOptions": {"cc.EffectAsset": {"glsl1": true, "glsl3": true, "glsl4": false}, "exportCCON": true}, "cocosParams": {"buildDir": "/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/android", "buildAssetsDir": "/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/android/data", "projDir": "/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards", "cmakePath": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake", "nativeEnginePath": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native", "enginePath": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine", "projectName": "ball", "debug": true, "encrypted": false, "xxteaKey": "f8qEwFrNZHsZu6to", "compressZip": false, "cMakeConfig": {"APP_NAME": "set(APP_NAME \"ball\")", "COCOS_X_PATH": "set(COCOS_X_PATH \"/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native\")", "USE_JOB_SYSTEM_TASKFLOW": false, "USE_JOB_SYSTEM_TBB": false, "ENABLE_FLOAT_OUTPUT": false, "USE_PHYSICS_PHYSX": "set(USE_PHYSICS_PHYSX OFF)", "USE_OCCLUSION_QUERY": "set(USE_OCCLUSION_QUERY OFF)", "USE_GEOMETRY_RENDERER": "set(USE_GEOMETRY_RENDERER OFF)", "USE_DEBUG_RENDERER": "set(USE_DEBUG_RENDERER OFF)", "USE_AUDIO": "set(USE_AUDIO OFF)", "USE_VIDEO": "set(USE_VIDEO OFF)", "USE_WEBVIEW": "set(USE_WEBVIEW ON)", "USE_SOCKET": "set(USE_SOCKET ON)", "USE_WEBSOCKET_SERVER": "set(USE_WEBSOCKET_SERVER OFF)", "USE_VENDOR": "set(USE_VENDOR OFF)", "USE_SPINE": "set(USE_SPINE ON)", "USE_DRAGONBONES": "set(USE_DRAGONBONES OFF)", "CC_USE_VULKAN": false, "CC_USE_GLES3": true, "CC_USE_GLES2": true, "CC_ENABLE_SWAPPY": false, "USE_ADPF": true}, "platformParams": {"packageName": "com.dominostar001.ds001.game", "resizeableActivity": true, "maxAspectRatio": "2.4", "orientation": {"landscapeRight": true, "landscapeLeft": true, "portrait": true, "upsideDown": false}, "apiLevel": 26, "appABIs": ["arm64-v8a", "armeabi-v7a"], "useDebugKeystore": true, "keystorePath": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/keystore/debug.keystore", "keystorePassword": "123456", "keystoreAlias": "debug_keystore", "keystoreAliasPassword": "123456", "appBundle": false, "androidInstant": false, "inputSDK": false, "remoteUrl": "", "sdkPath": "/Users/<USER>/Library/Android/sdk", "ndkPath": "/Users/<USER>/Library/Android/sdk/ndk/25.2.9519653", "javaHome": "", "javaPath": "", "swappy": false, "renderBackEnd": {"vulkan": false, "gles3": true, "gles2": true}}, "platform": "android", "packageName": "com.dominostar001.ds001.game"}, "generateCompileConfig": true, "dest": "/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/android/data/assets"}