{"name": "ball", "server": "", "platform": "ios", "buildPath": "project://build", "debug": true, "buildMode": "normal", "mangleProperties": false, "md5Cache": false, "skipCompressTexture": true, "sourceMaps": true, "overwriteProjectSettings": {"macroConfig": {"cleanupImageCache": "inherit-project-setting"}, "includeModules": {"physics": "inherit-project-setting", "physics-2d": "inherit-project-setting", "gfx-webgl2": "off"}}, "nativeCodeBundleMode": "asmjs", "polyfills": {"targets": "chrome 80"}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": ["/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/data/src/effect.bin"], "includes": ["/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/data/application.js"], "replaceOnly": ["/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/data/main.js"], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "db://assets/scene/main.scene", "outputName": "ios", "taskName": "ios", "scenes": [{"url": "db://assets/scene/main.scene", "uuid": "45599e55-94c9-434a-bab1-ba87aad4bc26"}, {"url": "db://assets/scene/test.scene", "uuid": "0463653e-daaa-46af-960d-ef0eef23f62b"}], "wasmCompressionMode": false, "packages": {"ios": {"executableName": "", "packageName": "io.gamedstar.eventstar.use", "renderBackEnd": {"gles2": false, "gles3": false, "metal": true}, "skipUpdateXcodeProject": false, "orientation": {"portrait": true, "upsideDown": false, "landscapeRight": true, "landscapeLeft": true}, "osTarget": {"iphoneos": false, "simulator": true}, "targetVersion": "12.0", "__version__": "1.0.1"}, "native": {"encrypted": false, "xxteaKey": "f8qEwFrNZHsZu6to", "compressZip": false, "JobSystem": "none", "__version__": "1.0.2"}, "cocos-service": {"configID": "def159", "services": [], "__version__": "3.0.8"}}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/ios5-26-2025 17-31.log", "useCache": true, "includeModules": ["2d", "3d", "animation", "base", "custom-pipeline", "dragon-bones", "intersection-2d", "meshopt", "physics-2d-box2d-wasm", "primitive", "profiler", "spine", "tween", "ui", "websocket", "custom-pipeline-builtin-scripts"], "designResolution": {"width": 750, "height": 1334, "fitWidth": true, "fitHeight": true}, "renderPipeline": "fd8ec536-a354-4a17-9c74-4f3883c378c8", "physicsConfig": {"gravity": {"x": 0, "y": -10, "z": 0}, "allowSleep": true, "sleepThreshold": 0.1, "autoSimulation": true, "fixedTimeStep": 0.0166667, "maxSubSteps": 1, "defaultMaterial": "ba21476f-2866-4f81-9c4d-6e359316e448", "collisionGroups": [{"index": 1, "name": "balls"}, {"index": 2, "name": "ballPacket"}, {"index": 3, "name": "tableSide"}], "collisionMatrix": {"0": 14, "1": 15, "2": 3, "3": 3}}, "flags": {"LOAD_BULLET_MANUALLY": false, "LOAD_SPINE_MANUALLY": false, "LOAD_BOX2D_MANUALLY": false}, "customLayers": [], "sortingLayers": [], "macroConfig": {"ENABLE_TRANSPARENT_CANVAS": true, "ENABLE_MULTI_TOUCH": false, "ENABLE_TILEDMAP_CULLING": false, "ENABLE_WEBGL_ANTIALIAS": false}, "customPipeline": true, "useBuildAssetCache": true, "useBuildEngineCache": true, "useBuildTextureCompressCache": true, "useBuildAutoAtlasCache": true, "resolution": {"width": 750, "height": 1334, "policy": 2}, "engineInfo": {"typescript": {"type": "builtin", "custom": "", "builtin": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine", "path": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine"}, "native": {"type": "builtin", "custom": "", "builtin": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native", "path": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native"}}, "appTemplateData": {"debugMode": true, "renderMode": false, "showFPS": true, "resolution": {"width": 750, "height": 1334, "policy": 2}, "md5Cache": false, "cocosTemplate": "", "settingsJsonPath": "src/settings.json", "hasPhysicsAmmo": false, "versionTips": "使用的 application.ejs 版本低于当前编辑器使用的版本，请检查并升级", "customVersion": "1.0.0", "versionCheckTemplate": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/templates/launcher/version-check.ejs"}, "buildEngineParam": {"debug": true, "mangleProperties": false, "inlineEnum": true, "sourceMaps": true, "includeModules": ["2d", "3d", "animation", "base", "custom-pipeline", "dragon-bones", "intersection-2d", "meshopt", "physics-2d-box2d-wasm", "primitive", "profiler", "spine", "tween", "ui", "websocket", "custom-pipeline-builtin-scripts"], "engineVersion": "3.8.5", "md5Map": [], "engineName": "src/cocos-js", "platform": "IOS", "useCache": true, "nativeCodeBundleMode": "asmjs", "wasmCompressionMode": false, "output": "/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/data/src/cocos-js", "targets": "chrome 80", "flags": {"DEBUG": true, "LOAD_BULLET_MANUALLY": false, "LOAD_SPINE_MANUALLY": false, "LOAD_BOX2D_MANUALLY": false}, "entry": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine"}, "buildScriptParam": {"experimentalEraseModules": false, "outputName": "project", "flags": {"DEBUG": true, "LOAD_BULLET_MANUALLY": false, "LOAD_SPINE_MANUALLY": false, "LOAD_BOX2D_MANUALLY": false}, "polyfills": {"targets": "chrome 80"}, "platform": "IOS", "commonDir": "/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/data/src/chunks", "bundleCommonChunk": false, "targets": "chrome 80", "system": {"preset": "commonjs-like"}}, "assetSerializeOptions": {"cc.EffectAsset": {"glsl1": false, "glsl3": false, "glsl4": true}, "exportCCON": true}, "cocosParams": {"buildDir": "/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios", "buildAssetsDir": "/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/data", "projDir": "/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards", "cmakePath": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake", "nativeEnginePath": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native", "enginePath": "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine", "projectName": "ball", "debug": true, "encrypted": false, "xxteaKey": "f8qEwFrNZHsZu6to", "compressZip": false, "cMakeConfig": {"APP_NAME": "set(APP_NAME \"ball\")", "COCOS_X_PATH": "set(COCOS_X_PATH \"/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native\")", "USE_JOB_SYSTEM_TASKFLOW": false, "USE_JOB_SYSTEM_TBB": false, "ENABLE_FLOAT_OUTPUT": false, "USE_PHYSICS_PHYSX": "set(USE_PHYSICS_PHYSX OFF)", "USE_OCCLUSION_QUERY": "set(USE_OCCLUSION_QUERY OFF)", "USE_GEOMETRY_RENDERER": "set(USE_GEOMETRY_RENDERER OFF)", "USE_DEBUG_RENDERER": "set(USE_DEBUG_RENDERER OFF)", "USE_AUDIO": "set(USE_AUDIO OFF)", "USE_VIDEO": "set(USE_VIDEO OFF)", "USE_WEBVIEW": "set(USE_WEBVIEW OFF)", "USE_SOCKET": "set(USE_SOCKET ON)", "USE_WEBSOCKET_SERVER": "set(USE_WEBSOCKET_SERVER OFF)", "USE_VENDOR": "set(USE_VENDOR OFF)", "USE_SPINE": "set(USE_SPINE ON)", "USE_DRAGONBONES": "set(USE_DRAGONBONES ON)", "CC_USE_GLES2": false, "CC_USE_GLES3": false, "CC_USE_METAL": true, "MACOSX_BUNDLE_GUI_IDENTIFIER": "set(MACOSX_BUNDLE_GUI_IDENTIFIER io.gamedstar.eventstar.use)", "TARGET_IOS_VERSION": "set(TARGET_IOS_VERSION 12.0)", "USE_PORTRAIT": true, "CUSTOM_COPY_RESOURCE_HOOK": false, "CC_EXECUTABLE_NAME": "set(CC_EXECUTABLE_NAME \"ball-mobile\")"}, "platformParams": {"orientation": {"portrait": true, "upsideDown": false, "landscapeRight": true, "landscapeLeft": true}, "bundleId": "io.gamedstar.eventstar.use", "skipUpdateXcodeProject": false, "simulator": true, "iphoneos": false}, "platform": "ios", "packageName": "io.gamedstar.eventstar.use", "executableName": "ball-mobile"}, "generateCompileConfig": true, "dest": "/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/data/assets"}