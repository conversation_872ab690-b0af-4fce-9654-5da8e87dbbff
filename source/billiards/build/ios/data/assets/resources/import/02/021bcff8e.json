[1, ["0019a888-dd8b-4182-bbe2-cf088be1c0f1@f9941", "97cf910f-8ea0-4895-b3b6-a8563a345961", "1263d74c-8167-4928-91a6-4e2672411f47@17020", "1804a706-78db-4e88-b1e3-09731d4c82e6@f9941", "1957121e-9cef-434c-82bd-bd467ebc8665@6c48a", "a3cd009f-0ab0-420d-9278-b9fdab939bbc"], ["node", "_spriteFrame", "root", "data", "_mesh", "mainTexture", "_effectAsset"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_lpos", "_children", "_lscale"], 0, 9, 4, 1, 5, 2, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CircleCollider2D", ["_group", "_friction", "_radius", "tag", "_restitution", "_density", "_sensor", "node", "__prefab"], -4, 1, 4], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.RigidBody2D", ["enabledContactListener", "bullet", "_group", "_gravityScale", "_linearDamping", "_fixedRotation", "node", "__prefab"], -3, 1, 4], ["bf426p3tdxLdY/l2eLh7Gql", ["ballSp", "node", "__prefab"], 2, 1, 4], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_name", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.ModelBakeSettings", [], 3], ["cc.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", ["node", "__prefab"], 3, 1, 4], ["cc.Animation", ["node", "__prefab"], 3, 1, 4], ["cc.Material", ["_name", "_techIdx", "_states", "_defines", "_props"], -1, 12]], [[6, 0, 2], [9, 0, 1, 2, 3, 4, 5, 5], [1, 0, 1, 2, 1], [0, 0, 1, 5, 3, 4, 6, 3], [4, 0, 1, 2, 3], [5, 0, 2], [0, 0, 1, 7, 3, 4, 6, 3], [0, 0, 1, 5, 3, 4, 8, 3], [0, 0, 2, 1, 5, 3, 4, 4], [1, 0, 1, 1], [2, 3, 0, 1, 4, 2, 7, 8, 6], [2, 0, 5, 6, 1, 2, 7, 8, 6], [7, 0, 1, 2, 3, 4, 5, 6, 7, 7], [8, 0, 1, 2, 2], [10, 0, 1, 2, 3, 4, 5, 2], [11, 1], [12, 0, 1, 1], [3, 1, 2, 3, 1], [3, 0, 1, 2, 3, 2], [13, 0, 1, 1], [14, 0, 1, 2, 3, 4, 5]], [[[[4, ".bin", 815868016, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 72464, "length": 14280, "count": 7140, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 72464, "count": 1294, "stride": 56}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}, {"name": "a_texCoord1", "format": 21, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.49748557806015015, -0.5, -0.4989933371543884], "maxPosition", 8, [1, 0.49949654936790466, 0.5, 0.49899348616600037]]], -1], 0, 0, [], [], []], [[[5, "ball0"], [6, "ball0", 33554432, [-7, -8, -9, -10], [[2, -2, [0, "c8C0kbw2JA36+WiQSCcvT/"], [5, 32, 32]], [10, 1, 2, 0, 0.85, 16, -3, [0, "2aDkYMTJhEUaAb104fkXVp"]], [12, true, true, 2, 0, 0.5, true, -4, [0, "a0W9X+0nNAqohkhiIsnftc"]], [11, 2, 0, true, 0, 31, -5, [0, "2aDkYMTJhEUaAb104fkXVp"]], [13, null, -6, [0, "d4010gnnNEiIUzP/GWjbAv"]]], [1, "0du/d1DEhLkLO7BbnsJEB7", null, null, null, -1, 0], [1, 17.004, 200.804, 0]], [7, "sphere", 33554432, 1, [[14, "Sphere<ModelComponent>", -11, [0, "fepSfA4U5FCaUM9R/OZkl/"], [1], [15], 2], [16, -12, [0, "a44UO8buVMF5cELuNAV+7Y"]], [2, -13, [0, "7fz07zUBhNe6eN7g0g3lLL"], [5, 1, 1]]], [1, "3fKB/NoFlBjZ1Cbz5hzGmI", null, null, null, 1, 0], [1, 30, 30, 30]], [3, "shadow", 33554432, 1, [[2, -14, [0, "88YEpI2pxDe74kngH5sANL"], [5, 40, 40]], [17, -15, [0, "2evEqlJXBPDZGkjtsKDKM8"], 0]], [1, "4dlRqrrvZE8JoO68CAYKfX", null, null, null, 1, 0], [1, 0, -3.6, 0]], [3, "highlight", 33554432, 1, [[2, -16, [0, "61ENugSANKI412YUrZA/NT"], [5, 32, 32]], [18, 0, -17, [0, "a7lw5h0XpLlIXrfsXfm/QS"], 3]], [1, "c42K0CEm1OoarnYUDXhinN", null, null, null, 1, 0], [1, -0.44, -1, 0]], [8, "hintCircle", false, 33554432, 1, [[9, -18, [0, "0a45MzT+hN0rJ7PpM5yh5a"]], [19, -19, [0, "8a67xNbwtKWYZz/4cLrCtX"]]], [1, "d12SIjO4lLgpi8XBb4O0A0", null, null, null, 1, 0]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, -2, 2, 0, -3, 4, 0, -4, 5, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 3, 1, 19], [0, 0, 0, 0], [1, -1, 4, 1], [0, 1, 2, 3]], [[[20, "ball", "0", [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_TEXTURE": true}, {}, {}], [[[{}, "tilingOffset", 8, [2, 1.03, 1, 0, 0], "mainTexture", 6, 0], {}, {}], 11, 0, 0]]], 0, 0, [0, 0], [5, 6], [4, 5]]]]