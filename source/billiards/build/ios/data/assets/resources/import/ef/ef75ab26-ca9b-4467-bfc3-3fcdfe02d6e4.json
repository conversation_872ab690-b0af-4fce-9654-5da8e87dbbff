[1, ["fd0c36d8-11b4-4071-9acf-20a783b46e64", "90efca05-c77e-4c9d-a239-1570be35d102@f9941", "b57ab3bd-add2-46cc-8978-631f33deccf6@f9941", "edab19f5-8cea-4f03-9c6c-3f723c5563a3@f9941"], ["node", "_spriteFrame", "_font", "root", "nodeMissed", "tipNoHitCount", "tipLabel", "data"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_lpos", "_parent"], 1, 9, 4, 2, 5, 1], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Widget", ["_alignFlags", "_right", "_left", "_originalWidth", "node", "__prefab"], -1, 1, 4], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_overflow", "_isSystemFontUsed", "_horizontalAlign", "_enableOutline", "node", "__prefab", "_outlineColor"], -4, 1, 4, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos"], 1, 1, 12, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.<PERSON><PERSON>", ["node", "__prefab", "clickEvents"], 3, 1, 4, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["edf98QH2D1AUoCrtQlbxFrU", ["node", "__prefab"], 3, 1, 4], ["cc.UIOpacity", ["_enabled", "node", "__prefab"], 2, 1, 4], ["4b3d3l5GaBDVIkQU9s5tAzZ", ["node", "__prefab", "tipLabel", "tipNoHitCount", "nodeMissed"], 3, 1, 4, 1, 1, 1]], [[7, 0, 2], [10, 0, 1, 2, 3, 4, 5, 5], [1, 0, 1, 2, 1], [0, 0, 1, 6, 2, 3, 5, 3], [6, 0, 1, 2, 3, 4, 5, 3], [3, 1, 2, 3, 1], [2, 0, 2, 4, 5, 3], [11, 0, 1, 1], [5, 0, 2], [0, 0, 1, 4, 2, 3, 5, 3], [0, 0, 1, 6, 4, 2, 3, 3], [1, 0, 1, 2, 3, 1], [1, 0, 1, 1], [3, 0, 1, 2, 3, 2], [8, 0, 1, 2, 1], [9, 0, 1, 2, 3], [2, 0, 1, 4, 5, 3], [2, 0, 3, 4, 5, 3], [4, 0, 5, 1, 2, 3, 4, 6, 7, 8, 9, 8], [4, 0, 1, 2, 3, 4, 7, 8, 6], [12, 0, 1, 2, 2], [13, 0, 1, 2, 3, 4, 1]], [[8, "tipNoHitItem"], [9, "tipNoHitItem", 33554432, [-10, -11, -12], [[2, -2, [0, "afoteK7HlCtrGcM6PUaLzF"], [5, 750, 406]], [5, -3, [0, "c1FFR7a01IJ6zzJLL/MmA5"], 2], [20, false, -4, [0, "a9hR/TkNlBu5+qfcDSzlvg"]], [17, 44, 750, -5, [0, "84KnsxDGNOp7NSVNUohqM2"]], [21, -9, [0, "04T4VFj/9KxprnFDQB9L1I"], -8, -7, -6]], [1, "a2bh6rq95LFLUdPLcrP4I/", null, null, null, -1, 0], [1, 0, -464, 0]], [3, "btn_close", 33554432, 1, [[2, -13, [0, "daMHd48kxMeImb7GaTwQQ5"], [5, 66, 69]], [5, -14, [0, "8ea86t/x5LDY42bUjKQIYq"], 0], [14, -15, [0, "62b7pF9ZxN14yF33/QwIHF"], [[15, "4b3d3l5GaBDVIkQU9s5tAzZ", "hideTip", 1]]], [16, 32, 15.466999999999985, -16, [0, "09h6cYoMlL8qEGVWxbAB5S"]]], [1, "23hSxTQOBMY4ex0llEKjef", null, null, null, 1, 0], [1, 326.533, 97.018, 0]], [10, "node_missed", 33554432, 1, [-18, -19], [[12, -17, [0, "93kay4CtFE8aIl8keCmRNL"]]], [1, "71DJS7OstDYqdu/+DLc1+4", null, null, null, 1, 0]], [4, "txtNoHitCount", 33554432, 3, [[[11, -20, [0, "69SXSTr75AbqOUllxFFGSR"], [5, 300, 54.4], [0, 0, 0.5]], -21, [7, -22, [0, "24o2xyGU1H3aFMMqedsmQq"]], [6, 8, -14.403999999999996, -23, [0, "2clT5DunVFwbke/nTxW57n"]]], 4, 1, 4, 4], [1, "b9+20ZbYZEV5FinlXTS90V", null, null, null, 1, 0], [1, -64.404, 2.155000000000001, 0]], [3, "icon_foul", 33554432, 3, [[2, -24, [0, "96hvvbGIRGr7k3mRdqln2w"], [5, 67, 59]], [13, 0, -25, [0, "9cnj4V3pVLh710g+Z7Mlws"], 1], [6, 8, -90.62099999999998, -26, [0, "d9sEgtPBhNbIaUgKCa8h/w"]]], [1, "f9um5YPCxD4Z4QRiEn47RQ", null, null, null, 1, 0], [1, -107.12099999999998, 4.329000000000008, 0]], [4, "tipLabel", 33554432, 1, [[[2, -27, [0, "4eAp4ScghGwZUlyKcqr/uY"], [5, 644, 50.4]], -28, [7, -29, [0, "3cqsUssstL2pxdh03Ygim8"]]], 4, 1, 4], [1, "0efIkVZpxBaq8X/Tp/2hsV", null, null, null, 1, 0], [1, 0, -90.537, 0]], [18, "", 0, 38, 38, 1, false, true, 4, [0, "a5UXb3drVPiIR2n3IcZgwW"], [4, 4280033191]], [19, "", 28, 28, 3, false, 6, [0, "d0/iHCbh9J8IYc1js5hGYK"]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 4, 3, 0, 5, 7, 0, 6, 8, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 6, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, -1, 5, 0, -2, 4, 0, 0, 4, 0, -2, 7, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, -2, 8, 0, 0, 6, 0, 7, 1, 29], [0, 0, 0, 7, 8], [1, 1, 1, 2, 2], [1, 2, 3, 0, 0]]