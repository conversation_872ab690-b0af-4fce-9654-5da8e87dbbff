[1, ["89e28899-9e89-43ef-88e5-e5a67715aa7e@f9941", "39d1dcd1-cdb2-4974-b536-b954922b1616@f9941", "fd0c36d8-11b4-4071-9acf-20a783b46e64", "4519243f-cc5f-4ac8-be53-067ac614fe71@f9941"], ["node", "_spriteFrame", "_font", "root", "wb<PERSON>iew", "data"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_parent", "_lpos"], 1, 9, 4, 2, 1, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos"], 1, 1, 12, 4, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.<PERSON><PERSON>", ["node", "__prefab", "clickEvents"], 3, 1, 4, 9], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_overflow", "_enableWrapText", "_isSystemFontUsed", "_enableOutline", "node", "__prefab", "_color", "_font"], -5, 1, 4, 5, 6], ["edf98QH2D1AUoCrtQlbxFrU", ["key", "node", "__prefab"], 2, 1, 4], ["cc.WebView", ["node", "__prefab"], 3, 1, 4], ["ca6edRSc/JDN7VB0EovZERj", ["showType", "node", "__prefab", "wb<PERSON>iew"], 2, 1, 4, 1], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4]], [[5, 0, 2], [4, 0, 1, 2, 1], [6, 0, 1, 2, 3, 4, 5, 5], [0, 0, 1, 5, 2, 3, 6, 3], [1, 0, 1, 2, 3, 4, 3], [2, 0, 2], [0, 0, 1, 4, 2, 3, 3], [0, 0, 1, 5, 4, 2, 3, 6, 3], [3, 0, 1, 2, 3, 4, 5, 3], [1, 2, 3, 4, 1], [7, 0, 1, 2, 1], [8, 0, 1, 2, 3, 4], [9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 9], [10, 0, 1, 2, 2], [11, 0, 1, 1], [12, 0, 1, 2, 3, 2], [13, 0, 1, 1]], [[5, "ruleWebView"], [6, "ruleWebView", 33554432, [-6], [[1, -2, [0, "0fIF1j02xN36nIOQxRW3ka"], [5, 750, 1334]], [15, 1, -4, [0, "ddavetBgZGD6bR3mmufzJp"], -3], [16, -5, [0, "f7B91zzA9NT6/w/akYYXnK"]]], [2, "d3MqvxLJBAyo+6dAe4wyyt", null, null, null, -1, 0]], [7, "Node", 33554432, 1, [-9, -10, -11, -12], [[1, -7, [0, "712amIWOtCQotsRcRaQsVj"], [5, 728, 877]], [4, 1, 0, -8, [0, "c3xpwETbZPVKo6U2Myy0YK"], 3]], [2, "5dKYhNyt9Gf4vSFSusH/3t", null, null, null, 1, 0], [1, 0, 45, 0]], [3, "btn_close", 33554432, 2, [[1, -13, [0, "fcTR7uCZdGN64lJtwdFVr7"], [5, 47, 47]], [9, -14, [0, "790xcJO19C8o1cH9UH9F6i"], 1], [10, -15, [0, "82dIyC6LVCt4+PHlATqFS4"], [[11, "ca6edRSc/JDN7VB0EovZERj", "onCancel", "0", 1]]]], [2, "e5vS8l7/dA0pukgm8ObCju", null, null, null, 1, 0], [1, 315.06600000000003, 394.549, 0]], [3, "title", 33554432, 2, [[1, -16, [0, "64l1iJGO9G8o3Ojr0t6Prk"], [5, 728, 59.44]], [12, "Rule", 44, 44, 44, 1, false, false, true, -17, [0, "6bnRfHd1lPbYd3iy0F3DEz"], [4, 4287424511], 2], [13, "rule", -18, [0, "8f8+dR8uxEhYff/OZ1a9PA"]]], [2, "edpiwvcuNM+bf5/CmI21ek", null, null, null, 1, 0], [1, -3, 396.738, 0]], [3, "bgIn", 33554432, 2, [[1, -19, [0, "4fLmEc7PxNw4EpssaZ3l9S"], [5, 708, 778]], [4, 1, 0, -20, [0, "71vIq2/3lJgr/YSgpDbOpN"], 0]], [2, "1b6yMZiyhCQIfzxUKEQ9m6", null, null, null, 1, 0], [1, 0, -37, 0]], [8, "webView", 33554432, 2, [[[1, -21, [0, "ceLWLBUcNI4qRHDOdmjlOg"], [5, 655, 680]], -22], 4, 1], [2, "c0WFA7pLpA2J5CPkE9LMzN", null, null, null, 1, 0], [1, 0, -30, 0]], [14, 6, [0, "892Na+W3VJbbQyDJSSgtwp"]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 7, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, -2, 3, 0, -3, 4, 0, -4, 6, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, -2, 7, 0, 5, 1, 22], [0, 0, 0, 0], [1, 1, 2, 1], [0, 1, 2, 3]]