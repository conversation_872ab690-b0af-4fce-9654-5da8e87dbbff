[1, ["0019a888-dd8b-4182-bbe2-cf088be1c0f1@f9941", "97cf910f-8ea0-4895-b3b6-a8563a345961", "1263d74c-8167-4928-91a6-4e2672411f47@17020", "1804a706-78db-4e88-b1e3-09731d4c82e6@f9941"], ["node", "_spriteFrame", "_mesh", "root", "data"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_lpos", "_children", "_lscale"], 0, 9, 4, 1, 5, 2, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.CircleCollider2D", ["tag", "_friction", "_radius", "_restitution", "_density", "_sensor", "node", "__prefab"], -3, 1, 4], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_name", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.ModelBakeSettings", [], 3], ["cc.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", ["node", "__prefab"], 3, 1, 4], ["cc.Animation", ["node", "__prefab"], 3, 1, 4], ["cc.RigidBody2D", ["enabledContactListener", "bullet", "_group", "_gravityScale", "_linearDamping", "_fixedRotation", "node", "__prefab"], -3, 1, 4], ["edde8Kd535F+Z1h4Lwir1v0", ["ballSp", "node", "__prefab"], 2, 1, 4]], [[5, 0, 2], [6, 0, 1, 2, 3, 4, 5, 5], [1, 0, 1, 2, 1], [0, 0, 1, 5, 3, 4, 6, 3], [4, 0, 2], [0, 0, 1, 7, 3, 4, 6, 3], [0, 0, 1, 5, 3, 4, 8, 3], [0, 0, 2, 1, 5, 3, 4, 4], [1, 0, 1, 1], [2, 1, 2, 3, 1], [2, 0, 1, 2, 3, 2], [7, 0, 1, 2, 3, 4, 5, 2], [8, 1], [9, 0, 1, 1], [10, 0, 1, 1], [3, 0, 1, 3, 2, 6, 7, 5], [3, 0, 4, 5, 1, 2, 6, 7, 6], [11, 0, 1, 2, 3, 4, 5, 6, 7, 7], [12, 0, 1, 2, 2]], [[4, "whiteBall0"], [5, "whiteBall0", 33554432, [-7, -8, -9, -10], [[2, -2, [0, "c8C0kbw2JA36+WiQSCcvT/"], [5, 32, 32]], [15, 1, 0, 0.85, 16, -3, [0, "2aDkYMTJhEUaAb104fkXVp"]], [17, true, true, 2, 0, 0.5, true, -4, [0, "a0W9X+0nNAqohkhiIsnftc"]], [16, 2, 0, true, 0, 31, -5, [0, "2aDkYMTJhEUaAb104fkXVp"]], [18, null, -6, [0, "f8bRB6dTFFP7RqbqtMri0e"]]], [1, "0du/d1DEhLkLO7BbnsJEB7", null, null, null, -1, 0], [1, 17.004, 200.804, 0]], [6, "sphere", 33554432, 1, [[11, "Sphere<ModelComponent>", -11, [0, "fepSfA4U5FCaUM9R/OZkl/"], [1], [12], 2], [13, -12, [0, "a44UO8buVMF5cELuNAV+7Y"]], [2, -13, [0, "7fz07zUBhNe6eN7g0g3lLL"], [5, 1, 1]]], [1, "3fKB/NoFlBjZ1Cbz5hzGmI", null, null, null, 1, 0], [1, 30, 30, 30]], [3, "shadow", 33554432, 1, [[2, -14, [0, "5eH+n9FqVBkKWRIviAPHU4"], [5, 40, 40]], [9, -15, [0, "83vCrOSpVAR75dVN4WmMVz"], 0]], [1, "15H4kRuZRAwo/q6ODAmXy9", null, null, null, 1, 0], [1, 0, -3.6, 0]], [3, "highlight", 33554432, 1, [[2, -16, [0, "64plWUhgpCUpy0s9lNXgc2"], [5, 32, 32]], [10, 0, -17, [0, "2bBEVpVSxFvaCtTbDn0Qdl"], 3]], [1, "25cwt7CeNKQZ/b3qXo2vZM", null, null, null, 1, 0], [1, -0.44, 0, 0]], [7, "hintCircle", false, 33554432, 1, [[8, -18, [0, "79nr2/wj1AwJJwAGSWqDQb"]], [14, -19, [0, "b987Bd0GpAKZeHo0dRjN8Q"]]], [1, "860ac/1dpPb6FL4+3hrfog", null, null, null, 1, 0]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, -2, 2, 0, -3, 4, 0, -4, 5, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 4, 1, 19], [0, 0, 0, 0], [1, -1, 2, 1], [0, 1, 2, 3]]