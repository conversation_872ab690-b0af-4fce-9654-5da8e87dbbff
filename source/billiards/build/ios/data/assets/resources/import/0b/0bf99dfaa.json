[1, ["63eaf9a3-153a-4e2d-9abd-91cbbb7d63a4@6c48a", "0019a888-dd8b-4182-bbe2-cf088be1c0f1@f9941", "1804a706-78db-4e88-b1e3-09731d4c82e6@f9941", "9800039e-478e-4818-8228-129c713b3a14", "6bb9b7d4-7e62-4501-a638-ba268f838f00@93043", "6bb9b7d4-7e62-4501-a638-ba268f838f00", "e706adc5-34f6-49c2-bdea-dc96fbe04083"], ["_textureSource", "node", "_spriteFrame", "root", "ballSp", "data", "_customMaterial", "_atlas", "_effectAsset"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_layer", "_components", "_prefab", "_parent", "_children", "_lpos"], 1, 9, 4, 1, 2, 5], ["cc.Sprite", ["_sizeMode", "node", "__prefab", "_spriteFrame", "_color"], 2, 1, 4, 6, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CircleCollider2D", ["_group", "_friction", "_radius", "tag", "_restitution", "_density", "_sensor", "node", "__prefab"], -4, 1, 4], "cc.SpriteAtlas", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab"], 1, 1, 12, 4], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.RigidBody2D", ["enabledContactListener", "bullet", "_gravityScale", "_linearDamping", "node", "__prefab"], -1, 1, 4], ["bf426p3tdxLdY/l2eLh7Gql", ["scrollSpeed", "node", "__prefab", "ballSp"], 2, 1, 4, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Animation", ["node", "__prefab"], 3, 1, 4], ["cc.Material", ["_name", "_props", "_states", "_defines"], -1], ["cc.EffectAsset", ["_name", "shaders", "techniques"], 0]], [[8, 0, 2], [11, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 1], [1, 0, 1, 4, 2, 3, 6, 3], [6, 0, 2], [1, 0, 1, 5, 2, 3, 3], [1, 0, 1, 4, 2, 3, 3], [7, 0, 1, 2, 3, 4, 3], [3, 0, 1, 1], [4, 3, 0, 1, 4, 2, 7, 8, 6], [4, 0, 5, 6, 1, 2, 7, 8, 6], [9, 0, 1, 2, 3, 4, 5, 5], [10, 0, 1, 2, 3, 2], [2, 1, 2, 3, 1], [2, 0, 1, 2, 3, 2], [2, 0, 1, 2, 4, 2], [12, 0, 1, 1], [13, 0, 1, 2, 3, 5], [14, 0, 1, 2, 4]], [[[[4, "ball"], [5, "ball", 33554432, [-8, -9, -10, -11], [[2, -2, [0, "eeQ2uWUbxIk4nsDw73i2kt"], [5, 32, 32]], [9, 1, 2, 0, 0.85, 16, -3, [0, "88bgGoUdZBEqI3c7rVHju/"]], [11, true, true, 0, 0.5, -4, [0, "95X7zxrYRGSZkD2JbMJFYh"]], [10, 2, 0, true, 0, 32, -5, [0, "57IiNsqq1Huae0KZuGOtnd"]], [12, 1, -7, [0, "31NebEO+5ASKst7HEica/6"], -6]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [3, "shadow", 33554432, 1, [[2, -12, [0, "12RzEFP69CKJfkti9ngUxS"], [5, 40, 40]], [13, -13, [0, "07ooxaFjpJAYmBgzCDGZya"], 0]], [1, "86odh6WbVJsJJKM2OOPZIi", null, null, null, 1, 0], [1, 0, -3.6, 0]], [7, "ballSp", 33554432, 1, [[[2, -14, [0, "6ePu3QbMZFkadJ9U+j7o0+"], [5, 32, 32]], -15], 4, 1], [1, "b1sIJgXoVDp72x0aZ6s8Hz", null, null, null, 1, 0]], [3, "highlight", 33554432, 1, [[2, -16, [0, "8dkRGQD1FND5JXoF77laJb"], [5, 32, 32]], [14, 0, -17, [0, "86vcsvlyBP35PAhegXFdgt"], 1]], [1, "7c8C+PZLlM3qXWau5HB/yu", null, null, null, 1, 0], [1, -0.4399999999999977, -1, 0]], [6, "hintCircle", 33554432, 1, [[8, -18, [0, "22oYqljotDMLTbASa8eLo+"]], [16, -19, [0, "4dI3KClA9DAppGHmbyoLMH"]]], [1, "6cx+QiNkdFOYNFQ34w4egy", null, null, null, 1, 0]], [15, 0, 3, [0, "44E3+4yCFJk7TELTB6pM5n"], [4, 4278245119]]], 0, [0, 3, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 4, 6, 0, 1, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, -4, 5, 0, 1, 2, 0, 1, 2, 0, 1, 3, 0, -2, 6, 0, 1, 4, 0, 1, 4, 0, 1, 5, 0, 1, 5, 0, 5, 1, 19], [0, 0, 6, 6, 6], [2, 2, 6, 2, 7], [1, 2, 3, 4, 5]], [[{"name": "tiles", "spriteFrames": ["10", "6bb9b7d4-7e62-4501-a638-ba268f838f00@d6710", "11", "6bb9b7d4-7e62-4501-a638-ba268f838f00@6422a", "12", "6bb9b7d4-7e62-4501-a638-ba268f838f00@cdab0", "13", "6bb9b7d4-7e62-4501-a638-ba268f838f00@c10f9", "14", "6bb9b7d4-7e62-4501-a638-ba268f838f00@a8656", "15", "6bb9b7d4-7e62-4501-a638-ba268f838f00@979f3", "00", "6bb9b7d4-7e62-4501-a638-ba268f838f00@bb1a3", "01", "6bb9b7d4-7e62-4501-a638-ba268f838f00@93043", "02", "6bb9b7d4-7e62-4501-a638-ba268f838f00@a6b9d", "03", "6bb9b7d4-7e62-4501-a638-ba268f838f00@ecffe", "04", "6bb9b7d4-7e62-4501-a638-ba268f838f00@74ef1", "05", "6bb9b7d4-7e62-4501-a638-ba268f838f00@7d214", "06", "6bb9b7d4-7e62-4501-a638-ba268f838f00@fea17", "07", "6bb9b7d4-7e62-4501-a638-ba268f838f00@d7755", "08", "6bb9b7d4-7e62-4501-a638-ba268f838f00@fe069", "09", "6bb9b7d4-7e62-4501-a638-ba268f838f00@0f466"]}], [5], 0, [], [], []], [[{"name": "09", "rect": {"x": 32, "y": 64, "width": 32, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "11", "rect": {"x": 96, "y": 64, "width": 32, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "04", "rect": {"x": 0, "y": 32, "width": 32, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "05", "rect": {"x": 32, "y": 32, "width": 32, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "01", "rect": {"x": 32, "y": 0, "width": 32, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "15", "rect": {"x": 96, "y": 96, "width": 32, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "02", "rect": {"x": 64, "y": 0, "width": 32, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "14", "rect": {"x": 64, "y": 96, "width": 32, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "00", "rect": {"x": 0, "y": 0, "width": 32, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "13", "rect": {"x": 32, "y": 96, "width": 32, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "12", "rect": {"x": 0, "y": 96, "width": 32, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "10", "rect": {"x": 64, "y": 64, "width": 32, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "07", "rect": {"x": 96, "y": 32, "width": 32, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "03", "rect": {"x": 96, "y": 0, "width": 32, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "08", "rect": {"x": 0, "y": 64, "width": 32, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "06", "rect": {"x": 64, "y": 32, "width": 32, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[[17, "billiards", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{}]]], 0, 0, [0], [8], [6]], [[[18, "../resources/shader/billiards", [{"hash": 1930775768, "name": "../resources/shader/billiards|sprite-vs:vert|sprite-fs:frag", "blocks": [{"name": "Constant", "stageFlags": 16, "binding": 0, "members": [{"name": "b_matrix", "type": 25, "count": 1}, {"name": "light_pos", "type": 16, "count": 1}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 1, "defines": []}, {"name": "a_color", "format": 44, "location": 2, "defines": []}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCLocal", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["USE_LOCAL"]}], "samplerTextures": [{"name": "cc_spriteTexture", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constant", "stageFlags": 16, "binding": 0, "members": [{"name": "b_matrix", "type": 25, "count": 1}, {"name": "light_pos", "type": 16, "count": 1}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl4": {"vert": "\nprecision highp float;\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#if USE_LOCAL\n  layout(set = 2, binding = 0) uniform CCLocal {\n    highp mat4 cc_matWorld;\n    highp mat4 cc_matWorldIT;\n    highp vec4 cc_lightingMapUVParam;\n    highp vec4 cc_localShadowBias;\n    highp vec4 cc_reflectionProbeData1;\n    highp vec4 cc_reflectionProbeData2;\n    highp vec4 cc_reflectionProbeBlendData1;\n    highp vec4 cc_reflectionProbeBlendData2;\n  };\n#endif\n#if SAMPLE_FROM_RT\n  #define QUATER_PI         0.78539816340\n  #define HALF_PI           1.57079632679\n  #define PI                3.14159265359\n  #define PI2               6.28318530718\n  #define PI4               12.5663706144\n  #define INV_QUATER_PI     1.27323954474\n  #define INV_HALF_PI       0.63661977237\n  #define INV_PI            0.31830988618\n  #define INV_PI2           0.15915494309\n  #define INV_PI4           0.07957747155\n  #define EPSILON           1e-6\n  #define EPSILON_LOWP      1e-4\n  #define LOG2              1.442695\n  #define EXP_VALUE         2.71828183\n  #define FP_MAX            65504.0\n  #define FP_SCALE          0.0009765625\n  #define FP_SCALE_INV      1024.0\n  #define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n      #define LIGHT_MAP_TYPE_DISABLED 0\n  #define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n  #define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n  #define REFLECTION_PROBE_TYPE_NONE 0\n  #define REFLECTION_PROBE_TYPE_CUBE 1\n  #define REFLECTION_PROBE_TYPE_PLANAR 2\n  #define REFLECTION_PROBE_TYPE_BLEND 3\n  #define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n      #define LIGHT_TYPE_DIRECTIONAL 0.0\n  #define LIGHT_TYPE_SPHERE 1.0\n  #define LIGHT_TYPE_SPOT 2.0\n  #define LIGHT_TYPE_POINT 3.0\n  #define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n  #define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n  #define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n  #define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n  #define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n  #define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n  #define TONE_MAPPING_ACES 0\n  #define TONE_MAPPING_LINEAR 1\n  #define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n  #ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n    #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n  #endif\n  #ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n    #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n  #endif\n#endif\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec2 a_texCoord;\nlayout(location = 2) in vec4 a_color;\nlayout(location = 0) out vec4 color;\nlayout(location = 1) out vec2 uv0;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  #if USE_LOCAL\n    pos = cc_matWorld * pos;\n  #endif\n  #if USE_PIXEL_ALIGNMENT\n    pos = cc_matView * pos;\n    pos.xyz = floor(pos.xyz);\n    pos = cc_matProj * pos;\n  #else\n    pos = cc_matViewProj * pos;\n  #endif\n  uv0 = a_texCoord;\n  #if SAMPLE_FROM_RT\n    uv0 = cc_cameraPos.w > 1.0 ? vec2(uv0.x, 1.0 - uv0.y) : uv0;\n  #endif\n  color = a_color;\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 CCSampleWithAlphaSeparated(sampler2D tex, vec2 uv) {\n#if CC_USE_EMBEDDED_ALPHA\n  return vec4(texture(tex, uv).rgb, texture(tex, uv + vec2(0.0, 0.5)).r);\n#else\n  return texture(tex, uv);\n#endif\n}\nlayout(location = 0) in vec4 color;\nlayout(location = 1) in vec2 uv0;\nlayout(set = 1, binding = 0) uniform Constant {\n  mat4x4 b_matrix;\n  vec4 light_pos;\n};\nlayout(set = 2, binding = 12) uniform sampler2D cc_spriteTexture;\nvec4 frag () {\n  vec2 uv = fract(uv0 * 4.0);\n  vec2 id = floor(uv0 * 4.0);\n  vec2 xy = uv * 2.0 - 1.0;\n  float z = sqrt(1.0 - length(xy));\n  vec3 _p3d = vec3(xy, z);\n  vec4 p = b_matrix * vec4(_p3d, 1.0);\n  vec3 p3d = p.xyz;\n  vec4 background = vec4(0.0, 0.0, 0.0, 0.0);\n  vec4 ballColor = color;\n  #if USE_BELT\n      vec3 white1 = vec3(0.0, 1.0, 0.0);\n      vec3 white2 = vec3(0.0, -1.0, 0.0);\n      float whiteLen = 0.7;\n      vec3 dw1 = white1 - p3d;\n      vec3 dw2 = white2 - p3d;\n      float dw = min(dot(dw1, dw1), dot(dw2, dw2));\n      ballColor = mix(ballColor, vec4(1.0, 1.0, 1.0, 1.0), smoothstep(0.0, -0.1, dw - whiteLen));\n  #endif\n  vec2 _xy = p3d.xy * 0.8 + 0.5;\n  vec2 _uv = _xy * 0.25 + id * 0.25;\n  vec4 numColor = CCSampleWithAlphaSeparated(cc_spriteTexture, _uv);\n  vec4 o = mix(ballColor, numColor, smoothstep(0., -0.1, length(_xy - 0.5) - 0.48) * step(0.0, p3d.z));\n  o = mix(background, o, smoothstep(0.0, -0.1, length(xy) - 1.0));\n  #if USE_LIGHT\n    vec3 lightDir = normalize(light_pos.xyz - _p3d.xyz);\n    float d = dot(normalize(_p3d.xyz), lightDir);\n    d = clamp(d, 0.0, 1.0);\n    d = sqrt(d);\n    float nol = 0.5 + 0.5 * d;\n    o *= vec4(nol, nol, nol, 1.0);\n  #endif\n  return o;\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": ["USE_LOCAL"]}], "samplerTextures": [{"name": "cc_spriteTexture", "defines": []}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 56, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 5}}, "defines": [{"name": "USE_LOCAL", "type": "boolean"}, {"name": "SAMPLE_FROM_RT", "type": "boolean"}, {"name": "USE_PIXEL_ALIGNMENT", "type": "boolean"}, {"name": "CC_USE_EMBEDDED_ALPHA", "type": "boolean"}, {"name": "USE_BELT", "type": "boolean"}, {"name": "USE_LIGHT", "type": "boolean"}]}], [{"passes": [{"program": "../resources/shader/billiards|sprite-vs:vert|sprite-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}, "properties": {"b_matrix": {"type": 25, "value": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0]}, "light_pos": {"type": 16, "value": [0.5, -0.5, 1, 1]}}}]}]]], 0, 0, [], [], []]]]