[1, ["fd0c36d8-11b4-4071-9acf-20a783b46e64", "a34176fc-e7d3-4aeb-96e1-49e23e127c69@f9941", "1f6bfd4b-37c2-4bdd-b4fc-bb065b238d2d@f9941", "0573bc9e-d56d-4da0-9203-3d7f4b30bd4a@f9941", "ddb451b7-f31c-4c32-be0d-6a8b5bbdff4c@f9941", "39e1240d-8073-41c2-847c-b87a3885106c", "b8d35b12-33df-4232-9665-8836f602a074"], ["node", "_spriteFrame", "_font", "root", "lbName", "spAvatar", "data", "_customMaterial", "_effectAsset"], [["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_lpos", "_children", "_lscale"], 0, 9, 4, 1, 5, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "_fillType", "_fillStart", "_isTrimmedMode", "node", "__prefab", "_spriteFrame", "_fillCenter"], -2, 1, 4, 6, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos"], 1, 1, 12, 4, 5], ["cc.Widget", ["_alignFlags", "_enabled", "_right", "node", "__prefab"], 0, 1, 4], ["cc.Label", ["_string", "_actualFontSize", "_enableWrapText", "_isSystemFontUsed", "_horizontalAlign", "_fontSize", "_overflow", "node", "__prefab", "_font"], -4, 1, 4, 6], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["de413E/7KdKG57LfQhVHaFr", ["dirV", "node", "__prefab"], 2, 1, 4], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["97323FPHLdFhLcK/XCsmO6e", ["nameFormatLength", "node", "__prefab", "spAvatar", "lbName"], 2, 1, 4, 1, 1], ["6e0cdvfdK1AHqEHnYZNdwhO", ["node", "__prefab"], 3, 1, 4], ["cc.Material", ["_name", "_props", "_states", "_defines"], -1], ["cc.EffectAsset", ["_name", "shaders", "techniques"], 0]], [[7, 0, 2], [9, 0, 1, 2, 3, 4, 5, 5], [2, 0, 1, 2, 1], [0, 0, 2, 1, 5, 3, 4, 4], [0, 0, 1, 5, 7, 3, 4, 3], [0, 0, 1, 5, 3, 4, 6, 3], [0, 0, 2, 1, 5, 3, 4, 6, 8, 4], [2, 0, 1, 1], [2, 0, 1, 2, 3, 1], [1, 5, 6, 7, 1], [1, 5, 6, 1], [6, 0, 2], [0, 0, 1, 7, 3, 4, 6, 3], [0, 0, 1, 5, 3, 4, 3], [3, 0, 1, 2, 3, 4, 3], [3, 0, 1, 2, 3, 4, 5, 3], [4, 1, 0, 2, 3, 4, 4], [4, 0, 3, 4, 2], [8, 0, 1, 2, 2], [10, 0, 1, 2, 3, 4, 2], [1, 0, 5, 6, 7, 2], [1, 1, 2, 0, 3, 5, 6, 8, 7, 5], [1, 0, 4, 5, 6, 3], [11, 0, 1, 1], [5, 0, 1, 2, 3, 7, 8, 9, 5], [5, 0, 4, 1, 5, 6, 2, 3, 7, 8, 8], [12, 0, 1, 2, 3, 5], [13, 0, 1, 2, 4]], [[[[11, "playerItem1"], [12, "playerItem1", 33554432, [-5, -6, -7, -8], [[7, -2, [0, "13Ug9d0YxLIYjKVbPc6IIF"]], [16, false, 32, 3, -3, [0, "3be5ZbvLVJgpNaL025V6WM"]], [18, -1, -4, [0, "4cDWHjmXpGI5wJ1lH6D7Fl"]]], [1, "d7zibWydJBhLmgKsX087Zi", null, null, null, -1, 0], [1, 322, 0, 0]], [4, "headNode", 33554432, 1, [-11, -12, -13, -14, -15, -16, -17], [[7, -9, [0, "d6yFBUWRlFjaI140lcU/1g"]], [17, 32, -10, [0, "93G9qxYypDp53oX+GEc0Ng"]]], [1, "32yoJSHn1EoIPxTpK1nyVv", null, null, null, 1, 0]], [4, "profile", 33554432, 2, [-22], [[2, -18, [0, "b9lje1e4RG4JjoZqgRurtK"], [5, 72, 72]], [19, 13, -21, [0, "56M63OMs1OAJ0EAt95WIhC"], -20, -19]], [1, "d1RT9JtQZMgrMYNzjT2I9E", null, null, null, 1, 0]], [5, "playerBg", 33554432, 1, [[2, -23, [0, "55G3iKWEBClooOG2bSmzIc"], [5, 372, 92]], [9, -24, [0, "3dckV0UlBIhKA+BRSSgMR1"], 0]], [1, "d1xX8ehOFLIbM7HzOAE2Qp", null, null, null, 1, 0], [1, -133.724, 0, 0]], [5, "objBallNode", 33554432, 1, [[8, -25, [0, "c7Se8OAnRP8KL3s1fTzAso"], [5, 260, 50], [0, 1, 0.5]], [23, -26, [0, "32Onh75CVHmobiaHf8O+eZ"]]], [1, "42aNpDW6xPBZ4fJfSYX/pE", null, null, null, 1, 0], [1, -60, -22, 0]], [3, "bg", false, 33554432, 2, [[2, -27, [0, "1cLFpxtABEhJR38HWzsudI"], [5, 78, 78]], [20, 0, -28, [0, "abMm3Tk2xAzLapCXTUNGmW"], 1]], [1, "08sP94nWZHt7fKKZ872kPq", null, null, null, 1, 0]], [3, "changeBg", false, 33554432, 2, [[2, -29, [0, "6bLUXOjtJHSJvCZfkwmw6A"], [5, 80, 80]], [9, -30, [0, "82J7ZhOzNN/JDf0SiunDVz"], 2]], [1, "26haFgpUZC45ClvMG6hTQp", null, null, null, 1, 0]], [14, "spAvatar", 33554432, 3, [[[2, -31, [0, "4fB9NnTxdPOboR40L4bV2+"], [5, 72, 72]], -32], 4, 1], [1, "4bFHyrQbtLFIEauvszpSDV", null, null, null, 1, 0]], [15, "lblName", 33554432, 1, [[[8, -33, [0, "8bNEduTtVPVakmBzAd1a+U"], [5, 240, 40], [0, 1, 0.5]], -34], 4, 1], [1, "1fsLjtkuhNb7mUbeedmNc6", null, null, null, 1, 0], [1, -46, 16, 0]], [3, "headAlphaBg", false, 33554432, 2, [[2, -35, [0, "e1pHc54RtO6KMV36aD/Q4K"], [5, 72, 72]], [21, 3, 2, 0, 0.25, -36, [0, "ef4TN6S3VNPK4No34EqY7P"], [0, 0.5, 0.5], 3]], [1, "5f4qkw/cxHfIfrTUr2tMRR", null, null, null, 1, 0]], [6, "img_count0", false, 33554432, 2, [[2, -37, [0, "cbvHwfMHVJ9ay0TAOTTXYQ"], [5, 20, 37]], [10, -38, [0, "66xUfGlpZE575165mViB2p"]]], [1, "95pzsS/phBzJaE2XX2d+eC", null, null, null, 1, 0], [1, -12.948, 0, 0], [1, 0.9, 0.9, 1]], [6, "img_count1", false, 33554432, 2, [[2, -39, [0, "f955Dcu89PmInNd+LW5poA"], [5, 20, 37]], [10, -40, [0, "72aUs1NypGALydBUz5PltG"]]], [1, "9c8+6XYp9M85o6fsFKgPKm", null, null, null, 1, 0], [1, 14.648, 0, 0], [1, 0.9, 0.9, 1]], [13, "cdTimeLabel", 33554432, 2, [[2, -41, [0, "feqpN3YThEoIneFGW45Ae+"], [5, 0, 50.4]], [24, "", 40, false, false, -42, [0, "92iKZRlWNA0ZvDaO6USjFk"], 4]], [1, "d1xIXdGsJP9Ljd4/pU8pnh", null, null, null, 1, 0]], [22, 0, false, 8, [0, "f0ppycCQ5GXJjju/9BxS7/"]], [25, "Name", 2, 24, 24, 1, false, false, 9, [0, "1b6hivapRGf7Qmhz4YeCdg"]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, -2, 5, 0, -3, 2, 0, -4, 9, 0, 0, 2, 0, 0, 2, 0, -1, 6, 0, -2, 7, 0, -3, 3, 0, -4, 10, 0, -5, 11, 0, -6, 12, 0, -7, 13, 0, 0, 3, 0, 4, 15, 0, 5, 14, 0, 0, 3, 0, -1, 8, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -2, 14, 0, 0, 9, 0, -2, 15, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 6, 1, 42], [0, 0, 0, 0, 0, 14, 15], [1, 1, 1, 1, 2, 7, 2], [1, 2, 3, 4, 0, 5, 0]], [[[26, "CircleAvatar", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_TEXTURE": true}]]], 0, 0, [0], [8], [6]], [[[27, "../resources/shader/CircleAvatar", [{"hash": 143270750, "name": "../resources/shader/CircleAvatar|sprite-vs:vert|sprite-fs:frag", "blocks": [{"name": "ALPHA_TEST_DATA", "stageFlags": 16, "binding": 0, "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"]}, {"name": "ARGS", "stageFlags": 16, "binding": 1, "members": [{"name": "radius", "type": 13, "count": 1}, {"name": "blur", "type": 13, "count": 1}, {"name": "center", "type": 14, "count": 1}, {"name": "wh_ratio", "type": 13, "count": 1}, {"name": "grayscale", "type": 13, "count": 1}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 1, "defines": []}, {"name": "a_color", "format": 44, "location": 2, "defines": []}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCLocal", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["USE_LOCAL"]}], "samplerTextures": [{"name": "cc_spriteTexture", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": ["USE_TEXTURE"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "ALPHA_TEST_DATA", "stageFlags": 16, "binding": 0, "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"]}, {"name": "ARGS", "stageFlags": 16, "binding": 1, "members": [{"name": "radius", "type": 13, "count": 1}, {"name": "blur", "type": 13, "count": 1}, {"name": "center", "type": 14, "count": 1}, {"name": "wh_ratio", "type": 13, "count": 1}, {"name": "grayscale", "type": 13, "count": 1}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl4": {"vert": "\nprecision highp float;\nlayout(set = 0, binding = 0) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(set = 0, binding = 1) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#if USE_LOCAL\n  layout(set = 2, binding = 0) uniform CCLocal {\n    highp mat4 cc_matWorld;\n    highp mat4 cc_matWorldIT;\n    highp vec4 cc_lightingMapUVParam;\n    highp vec4 cc_localShadowBias;\n    highp vec4 cc_reflectionProbeData1;\n    highp vec4 cc_reflectionProbeData2;\n    highp vec4 cc_reflectionProbeBlendData1;\n    highp vec4 cc_reflectionProbeBlendData2;\n  };\n#endif\nlayout(location = 0) in vec3 a_position;\nlayout(location = 1) in vec2 a_texCoord;\nlayout(location = 2) in vec4 a_color;\nlayout(location = 0) out vec4 v_color;\nlayout(location = 1) out vec2 v_uv0;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  #if USE_LOCAL\n    pos = cc_matWorld * pos;\n  #endif\n  #if USE_PIXEL_ALIGNMENT\n    pos = cc_matView * pos;\n    pos.xyz = floor(pos.xyz);\n    pos = cc_matProj * pos;\n  #else\n    pos = cc_matViewProj * pos;\n  #endif\n  v_color = a_color;\n  v_uv0 = a_texCoord;\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 CCSampleWithAlphaSeparated(sampler2D tex, vec2 uv) {\n#if CC_USE_EMBEDDED_ALPHA\n  return vec4(texture(tex, uv).rgb, texture(tex, uv + vec2(0.0, 0.5)).r);\n#else\n  return texture(tex, uv);\n#endif\n}\n#if USE_ALPHA_TEST\n  layout(set = 1, binding = 0) uniform ALPHA_TEST_DATA {\n    float alphaThreshold;\n  };\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n    if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n    if (alpha < alphaThreshold) discard;\n  #endif\n}\nlayout(location = 0) in vec4 v_color;\n#if USE_TEXTURE\n  layout(location = 1) in vec2 v_uv0;\n  layout(set = 2, binding = 12) uniform sampler2D cc_spriteTexture;\n#endif\nlayout(set = 1, binding = 1) uniform ARGS{\n  float radius;\n  float blur;\n  vec2 center;\n  float wh_ratio;\n  float grayscale;\n};\nvec4 frag () {\n  vec4 o = vec4(1, 1, 1, 1);\n  vec4 textureColor = CCSampleWithAlphaSeparated(cc_spriteTexture, v_uv0);\n  o *= textureColor;\n  o *= v_color;\n  ALPHA_TEST(o);\n  if (grayscale > 0.5) {\n    float gray = dot(vec3(0.299, 0.587, 0.114), o.rgb);\n    o.rgb = vec3(gray);\n  }\n  float circle = radius * radius;\n  float rx = center.x * wh_ratio;\n  float ry = center.y;\n  float dis = (v_uv0.x * wh_ratio - rx) * (v_uv0.x * wh_ratio - rx) + (v_uv0.y  - ry) * (v_uv0.y - ry);\n  o.a = smoothstep(circle, circle - blur, dis) * o.a;\n  return o;\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": ["USE_LOCAL"]}], "samplerTextures": [{"name": "cc_spriteTexture", "defines": ["USE_TEXTURE"]}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 56, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 6}}, "defines": [{"name": "USE_LOCAL", "type": "boolean"}, {"name": "USE_PIXEL_ALIGNMENT", "type": "boolean"}, {"name": "CC_USE_EMBEDDED_ALPHA", "type": "boolean"}, {"name": "USE_ALPHA_TEST", "type": "boolean"}, {"name": "USE_TEXTURE", "type": "boolean"}]}], [{"passes": [{"program": "../resources/shader/CircleAvatar|sprite-vs:vert|sprite-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}, "properties": {"alphaThreshold": {"type": 13, "value": [0.5]}, "wh_ratio": {"type": 13, "value": [1]}, "blur": {"type": 13, "value": [0.01]}, "radius": {"type": 13, "value": [0.5]}, "center": {"type": 14, "value": [0.5, 0.5]}, "grayscale": {"type": 13, "value": [0]}}}]}]]], 0, 0, [], [], []]]]