[1, ["fd0c36d8-11b4-4071-9acf-20a783b46e64", "bd1bcaba-bd7d-4a71-b143-997c882383e4@f9941", "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "b59fa3d0-66e1-4088-a85b-231b1675f08c@f9941", "c82d4ea1-0bf3-4667-a8c0-09091a65594f@f9941", "a1f8ddf1-b7db-48c2-9260-d11368c6df48@f9941", "20835ba4-6145-4fbc-a58a-051ce700aa3e@6c48a", "557dc3db-51d2-4ffc-8616-982d7566d797@6c48a", "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@6c48a", "e706adc5-34f6-49c2-bdea-dc96fbe04083", "951249e0-9f16-456d-8b85-a6ca954da16b@6c48a", "9cf11a8a-1769-480b-bfa5-12d3f6c8f8c0@6c48a", "bd1bcaba-bd7d-4a71-b143-997c882383e4@6c48a", "f3d7a3cc-9c41-4861-943f-d4e6f6507c75@6c48a", "99a650fd-cec4-482c-9f04-b87bc44d4d58@f9941", "b13356c4-ea40-4731-9a7d-44fbe4ad348e@f9941", "3f43ebcd-f512-4a4a-b0b2-043f38b60f72@f9941", "f91dda88-8315-4d12-b306-f6d423782a98@f9941", "9cf11a8a-1769-480b-bfa5-12d3f6c8f8c0@f9941", "ea1da508-64b7-4544-80a9-3cb1abeef8cf@f9941", "c313bb0a-ef52-4fcd-b557-9f1b7e2ade24@f9941", "98be28a6-9661-4905-a4d0-053bc944d93d@f9941", "b81feaa9-842a-4176-92c7-92b9da6d1a7a@f9941", "cd9e02ff-e9b5-4a66-8ee6-2106a4591bce@f9941", "71b81800-9630-4229-9447-92e9e2a9abe9@f9941", "aacbec2e-c79f-4b43-8d76-82a92adab6d5@f9941", "9ca8dcb7-d263-49f5-b285-ef9c0b7a5ccc@f9941", "ec205bb2-9f8c-4486-a3d7-58b2fdb24109@f9941", "19675c19-6bd8-4f2e-918c-c4d31dd8a6e5@f9941", "4e7ead1f-a22a-4a0e-9e17-8b83ab0f0712", "f3d7a3cc-9c41-4861-943f-d4e6f6507c75@f9941", "ffcc534a-fa56-489e-bd65-bf4116f79a3f@f9941", "4b6633e0-b4a5-4ceb-ab5d-df0cebe73897", "80f32270-ec66-4a10-a211-5a402d8d283f@f9941", "4646441b-9911-4f34-aead-207321f53af4@f9941", "9c1cc9e2-9ce1-4bdf-91a3-0796706ef653@f9941", "5346414f-374b-4c0d-a916-4eee0f7afad8@f9941", "4d1696cd-8048-4bac-8086-3671387df56b", "a37d9494-6494-4d8c-ae84-6979d3ea01be", "297a1fad-d713-4d13-84be-02bf64c92e35", "9800039e-478e-4818-8228-129c713b3a14", "71350878-a712-4da3-982c-119b2afbb5a5", "6bb9b7d4-7e62-4501-a638-ba268f838f00@bb1a3", "6bb9b7d4-7e62-4501-a638-ba268f838f00@93043", "6bb9b7d4-7e62-4501-a638-ba268f838f00@a6b9d", "6bb9b7d4-7e62-4501-a638-ba268f838f00@ecffe", "6bb9b7d4-7e62-4501-a638-ba268f838f00@74ef1", "6bb9b7d4-7e62-4501-a638-ba268f838f00@7d214", "6bb9b7d4-7e62-4501-a638-ba268f838f00@fea17", "6bb9b7d4-7e62-4501-a638-ba268f838f00@d7755", "6bb9b7d4-7e62-4501-a638-ba268f838f00@fe069", "6bb9b7d4-7e62-4501-a638-ba268f838f00@0f466", "6bb9b7d4-7e62-4501-a638-ba268f838f00@d6710", "6bb9b7d4-7e62-4501-a638-ba268f838f00@6422a", "6bb9b7d4-7e62-4501-a638-ba268f838f00@cdab0", "6bb9b7d4-7e62-4501-a638-ba268f838f00@c10f9", "6bb9b7d4-7e62-4501-a638-ba268f838f00@a8656", "6bb9b7d4-7e62-4501-a638-ba268f838f00@979f3"], ["node", "_spriteFrame", "_font", "_backgroundImage", "_textureSource", "_target", "_parent", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite", "_effectAsset", "root", "test<PERSON><PERSON><PERSON>", "txtWinGold", "sliderbar", "table", "cue", "tableLinearDampEdit", "frictionEdit", "restitutionEdit", "linearDampEdit", "powerRange", "maxPowerEdit", "minPowerEdit", "_placeholder<PERSON><PERSON><PERSON>", "_textLabel", "data", "_customMaterial", "_skeletonData", "pfBall", "b1", "b2"], [["cc.Node", ["_name", "_layer", "_active", "_obj<PERSON><PERSON>s", "_components", "_prefab", "_parent", "_lpos", "_children"], -1, 9, 4, 1, 5, 2], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isSystemFontUsed", "_overflow", "_horizontalAlign", "_enableWrapText", "_lineHeight", "_isBold", "_verticalAlign", "node", "__prefab", "_font", "_color"], -7, 1, 4, 6, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_left", "_right", "_originalHeight", "_verticalCenter", "_alignMode", "node", "__prefab"], -4, 1, 4], ["cc.Sprite", ["_sizeMode", "_type", "_fillType", "_fillStart", "_fillRange", "node", "__prefab", "_spriteFrame", "_customMaterial"], -2, 1, 4, 6, 6], "cc.SpriteFrame", ["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_lpos", "_children", "_parent"], 0, 12, 4, 5, 2, 1], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.PolygonCollider2D", ["_group", "_friction", "_restitution", "tag", "node", "__prefab", "_points", "_offset"], -1, 1, 4, 12, 5], ["cc.Mask", ["_type", "_alphaThreshold", "node", "__prefab"], 1, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "clickEvents", "_target", "_normalColor", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite"], 2, 1, 4, 9, 1, 5, 6, 6, 6, 6], ["cc.EditBox", ["_maxLength", "_inputMode", "node", "__prefab", "_textLabel", "_placeholder<PERSON><PERSON><PERSON>", "_backgroundImage"], 1, 1, 4, 1, 1, 6], ["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3], ["cc.Material", ["_name", "_props", "_states", "_defines"], -1], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["38f0e6Dj2RNGKg7QovKytkK", ["node", "__prefab", "playerPrefabs", "cue", "table", "sliderbar", "txtWinGold", "textures", "test<PERSON><PERSON><PERSON>", "pfBall", "b1", "b2"], 3, 1, 4, 3, 1, 1, 1, 1, 3, 1, 6, 6, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.RigidBody2D", ["enabledContactListener", "awakeOnLoad", "_type", "_linearDamping", "node", "__prefab"], -1, 1, 4], ["27bbdYLsvZKsLpWyOKbnUd9", ["node", "__prefab", "minPowerEdit", "maxPowerEdit", "powerRange", "linearDampEdit", "restitutionEdit", "frictionEdit", "tableLinearDampEdit"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1], ["0e249Csn/5O1ZXK7lr2zy24", ["node", "__prefab"], 3, 1, 4], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.UIOpacity", ["node", "__prefab"], 3, 1, 4], ["adc10Jcl0JBIqKggIVkilUH", ["node", "__prefab"], 3, 1, 4], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["edf98QH2D1AUoCrtQlbxFrU", ["key", "node", "__prefab"], 2, 1, 4], ["sp.Skeleton", ["defaultSkin", "_premultipliedAlpha", "_preCacheMode", "loop", "node", "__prefab", "_skeletonData"], -1, 1, 4, 6], ["6b32dVI2p9OLaXSJZMy7uhG", ["node", "__prefab"], 3, 1, 4], ["35249I9WChLf5HdmIvwbtpm", ["node", "__prefab"], 3, 1, 4], ["12b15H4/apPz5m9UvQKUFXV", ["node", "__prefab"], 3, 1, 4]], [[14, 0, 2], [16, 0, 1, 2, 3, 4, 5, 5], [6, 0, 1, 2, 1], [6, 0, 1, 2, 3, 1], [0, 0, 1, 6, 4, 5, 7, 3], [3, 5, 6, 7, 1], [3, 1, 0, 5, 6, 7, 3], [5, 0, 1, 7, 3, 4, 5, 3], [5, 0, 2, 1, 7, 3, 4, 5, 4], [3, 0, 5, 6, 7, 2], [5, 0, 1, 7, 6, 3, 4, 5, 3], [10, 1, 0, 2, 3, 4, 5, 3], [1, 0, 5, 1, 2, 4, 6, 3, 10, 11, 8], [1, 0, 5, 1, 2, 4, 6, 3, 10, 11, 13, 8], [0, 0, 1, 6, 8, 4, 5, 7, 3], [0, 0, 1, 6, 4, 5, 3], [7, 0, 1, 2, 4, 5, 7, 6, 4], [7, 3, 0, 1, 4, 5, 6, 4], [1, 0, 5, 1, 2, 7, 3, 8, 10, 11, 12, 8], [0, 0, 2, 1, 6, 8, 4, 5, 7, 4], [24, 0, 1, 2, 3], [0, 0, 3, 1, 6, 4, 5, 4], [22, 0, 1, 1], [9, 0, 1, 2, 3, 5, 4, 6, 7, 8, 9, 2], [1, 0, 1, 2, 4, 6, 3, 10, 11, 13, 12, 7], [0, 0, 1, 8, 4, 5, 3], [0, 0, 2, 1, 6, 4, 5, 4], [0, 0, 2, 1, 6, 4, 5, 7, 4], [2, 0, 2, 3, 5, 1, 7, 8, 6], [2, 0, 3, 7, 8, 3], [3, 0, 5, 6, 2], [20, 0, 1, 1], [11, 0, 1, 2, 3, 4, 5], [12, 0, 1, 2, 3, 5], [13, 0, 2], [0, 0, 1, 4, 5, 7, 3], [0, 0, 1, 6, 8, 4, 5, 3], [5, 0, 1, 6, 3, 4, 3], [5, 0, 1, 6, 3, 4, 5, 3], [6, 0, 1, 1], [2, 0, 1, 4, 7, 8, 4], [2, 0, 2, 3, 1, 7, 8, 5], [2, 0, 2, 7, 8, 3], [2, 0, 1, 4, 6, 7, 8, 5], [2, 0, 7, 8, 2], [2, 0, 5, 1, 7, 8, 4], [15, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1], [3, 1, 5, 6, 7, 2], [3, 2, 0, 3, 4, 5, 6, 7, 5], [3, 1, 0, 5, 6, 8, 7, 3], [17, 0, 1, 2, 3, 4, 5, 5], [18, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [19, 0, 1, 1], [8, 2, 3, 1], [8, 0, 1, 2, 3, 3], [21, 0, 1, 2, 1], [23, 0, 1, 1], [9, 1, 2, 3, 4, 1], [10, 0, 2, 3, 4, 5, 6, 2], [1, 0, 1, 2, 4, 6, 3, 10, 11, 12, 7], [1, 0, 1, 2, 7, 3, 8, 10, 11, 12, 7], [1, 0, 5, 1, 2, 6, 3, 10, 11, 13, 7], [1, 0, 5, 1, 2, 7, 4, 10, 11, 7], [1, 0, 5, 9, 1, 2, 4, 3, 10, 11, 8], [1, 0, 5, 1, 2, 4, 3, 10, 11, 13, 7], [25, 0, 1, 2, 2], [26, 0, 1, 2, 3, 4, 5, 6, 5], [27, 0, 1, 1], [28, 0, 1, 1], [29, 0, 1, 1]], [[[{"name": "default_btn_normal", "rect": {"x": 0, "y": 0, "width": 40, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 40}, "rotated": false, "capInsets": [12, 12, 12, 12], "vertices": {"rawPosition": [-20, -20, 0, 20, -20, 0, -20, 20, 0, 20, 20, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 40, 40, 40, 0, 0, 40, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -20, "y": -20, "z": 0}, "maxPos": {"x": 20, "y": 20, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [4], [8]], [[[32, "skeleton", "\nskeleton.png\nsize: 512,128\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n183-1\n  rotate: false\n  xy: 416, 90\n  size: 37, 36\n  orig: 37, 36\n  offset: 0, 0\n  index: -1\n183-10\n  rotate: false\n  xy: 190, 68\n  size: 59, 58\n  orig: 59, 58\n  offset: 0, 0\n  index: -1\n183-11\n  rotate: false\n  xy: 190, 10\n  size: 58, 56\n  orig: 58, 56\n  offset: 0, 0\n  index: -1\n183-12\n  rotate: false\n  xy: 251, 70\n  size: 57, 56\n  orig: 57, 56\n  offset: 0, 0\n  index: -1\n183-2\n  rotate: false\n  xy: 367, 79\n  size: 47, 47\n  orig: 47, 47\n  offset: 0, 0\n  index: -1\n183-3\n  rotate: false\n  xy: 310, 71\n  size: 55, 55\n  orig: 55, 55\n  offset: 0, 0\n  index: -1\n183-4\n  rotate: false\n  xy: 128, 67\n  size: 60, 59\n  orig: 60, 59\n  offset: 0, 0\n  index: -1\n183-5\n  rotate: false\n  xy: 2, 65\n  size: 61, 61\n  orig: 61, 61\n  offset: 0, 0\n  index: -1\n183-6\n  rotate: false\n  xy: 65, 66\n  size: 61, 60\n  orig: 61, 60\n  offset: 0, 0\n  index: -1\n183-7\n  rotate: false\n  xy: 65, 5\n  size: 61, 59\n  orig: 61, 59\n  offset: 0, 0\n  index: -1\n183-8\n  rotate: false\n  xy: 128, 6\n  size: 60, 59\n  orig: 60, 59\n  offset: 0, 0\n  index: -1\n183-9\n  rotate: false\n  xy: 2, 2\n  size: 61, 61\n  orig: 61, 61\n  offset: 0, 0\n  index: -1\n", ["skeleton.png"], {"skeleton": {"hash": "y5xsDTB0vx/OsPJeERxzdl+J3eI", "spine": "3.6.53", "width": 957.68, "height": 2073.69, "images": ""}, "bones": [{"name": "root"}], "slots": [{"name": "jd", "bone": "root"}], "skins": {"default": {"jd": {"183-1": {"width": 37, "height": 36}, "183-2": {"width": 47, "height": 47}, "183-3": {"width": 55, "height": 55}, "183-4": {"width": 60, "height": 59}, "183-5": {"width": 61, "height": 61}, "183-6": {"width": 61, "height": 60}, "183-7": {"width": 61, "height": 59}, "183-8": {"width": 60, "height": 59}, "183-9": {"width": 61, "height": 61}, "183-10": {"width": 59, "height": 58}, "183-11": {"width": 58, "height": 56}, "183-12": {"width": 57, "height": 56}, "jd": {"type": "point"}}}}, "animations": {"183": {"slots": {"jd": {"attachment": [{"time": 0, "name": "183-1"}, {"time": 0.0333, "name": "183-2"}, {"time": 0.0667, "name": "183-3"}, {"time": 0.1, "name": "183-4"}, {"time": 0.1333, "name": "183-5"}, {"time": 0.1667, "name": "183-6"}, {"time": 0.2, "name": "183-7"}, {"time": 0.2333, "name": "183-8"}, {"time": 0.2667, "name": "183-9"}, {"time": 0.3, "name": "183-10"}, {"time": 0.3333, "name": "183-11"}, {"time": 0.3667, "name": "183-12"}, {"time": 0.4, "name": null}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}}}}, [0]]], 0, 0, [0], [-1], [9]], [[{"name": "default_btn_pressed", "rect": {"x": 0, "y": 0, "width": 40, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 40}, "rotated": false, "capInsets": [12, 12, 12, 12], "vertices": {"rawPosition": [-20, -20, 0, 20, -20, 0, -20, 20, 0, 20, 20, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 40, 40, 40, 0, 0, 40, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -20, "y": -20, "z": 0}, "maxPos": {"x": 20, "y": 20, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [4], [10]], [[[33, "billiards2", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_BELT": true}]]], 0, 0, [0], [11], [11]], [[{"name": "default_btn_disabled", "rect": {"x": 0, "y": 0, "width": 40, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 40}, "rotated": false, "capInsets": [12, 12, 12, 12], "vertices": {"rawPosition": [-20, -20, 0, 20, -20, 0, -20, 20, 0, 20, 20, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 40, 40, 40, 0, 0, 40, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -20, "y": -20, "z": 0}, "maxPos": {"x": 20, "y": 20, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [4], [12]], [[{"name": "img_tableBg", "rect": {"x": 0, "y": 0, "width": 608, "height": 1075}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 608, "height": 1075}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-304, -537.5, 0, 304, -537.5, 0, -304, 537.5, 0, 304, 537.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1075, 608, 1075, 0, 0, 608, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -304, "y": -537.5, "z": 0}, "maxPos": {"x": 304, "y": 537.5, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [4], [13]], [[{"name": "default_editbox_bg", "rect": {"x": 0, "y": 0, "width": 40, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 40}, "rotated": false, "capInsets": [12, 12, 12, 12], "vertices": {"rawPosition": [-20, -20, 0, 20, -20, 0, -20, 20, 0, 20, 20, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 40, 40, 40, 0, 0, 40, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -20, "y": -20, "z": 0}, "maxPos": {"x": 20, "y": 20, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [4], [14]], [[{"name": "img_fineTurning", "rect": {"x": 0, "y": 0, "width": 29, "height": 242}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 29, "height": 242}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-14.5, -121, 0, 14.5, -121, 0, -14.5, 121, 0, 14.5, 121, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 242, 29, 242, 0, 0, 29, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -14.5, "y": -121, "z": 0}, "maxPos": {"x": 14.5, "y": 121, "z": 0}}, "packable": false, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [4], [15]], [[[34, "gameUI"], [25, "gameUI", 33554432, [-10, -11, -12, -13, -14, -15, -16, -17, -18, -19], [[2, -2, [0, "09sJoyf/NFApgPW4HFZYS/"], [5, 750, 1334]], [40, 45, 750, 1334, -3, [0, "1eIPS75W9LXLNSJxhUA/fe"]], [46, -9, [0, "carC7iu+VLkrMXNzUlh/zB"], [62, 63], -8, -7, -6, -5, [66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81], -4, 61, 64, 65]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [37, "table", 33554432, [-36, -37], [[[2, -20, [0, "8faCjl3sVPYpuPHqYZlAku"], [5, 608, 1075]], [5, -21, [0, "1aqbit2xNJUIFs5cbrxKjO"], 4], [16, 8, 0, 0.8, -22, [0, "fa6jPD//5MSpugvHoe/3qN"], [0, -10.1, 26.9], [[[0, 207.6, 442], [0, 229.5, 462.5], [0, -207, 461.7], [0, -187.5, 442]], 8, 8, 8, 8]], [16, 8, 0, 0.8, -23, [0, "40gRsFOXlHGqsZq1oLGh4L"], [0, -236.79999999999998, -221.3], [[[0, -19, 243.8], [0, 1, 251.6], [0, 1, 655.4], [0, -19, 675.4]], 8, 8, 8, 8]], [16, 8, 0, 0.8, -24, [0, "ba2631/CVMhIA4nmrfWQB2"], [0, -236.8, -662], [[[0, -19, 209], [0, 1, 228.9], [0, 1, 626.1], [0, -19, 632.6]], 8, 8, 8, 8]], [16, 8, 0, 0.8, -25, [0, "e4QamsfglNXKJkh/JqQrdp"], [0, 236.8, -221.3], [[[0, -1, 251.6], [0, 19, 243.8], [0, 19, 675.4], [0, -1, 655.4]], 8, 8, 8, 8]], [16, 8, 0, 0.8, -26, [0, "b69nQ713dDwYM0ayx/6i/v"], [0, 236.8, -662], [[[0, -1, 228.9], [0, 19, 209], [0, 19, 632.6], [0, -1, 626.1]], 8, 8, 8, 8]], [16, 8, 0, 0.8, -27, [0, "3e5yrKajpPda8+EmJ25PNN"], [0, -10.1, -967], [[[0, 226.7, 478.3], [0, 207.6, 498.6], [0, -187.5, 498.2], [0, -208.1, 477.9]], 8, 8, 8, 8]], [17, 4, 4, 0, -28, [0, "390zNsvlBKc6PQhbC703dK"], [[[0, 253.8, 23.6], [0, 257.3, 15.9], [0, 256.1, 4.9], [0, 256, -9.6], [0, 257.8, -19.5], [0, 256.1, -29], [0, 334.8, 7.6]], 8, 8, 8, 8, 8, 8, 8]], [17, 5, 4, 0, -29, [0, "4d2CsOkkBGGIQ1m2Zbfc6t"], [[[0, 216.9, 489.1], [0, 227.3, 486.6], [0, 233.7, 480.2], [0, 240.7, 473], [0, 249.6, 466.8], [0, 255, 451.3], [0, 316.2, 491.3], [0, 230, 526.6]], 8, 8, 8, 8, 8, 8, 8, 8]], [17, 3, 4, 0, -30, [0, "2eQccYvUhOpbhuY30gMRVQ"], [[[0, 216.2, -486.9], [0, 228.5, -482.3], [0, 238, -470.4], [0, 251.2, -464.9], [0, 254.8, -452.3], [0, 302, -485.7], [0, 236.4, -526]], 8, 8, 8, 8, 8, 8, 8]], [17, 6, 4, 0, -31, [0, "abLcvtmYhLKIWo67YwEU/z"], [[[0, -217.2, 488.9], [0, -229.3, 486.3], [0, -238.8, 474.8], [0, -251.2, 467.8], [0, -255.6, 452.8], [0, -319.9, 521.5], [0, -230, 526.6]], 8, 8, 8, 8, 8, 8, 8]], [17, 1, 4, 0, -32, [0, "c8juKs24FCe5fcplzWFE0h"], [[[0, -255.8, 23.8], [0, -254.5, 11.3], [0, -251.4, -2.5], [0, -254, -14.9], [0, -254.4, -30.3], [0, -360.4, -7.8]], 8, 8, 8, 8, 8, 8]], [17, 2, 4, 0, -33, [0, "3d5U8pQGJPr7COlSu6DoCg"], [[[0, -218.5, -490.1], [0, -228.2, -484.4], [0, -238.3, -470.6], [0, -249.6, -464.3], [0, -254.8, -452.3], [0, -372.7, -507], [0, -230, -526.6]], 8, 8, 8, 8, 8, 8, 8]], [50, true, false, 0, 0.8, -34, [0, "fcFBVDZZZInY93U+Gf3e9R"]], -35], 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 1], [1, "58621eULhIfazhZxUQ+e0E", null, null, null, 1, 0]], [19, "TestPanel", false, 33554432, 1, [-48, -49, -50, -51, -52, -53, -54, -55, -56, -57, -58, -59, -60, -61], [[2, -38, [0, "53rFsDo5FOzqYCBMM5oRp4"], [5, 300, 360]], [9, 0, -39, [0, "d4S5yKjeNE66O3E9OooYo7"], 46], [51, -47, [0, "9b8WnLwYhNR5m0SROGBH6O"], -46, -45, -44, -43, -42, -41, -40]], [1, "32Ith4d2NIOr7AWA4UePNx", null, null, null, 1, 0], [1, 5.179, 38.697, 0]], [38, "cue", 33554432, [-66, -67, -68, -69, -70, -71], [[[3, -62, [0, "eaOBs2WYlC5YtnH/NTndlG"], [5, 44, 580], [0, 0.4, 1]], [9, 0, -63, [0, "56wow6qYpGf5/S516PTpWV"], 12], -64, [52, -65, [0, "44z/c5qRtEWJEtkRmbuGbB"]]], 4, 4, 1, 4], [1, "71gS6zjaJI6KN9t2ok7rT8", null, null, null, 1, 0], [1, 3.0190000000000055, -969.402, 0]], [14, "tableLayer", 33554432, 1, [-73, 2, -74, 4, -75, -76, -77, -78], [[39, -72, [0, "e1s1yTIgJKOr2/DDBcMsNl"]]], [1, "75Fc9Vju1C6o5LVDAllCAD", null, null, null, 1, 0], [1, 0, -78, 0]], [10, "sliderbar", 33554432, 5, [-82, -83, -84], [[[2, -79, [0, "46j37UvPpLlKj9VVdvpbbd"], [5, 100, 444]], -80, [31, -81, [0, "5bJxb7N1NLcaopVNY5TMM5"]]], 4, 1, 4], [1, "c12QWemNtHvJea1LJ6Onyg", null, null, null, 1, 0], [1, -330, -248, 0]], [25, "node", 33554432, [-87, -88, -89, -90], [[2, -85, [0, "82MG/821dBka9p4JPO4jzg"], [5, 350, 102]], [41, 42, 200, 200, 100, -86, [0, "e2GQ9mhXNE7JCqxWWI/qxm"]]], [1, "caqynfPmhLpLoAT10s0OC9", null, null, null, 1, 0]], [14, "mask", 33554432, 6, [-95], [[2, -91, [0, "73kMIkoItNpZrarmEcdHCa"], [5, 40, 406]], [53, -92, [0, "f9CYmuSeZI3YagylWMsz2t"]], [55, -93, [0, "7feDNUvX9N8L4Ua16mG/XC"], [4, 16777215]], [22, -94, [0, "38l3EMqiVBx5gcnt9puLYf"]]], [1, "375BvBb7tEj4D6KDpJWzxI", null, null, null, 1, 0], [1, 4, -5, 0]], [14, "fineTurning", 33554432, 5, [-99, -100], [[2, -96, [0, "6ei92CDz1PgoXtDEuiZ77T"], [5, 60, 270]], [56, -97, [0, "d9V6jLi0BPA705CfU20wae"]], [31, -98, [0, "b7vK/U8UJEurLrkoAvOOwW"]]], [1, "687NTRBctGUaTpzHgJVJLF", null, null, null, 1, 0], [1, 330, -336, 0]], [35, "icon_set", 33554432, [[2, -101, [0, "7amsXNIpNEKrr2urpX3d8y"], [5, 61, 62]], [5, -102, [0, "c40UglVXdB3oF/lHaIqkGv"], 23], [57, -104, [0, "ebvItHQbhBZb97eTMY2A61"], [[20, "38f0e6Dj2RNGKg7QovKytkK", "onClickSet", 1]], -103], [28, 34, 19, 19, 6, 61, -105, [0, "00rlbj3sVF97gycDAfbjyK"]]], [1, "ccEizfp9tCaqTFO1J7GgBz", null, null, null, 1, 0], [1, 325.5, 6, 0]], [14, "saveBtn", 33554432, 3, [-110], [[2, -106, [0, "b1bu0b4tNCGpsFupBs/v0y"], [5, 70, 36]], [6, 1, 0, -107, [0, "caEaennpBA2KpBd+nBRobU"], 34], [23, 2, -109, [0, "danFcjnFZGnLNL4YqJUaKn"], [[20, "27bbdYLsvZKsLpWyOKbnUd9", "saveData", 3]], [4, **********], -108, 35, 36, 37, 38]], [1, "57UM6+jcxCNaO3LRAAwVPj", null, null, null, 1, 0], [1, 1.261, -160.582, 0]], [10, "minPowerTxt", 33554432, 3, [-114, -115], [[[2, -111, [0, "87o9u85YNIA6IwuanoLS54"], [5, 86, 40]], [6, 1, 0, -112, [0, "99Z69jRtlJT7JTlBzzyd1R"], 39], -113], 4, 4, 1], [1, "18SZ5fIklO77KM9FLGEJc7", null, null, null, 1, 0], [1, -11.734, 156.984, 0]], [10, "maxPowerTxt", 33554432, 3, [-119, -120], [[[2, -116, [0, "9bbHCorntFEojSD+ATQhO0"], [5, 96, 40]], [6, 1, 0, -117, [0, "14qRpTgeRFCIYKmAuDnqkD"], 40], -118], 4, 4, 1], [1, "30VCdAaAtCtYk9oMOJKnh5", null, null, null, 1, 0], [1, 92.334, 157.257, 0]], [10, "powerRange", 33554432, 3, [-124, -125], [[[2, -121, [0, "1cEzvnV39G+rgXxRAd8U5C"], [5, 180, 40]], [6, 1, 0, -122, [0, "adUeTJtWNO36dsDjj9PU3k"], 41], -123], 4, 4, 1], [1, "6eqnsRADJLkbXcEYNDfuAe", null, null, null, 1, 0], [1, 33.862, 108.568, 0]], [10, "restitutionTxt", 33554432, 3, [-129, -130], [[[2, -126, [0, "f2qPQRVF5MkLOg03jmTwsI"], [5, 100, 40]], [6, 1, 0, -127, [0, "2fDz+AsMNN9K8vzezzQ9K8"], 42], -128], 4, 4, 1], [1, "c98imtBZlBtYm6DmUn32yc", null, null, null, 1, 0], [1, 41.334, 11.764, 0]], [10, "frictionTxt", 33554432, 3, [-134, -135], [[[2, -131, [0, "86cOlk2dxPC6h3KoEMQYGR"], [5, 100, 40]], [6, 1, 0, -132, [0, "22gqaF50ZP/4+C9SgfPhQa"], 43], -133], 4, 4, 1], [1, "1fq0gof+9O3oNbaCcTwi+b", null, null, null, 1, 0], [1, 40.484, -35.592, 0]], [10, "linearDampTxt", 33554432, 3, [-139, -140], [[[2, -136, [0, "cfDK/h9FtIo4UQMqeAcW19"], [5, 100, 40]], [6, 1, 0, -137, [0, "85d5PVyQNENp11AogWEoIY"], 44], -138], 4, 4, 1], [1, "f4MPUyW5xKX5srJOlZzLXP", null, null, null, 1, 0], [1, 40.998, 60.702, 0]], [10, "tableLinearDampTxt", 33554432, 3, [-144, -145], [[[2, -141, [0, "e8LNDHeh5DaY5Aq02AXWeb"], [5, 100, 40]], [6, 1, 0, -142, [0, "14nyMQhH5DxZrJQtrJbx9b"], 45], -143], 4, 4, 1], [1, "abTtKUCPtBmaoL/3dTPgrn", null, null, null, 1, 0], [1, 40.223, -81.642, 0]], [19, "test8Btn", false, 33554432, 1, [-150], [[2, -146, [0, "8a1YvmLNZGwYAXlFzV8Wl6"], [5, 100, 40]], [6, 1, 0, -147, [0, "65DS0bchtEb4J/3RCDIraq"], 48], [23, 2, -149, [0, "65xVYRRxlLqKeNa9Xqg/3c"], [[20, "38f0e6Dj2RNGKg7QovKytkK", "test8Object", 1]], [4, **********], -148, 49, 50, 51, 52]], [1, "82bsr38rNHRapO4f3VyICr", null, null, null, 1, 0], [1, 166.296, 444.634, 0]], [19, "testBtn", false, 33554432, 1, [-155], [[2, -151, [0, "1cUPq09PNLsZnhsLeo71gU"], [5, 80, 40]], [6, 1, 0, -152, [0, "56h7blxE9DfKj6OM08RwcK"], 54], [23, 2, -154, [0, "c1hHLXfHpN6Z7xyQoyftuB"], [[20, "38f0e6Dj2RNGKg7QovKytkK", "testEnterBall", 1]], [4, **********], -153, 55, 56, 57, 58]], [1, "8dZCiw+3ZMZYvKGFr3feYp", null, null, null, 1, 0], [1, 35.383, 444.656, 0]], [19, "inputBallId", false, 33554432, 1, [-161, -162], [[2, -156, [0, "1f8k2RocdBdrU+rc+DhOUm"], [5, 200, 50]], [6, 1, 0, -157, [0, "09d39trSBOuaxBdF2Fgq86"], 59], [58, 8, -160, [0, "dfXx4QSUhHhIBEwWJNL3Yx"], -159, -158, 60]], [1, "a6XzyQsZhIOoYfb1q7fNh4", null, null, null, 1, 0], [1, -105.271, 444.889, 0]], [36, "fineTurningBgMask", 33554432, 9, [-166], [[2, -163, [0, "46F0enk2VPT5PejEAnZNS0"], [5, 59, 270]], [54, 3, 1, -164, [0, "90Mt95rztMXZymqPcTgclQ"]], [5, -165, [0, "65HJoh6eRF5qN6xUrjr7a1"], 19]], [1, "10Ujn9jPJCo7j10d/EdDF4", null, null, null, 1, 0]], [14, "hand", 33554432, 5, [-169, -170], [[2, -167, [0, "71N7OKgpdDVaMbHXMepb2F"], [5, 130, 130]], [30, 0, -168, [0, "6328Y5vS5GD5MLNkv4GFQx"]]], [1, "1fYcFdprVHOJBk8M3GgCu6", null, null, null, 1, 0], [1, 0, 118, 0]], [14, "topLayer", 33554432, 1, [10, 7], [[2, -171, [0, "5aBnI1X6RNDJHdXaf7xPxi"], [5, 750, 102]], [28, 41, -2.5757174171303632e-14, 2.5757174171303632e-14, 618, 750, -172, [0, "c2n+Lk9RlJobiBQIS2lWCn"]]], [1, "54UYohG2hAsrOBDSYNpe1E", null, null, null, 1, 0], [1, 0, 616, 0]], [4, "lblMode", 33554432, 7, [[3, -173, [0, "32kNC/N+pCx5rQPFyk1wBL"], [5, 180, 50.4], [0, 1, 0.5]], [59, "8 Ball Pool", 26, 26, 1, false, false, -174, [0, "d9sNYrJVBIpbj01nu4w12V"], 26], [42, 8, 10.195000000000007, -175, [0, "bcda4aBMJOd6CT+vBhOwog"]], [65, "ball8", -176, [0, "b0H76iPWpPar9VIDSjbE2p"]]], [1, "834AFRL7JEWLjzFv9Ut+eG", null, null, null, 1, 0], [1, 15.194999999999993, 8, 0]], [15, "img_bg", 33554432, 1, [[2, -177, [0, "99KQqWuMhMPbauHGRGWJYC"], [5, 750, 1334]], [30, 0, -178, [0, "2crM3ueMRJmq2olKNISDPi"]], [43, 45, 750, 1334, 1, -179, [0, "a13pOqb3VJppd/x1hF32LI"]]], [1, "81zKXI/txLKp9H9h6hk58j", null, null, null, 1, 0]], [4, "img_topBg", 33554432, 1, [[2, -180, [0, "d8UMcjUD1GI43oI43dk/gR"], [5, 750, 102]], [5, -181, [0, "20ueQQxHlISpRUiGDHWmPQ"], 0], [44, 17, -182, [0, "a3d/2piwVFtrrlcgiXN6Ng"]]], [1, "b5C6D6PF5CFJOPdWR7RNeN", null, null, null, 1, 0], [1, 0, 616, 0]], [26, "slider<PERSON>ower", false, 33554432, 6, [[2, -183, [0, "d6Lo3O07dFu5uYAKLuDig5"], [5, 42, 420]], [22, -184, [0, "0541hWcPJCcr0eGDaKTmLq"]], [5, -185, [0, "8bvRGOAyxAGq+UY9FOvaGQ"], 14]], [1, "f7mcDTFVxNuIC/g/Ovefan", null, null, null, 1, 0]], [15, "img_fineTurning_bg", 33554432, 9, [[2, -186, [0, "d94+dZDFVAAIe0RvU11gHl"], [5, 59, 270]], [5, -187, [0, "fef1pJKPtKAqQtwLEUleo5"], 16], [22, -188, [0, "3crk14VmFJPJyqnYmfyTBP"]]], [1, "97xO6+8DFHcZ/BIajSY7oE", null, null, null, 1, 0]], [4, "img_coin", 33554432, 7, [[2, -189, [0, "54YS3MWrBBbrtZmS1sooZZ"], [5, 45, 45]], [9, 0, -190, [0, "092RQXejFHPobazOoFke+A"], 25], [29, 32, 116.292, -191, [0, "6b1yYX3chDVo3rL9PwzDlJ"]]], [1, "823DFRpzNGGadYDDIDoQrm", null, null, null, 1, 0], [1, 36.208, 6, 0]], [7, "txt_winGold", 33554432, 7, [[[3, -192, [0, "741qABofBHPZsmbxdo6dEd"], [5, 0, 50.4], [0, 0, 0.5]], -193, [29, 32, 95.806, -194, [0, "b1Zu6zJrJAML3o+vhUgdjj"]]], 4, 1, 4], [1, "ddVRNj9yZDNZZmVYM2tVro", null, null, null, 1, 0], [1, 79.194, 7, 0]], [4, "player<PERSON>ode", 33554432, 1, [[2, -195, [0, "101FxzOdJLv4wPoUGN4Z/5"], [5, 750, 100]], [45, 42, 514, 750, -196, [0, "44VXvS/SpFt5oXc17/lT9T"]]], [1, "d6BIy7azNLzo+15mUweeQv", null, null, null, 1, 0], [1, 0, 514, 0]], [4, "img_package", 33554432, 5, [[2, -197, [0, "e32rRGqPJGzK0MZeqZ2KGC"], [5, 537, 108]], [5, -198, [0, "baSXiqARxN2YIRci+tvmZs"], 1]], [1, "4befvDsHRNqqLFGLP2Yq3/", null, null, null, 1, 0], [1, -8, -539, 0]], [4, "openningArea", 33554432, 2, [[2, -199, [0, "7fCtRXP3VInpKgxFQOLOaI"], [5, 504.166, 257.273]], [9, 0, -200, [0, "bayDdC99FNPZm0C9ohkGJm"], 2]], [1, "8aBdoQHzdC4KUdx3M0BO1m", null, null, null, 1, 0], [1, -1.2369999999999752, -360.283, 0]], [4, "bigCircle", 33554432, 2, [[2, -201, [0, "403WxBzpFNYbMHpvfWQnG9"], [5, 248, 248]], [9, 0, -202, [0, "78DOR7ujRM4blaTpb+wmdv"], 3]], [1, "878Y39pIVK7orZP5a3pi52", null, null, null, 1, 0], [1, 0, 40, 0]], [15, "touchNode", 33554432, 5, [[2, -203, [0, "ecQdrJ/GtCG5uWBh3qprG2"], [5, 1000, 1668]], [9, 0, -204, [0, "9an6Uu9EBInIuwZGsDhOTZ"], 5]], [1, "c8kOgV629FwZ3+KV0Wgr9n", null, null, null, 1, 0]], [4, "ray", 33554432, 4, [[3, -205, [0, "f3Q6dldZVOjo7ocuRTfj0M"], [5, 41, 61], [0, 0.5, 0]], [47, 1, -206, [0, "c53srmEbtMNoU+0SmjEjd4"], 6]], [1, "acNvgpg89DLbn1UKcfacTo", null, null, null, 1, 0], [1, -0.298, 4.945, 0]], [4, "lineH", 33554432, 4, [[3, -207, [0, "7esrKhI0JDRpLFYpuSfcXw"], [5, 22, 61], [0, 0.5, 0.18]], [5, -208, [0, "0fIHnb1QBMT69J8wQzat6H"], 7]], [1, "c51E5SIiRLZbDt/dKOOdqC", null, null, null, 1, 0], [1, 51.05, -144.791, 0]], [4, "circle", 33554432, 4, [[2, -209, [0, "a47n9kW2JBKYLdWDTcA34P"], [5, 47, 39]], [5, -210, [0, "66HnQCbxlPTbPuorD1Kc/P"], 8]], [1, "7cjEn95YpBZ6zXe3oUGg+h", null, null, null, 1, 0], [1, 0, 11.782, 0]], [4, "lineR", 33554432, 4, [[3, -211, [0, "13nAGgwjJMub7ngWwnOB5+"], [5, 22, 47], [0, 0.5, 0.2]], [5, -212, [0, "10Ejv2gH9FHolOeAlCvReJ"], 9]], [1, "03r59ecpdFr50EyrPnybWC", null, null, null, 1, 0], [1, 70.415, -146.551, 0]], [26, "forbidSet", false, 33554432, 4, [[2, -213, [0, "a9yq9dYZRC37/sxHyJgDiN"], [5, 30, 30]], [9, 0, -214, [0, "53Q5i9AmlDapam+nNJnwNc"], 10]], [1, "93e0sSp25IqLLOg4ES5UAs", null, null, null, 1, 0]], [27, "hitball", false, 33554432, 4, [[2, -215, [0, "8dpa6OEalGI6JIZvFJuQ7t"], [5, 64, 47]], [5, -216, [0, "caKrRLH19IErwV36x9Mjf2"], 11]], [1, "b25MyQJ09MG7+8B0umUIhT", null, null, null, 1, 0], [1, 0, -9.395, 0]], [15, "<PERSON><PERSON><PERSON><PERSON><PERSON>", 33554432, 6, [[2, -217, [0, "b8Rv0xJ7FOo6PlJX9MC6D1"], [5, 64, 444]], [5, -218, [0, "5aWpwMnFRALJM8kwJSqBqL"], 13]], [1, "3dPG65iv1CcYOq5BxbwJDG", null, null, null, 1, 0]], [15, "sliderCue", 33554432, 8, [[2, -219, [0, "ed7GZZCtpO87uWXJKC8Zl7"], [5, 39, 406]], [48, 1, 0, 1, -0.7, -220, [0, "a3oc+5/bpNBpLMtp8RSmFn"], 15]], [1, "39EqvdHHhDJrEC1wMIv0Vt", null, null, null, 1, 0]], [4, "scale", 33554432, 22, [[2, -221, [0, "47EZG6msJBZruTVommCtcV"], [5, 29, 242]], [49, 2, 0, -222, [0, "b0T2/tELlOVpYuB337xxCg"], 17, 18]], [1, "4bSvncs1NN558A2k/Mb8f+", null, null, null, 1, 0], [1, -1, 0, 0]], [15, "forbidSet", 33554432, 23, [[2, -223, [0, "3eXXkYXKhP9JSG8krmIb+n"], [5, 30, 30]], [9, 0, -224, [0, "f7+r7yjK1NvLsNXss0oZi6"], 20]], [1, "db5HPnKm1KxoR9G0l5A8TG", null, null, null, 1, 0]], [4, "hand", 33554432, 23, [[2, -225, [0, "ffxGKOTs1FupdbFl1AMzUP"], [5, 56, 41]], [9, 0, -226, [0, "03igwFMttL87ZD7STgp5q3"], 21]], [1, "d7h4phPzNF97zHwD5S43aa", null, null, null, 1, 0], [1, 23.964, -0.844, 0]], [27, "enterBallSk", false, 33554432, 5, [[3, -227, [0, "9b9y+AQkJAYZdUTVW7xNe4"], [5, 957.6799926757812, 2073.68994140625], [0, 0, 0]], [66, "default", false, 0, false, -228, [0, "4dDjmG3QlMtr222bP6//4K"], 22]], [1, "aeMjM+GBNDGZpefvxTop+Y", null, null, null, 1, 0], [1, -250, 480, 0]], [4, "image_title_bg", 33554432, 7, [[2, -229, [0, "762fH22ZxIJbBLAdq767Pg"], [5, 350, 54]], [6, 1, 0, -230, [0, "e51yfniutE+YDciwBIy1pg"], 24]], [1, "5fXUiesLZEB61yWSK32qJK", null, null, null, 1, 0], [1, 0, 6, 0]], [4, "Label", 33554432, 3, [[3, -231, [0, "78NY5+yDxBc61pjv4idsYh"], [5, 86.66015625, 31.5], [0, 0, 0.5]], [60, "击球力度:", 20, 20, 25, false, true, -232, [0, "50E3fPF39NnY73TiF5w+sr"], 27]], [1, "a7IaxQ5GJNY6XeWWG5snno", null, null, null, 1, 0], [1, -144.99, 156.449, 0]], [4, "Label", 33554432, 3, [[3, -233, [0, "484UQds3hPG6D5y7QvC914"], [5, 126.66015625, 31.5], [0, 0, 0.5]], [18, "白球衰减速度:", 0, 20, 20, 25, false, true, -234, [0, "0ejgwcXS5EtqT+ZXOd7yM3"], 28]], [1, "7blUubTeZIkoZb2OBI0vfa", null, null, null, 1, 0], [1, -145, 63.152, 0]], [4, "Label", 33554432, 3, [[3, -235, [0, "5dUFpA4kxLZa0r27c6ytRW"], [5, 106.66015625, 31.5], [0, 0, 0.5]], [18, "白球反弹力:", 0, 20, 20, 25, false, true, -236, [0, "b2Fi5Ic0VEJ5w0bHHZjpKP"], 29]], [1, "e5RTbuxOhE54JcZojxgqwB", null, null, null, 1, 0], [1, -146.24, 9.487, 0]], [4, "Label", 33554432, 3, [[3, -237, [0, "1ax8UXFalMI4rOAgzgsAYu"], [5, 106.66015625, 31.5], [0, 0, 0.5]], [18, "白球摩擦力:", 0, 20, 20, 25, false, true, -238, [0, "59M+QUL8VOU7XGrYAxZLYh"], 30]], [1, "beuDaM0shN07jdk5Q1T973", null, null, null, 1, 0], [1, -144.925, -37.869, 0]], [4, "Label", 33554432, 3, [[3, -239, [0, "1a8dvmK4tO6rtAz43pdzim"], [5, 126.66015625, 31.5], [0, 0, 0.5]], [18, "库边衰减速度:", 0, 20, 20, 25, false, true, -240, [0, "0eExb2wiRI6aJbHgLvIdPV"], 31]], [1, "61pUJ6NqxD/reuDR0vt0wP", null, null, null, 1, 0], [1, -146.854, -81.736, 0]], [4, "Label", 33554432, 3, [[3, -241, [0, "5fCi1vE4lNI5wP0WiPI27g"], [5, 304.501953125, 56.49999999999999], [0, 0, 0.5]], [18, "他球速度衰减系数0.6;  反弹力0.85\n摩擦力0", 0, 20, 20, 25, false, true, -242, [0, "ad81EjoT9MGL3ov7qPS/tG"], 32]], [1, "7bQUCCdS1CwK0K09buXsbM", null, null, null, 1, 0], [1, -149.242, -127.939, 0]], [21, "Label", 512, 33554432, 11, [[2, -243, [0, "9fAjoowWZItJilAjkZBILs"], [5, 60, 35]], [24, "save", 20, 20, 1, false, false, -244, [0, "f0GsnkXflDUrcyaInnoWVB"], [4, 4278190080], 33]], [1, "b774T15UBFzpA90IDrbdQ/", null, null, null, 1, 0]], [8, "TEXT_LABEL", false, 33554432, 12, [[[3, -245, [0, "8eAauBReBLNbzv3vEOQlq3"], [5, 84, 40], [0, 0, 1]], -246], 4, 1], [1, "d6hKkqm8BED6tZZWeFjsEU", null, null, null, 1, 0], [1, -41, 20, 0]], [7, "PLACEHOLDER_LABEL", 33554432, 12, [[[3, -247, [0, "a9GYnn+qBMroLqBUuddCOE"], [5, 84, 40], [0, 0, 1]], -248], 4, 1], [1, "06umR3/oJLQ55JveRXQ/eC", null, null, null, 1, 0], [1, -41, 20, 0]], [8, "TEXT_LABEL", false, 33554432, 13, [[[3, -249, [0, "5egiG/pi9C85JugDizsnxP"], [5, 94, 40], [0, 0, 1]], -250], 4, 1], [1, "f9bvzEp3VOqrAWta/6aBrB", null, null, null, 1, 0], [1, -46, 20, 0]], [7, "PLACEHOLDER_LABEL", 33554432, 13, [[[3, -251, [0, "e19HLaQJ1CM5I7GzxDqPzC"], [5, 94, 40], [0, 0, 1]], -252], 4, 1], [1, "24V6n9MsFEhJxOwcWVnQms", null, null, null, 1, 0], [1, -46, 20, 0]], [8, "TEXT_LABEL", false, 33554432, 14, [[[3, -253, [0, "e92M+UCkxCmqja3Nv/KwJS"], [5, 178, 40], [0, 0, 1]], -254], 4, 1], [1, "89cITH3vxLg73+D3pCyi6n", null, null, null, 1, 0], [1, -88, 20, 0]], [7, "PLACEHOLDER_LABEL", 33554432, 14, [[[3, -255, [0, "88qghycMdNbYJxL+DFG3mK"], [5, 178, 40], [0, 0, 1]], -256], 4, 1], [1, "e0JlVLunhFcIyvdUTs5kOG", null, null, null, 1, 0], [1, -88, 20, 0]], [8, "TEXT_LABEL", false, 33554432, 15, [[[3, -257, [0, "82OxlnNvdBAbZBlzG5TgMp"], [5, 98, 40], [0, 0, 1]], -258], 4, 1], [1, "9eTSsAvTVMVbpHXoNwjIAo", null, null, null, 1, 0], [1, -48, 20, 0]], [7, "PLACEHOLDER_LABEL", 33554432, 15, [[[3, -259, [0, "f9reWRvcBAD64qX6kX6gfr"], [5, 98, 40], [0, 0, 1]], -260], 4, 1], [1, "d2sSMOPQdKd7GPmIvvEsR/", null, null, null, 1, 0], [1, -48, 20, 0]], [8, "TEXT_LABEL", false, 33554432, 16, [[[3, -261, [0, "07VbpG045NYpP/TzXt3j3D"], [5, 98, 40], [0, 0, 1]], -262], 4, 1], [1, "baDENsILlCNIvV9fLkAiGT", null, null, null, 1, 0], [1, -48, 20, 0]], [7, "PLACEHOLDER_LABEL", 33554432, 16, [[[3, -263, [0, "efnca5rk9JOINay3G8YPca"], [5, 98, 40], [0, 0, 1]], -264], 4, 1], [1, "0aXoLsfWJMDKnQfwbP7AHB", null, null, null, 1, 0], [1, -48, 20, 0]], [8, "TEXT_LABEL", false, 33554432, 17, [[[3, -265, [0, "10L5r7dxZEcLvpIjg/qn1A"], [5, 98, 40], [0, 0, 1]], -266], 4, 1], [1, "4ay9MC1ctGo4DIO6jtKfxV", null, null, null, 1, 0], [1, -48, 20, 0]], [7, "PLACEHOLDER_LABEL", 33554432, 17, [[[3, -267, [0, "a4yIug33ZKGLU6yBh//gLP"], [5, 98, 40], [0, 0, 1]], -268], 4, 1], [1, "528vl3Hc9DI7+jh<PERSON><PERSON><PERSON><PERSON>", null, null, null, 1, 0], [1, -48, 20, 0]], [8, "TEXT_LABEL", false, 33554432, 18, [[[3, -269, [0, "99VEsrJYdJPb78VCZSJj1Z"], [5, 98, 40], [0, 0, 1]], -270], 4, 1], [1, "46btxYSIlCwrIxEQ5fNwLe", null, null, null, 1, 0], [1, -48, 20, 0]], [7, "PLACEHOLDER_LABEL", 33554432, 18, [[[3, -271, [0, "f3HxANiZ9OWa92GQh3scMQ"], [5, 98, 40], [0, 0, 1]], -272], 4, 1], [1, "baM9+njZtL0ZpYU0HoE1Yi", null, null, null, 1, 0], [1, -48, 20, 0]], [21, "Label", 512, 33554432, 19, [[2, -273, [0, "80+749NuVEcI7NO4L3ailP"], [5, 100, 40]], [24, "进黑8", 20, 20, 1, false, false, -274, [0, "a0rMkcMqJBiLhSsmF6kvKg"], [4, 4278190080], 47]], [1, "31iMtIioJLxaonocE8XV9h", null, null, null, 1, 0]], [8, "test<PERSON><PERSON><PERSON>", false, 33554432, 1, [[[3, -275, [0, "beG9AkYL9Kk5BJeVDy6ZF0"], [5, 750, 25.2], [0, 0, 0.5]], -276], 4, 1], [1, "63dmh5oNBONL962Zn3l+fG", null, null, null, 1, 0], [1, -373.176, 567.684, 0]], [21, "Label", 512, 33554432, 20, [[2, -277, [0, "78sFE/wldAr4F7xqyWaZjZ"], [5, 100, 40]], [24, "进球", 20, 20, 1, false, false, -278, [0, "50/iogPRFHlL64V8G+E146"], [4, 4278190080], 53]], [1, "fbwNKsxQlMPLKygatAI7U/", null, null, null, 1, 0]], [8, "TEXT_LABEL", false, 33554432, 21, [[[3, -279, [0, "d7447P7MlBLqfAp2gfhiqs"], [5, 198, 50], [0, 0, 1]], -280], 4, 1], [1, "1aq2LsTshJ1bFXL5F9yCPj", null, null, null, 1, 0], [1, -98, 25, 0]], [7, "PLACEHOLDER_LABEL", 33554432, 21, [[[3, -281, [0, "c4Qxp2KvVAypI2/FnCYa+B"], [5, 198, 50], [0, 0, 1]], -282], 4, 1], [1, "fekXwz/8FK0pBnWWt71M9R", null, null, null, 1, 0], [1, -98, 25, 0]], [67, 2, [0, "dfX2RypDdIRIJpo0c7FXPm"]], [68, 4, [0, "d7k5arhp1DUpdJIAyt+onF"]], [69, 6, [0, "9fGlaHmXhLiaXZ6ePRSrDH"]], [61, "", 0, 32, 32, false, false, 31, [0, "59Isj+kFZIfYFIuMn7uLWB"], [4, 4284217855]], [12, "", 0, 40, 20, 1, false, false, 57, [0, "9cODEqe4JEEqpA4b8+UKJW"]], [13, "1", 0, 20, 20, 1, false, false, 58, [0, "0d3Cjd0mZElKAdDKOeh8rp"], [4, 4290493371]], [11, 6, 8, 12, [0, "d3EcSnMGtNBYwhzKhnNqo2"], 80, 81], [12, "", 0, 40, 20, 1, false, false, 59, [0, "20vIeBeQdA9pkx+TtxY1Bl"]], [13, "5", 0, 20, 20, 1, false, false, 60, [0, "35Vq0VV7pDJL6B<PERSON><PERSON><PERSON>yi"], [4, 4290493371]], [11, 6, 8, 13, [0, "858TT7w7tKnKFEFOgkPZQh"], 83, 84], [12, "", 0, 40, 20, 1, false, false, 61, [0, "d5iDCeIN9IGLw7/wjjcHrj"]], [13, "80", 0, 20, 20, 1, false, false, 62, [0, "a7Uil3HgpDJqn4ECgOdvJV"], [4, 4290493371]], [11, 6, 8, 14, [0, "bf0Lq9AhNBrbDnxbkxKm75"], 86, 87], [12, "", 0, 40, 20, 1, false, false, 63, [0, "51n9abnEJA8ZaXcscGo9k+"]], [13, "0.85", 0, 20, 20, 1, false, false, 64, [0, "d8OVvqDEdFJIQu1BqAx1BS"], [4, 4290493371]], [11, 6, 8, 15, [0, "a6R/SdSAVAWY/JIsvTGWMo"], 89, 90], [12, "", 0, 40, 20, 1, false, false, 65, [0, "e7eAEMwg9KaKs7DRXUhydw"]], [13, "0", 0, 20, 20, 1, false, false, 66, [0, "ccXgv3Ft1BL71Yrt/rC27J"], [4, 4290493371]], [11, 6, 8, 16, [0, "dbhFFOAkNPNZ0f/BsQnW8O"], 92, 93], [12, "", 0, 40, 20, 1, false, false, 67, [0, "b5F6+kVR9GFqBw6pFJXEWf"]], [13, "0.6", 0, 20, 20, 1, false, false, 68, [0, "9818w4LzVKnpK9KT3mIz7h"], [4, 4290493371]], [11, 6, 8, 17, [0, "efhfFICZZMK7lSOjLiUStF"], 95, 96], [12, "", 0, 40, 20, 1, false, false, 69, [0, "6ck8NSqxdO+IZh8ycagepn"]], [13, "0-1", 0, 20, 20, 1, false, false, 70, [0, "1beaFDbgRGlo6VYHcQDdUT"], [4, 4290493371]], [11, 6, 8, 18, [0, "36HgBWgUBELr5aotogvCh3"], 98, 99], [62, "", 0, 20, 20, 20, 3, 72, [0, "70ykswpp1A+Ig92VsREKwB"]], [63, "", 0, 0, 20, 20, 1, false, 74, [0, "96AUs+IvBMKKuGFPvWDklI"]], [64, "输入球id 2-1", 0, 20, 20, 1, false, 75, [0, "81+vmvrypHC6jqO+YagN0B"], [4, 4290493371]]], 0, [0, 12, 1, 0, 0, 1, 0, 0, 1, 0, 13, 101, 0, 14, 79, 0, 15, 78, 0, 16, 76, 0, 17, 77, 0, 0, 1, 0, -1, 26, 0, -2, 27, 0, -3, 32, 0, -4, 5, 0, -5, 24, 0, -6, 3, 0, -7, 19, 0, -8, 72, 0, -9, 20, 0, -10, 21, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -16, 76, 0, -1, 34, 0, -2, 35, 0, 0, 3, 0, 0, 3, 0, 18, 100, 0, 19, 94, 0, 20, 91, 0, 21, 97, 0, 22, 88, 0, 23, 85, 0, 24, 82, 0, 0, 3, 0, -1, 50, 0, -2, 51, 0, -3, 52, 0, -4, 53, 0, -5, 54, 0, -6, 55, 0, -7, 11, 0, -8, 12, 0, -9, 13, 0, -10, 14, 0, -11, 15, 0, -12, 16, 0, -13, 17, 0, -14, 18, 0, 0, 4, 0, 0, 4, 0, -3, 77, 0, 0, 4, 0, -1, 37, 0, -2, 38, 0, -3, 39, 0, -4, 40, 0, -5, 41, 0, -6, 42, 0, 0, 5, 0, -1, 33, 0, -3, 36, 0, -5, 6, 0, -6, 9, 0, -7, 23, 0, -8, 48, 0, 0, 6, 0, -2, 78, 0, 0, 6, 0, -1, 43, 0, -2, 28, 0, -3, 8, 0, 0, 7, 0, 0, 7, 0, -1, 49, 0, -2, 30, 0, -3, 31, 0, -4, 25, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 44, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, -1, 29, 0, -2, 22, 0, 0, 10, 0, 0, 10, 0, 5, 10, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 5, 11, 0, 0, 11, 0, -1, 56, 0, 0, 12, 0, 0, 12, 0, -3, 82, 0, -1, 57, 0, -2, 58, 0, 0, 13, 0, 0, 13, 0, -3, 85, 0, -1, 59, 0, -2, 60, 0, 0, 14, 0, 0, 14, 0, -3, 88, 0, -1, 61, 0, -2, 62, 0, 0, 15, 0, 0, 15, 0, -3, 91, 0, -1, 63, 0, -2, 64, 0, 0, 16, 0, 0, 16, 0, -3, 94, 0, -1, 65, 0, -2, 66, 0, 0, 17, 0, 0, 17, 0, -3, 97, 0, -1, 67, 0, -2, 68, 0, 0, 18, 0, 0, 18, 0, -3, 100, 0, -1, 69, 0, -2, 70, 0, 0, 19, 0, 0, 19, 0, 5, 19, 0, 0, 19, 0, -1, 71, 0, 0, 20, 0, 0, 20, 0, 5, 20, 0, 0, 20, 0, -1, 73, 0, 0, 21, 0, 0, 21, 0, 25, 103, 0, 26, 102, 0, 0, 21, 0, -1, 74, 0, -2, 75, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, -1, 45, 0, 0, 23, 0, 0, 23, 0, -1, 46, 0, -2, 47, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, -2, 79, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, 0, 48, 0, 0, 49, 0, 0, 49, 0, 0, 50, 0, 0, 50, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, 0, 53, 0, 0, 54, 0, 0, 54, 0, 0, 55, 0, 0, 55, 0, 0, 56, 0, 0, 56, 0, 0, 57, 0, -2, 80, 0, 0, 58, 0, -2, 81, 0, 0, 59, 0, -2, 83, 0, 0, 60, 0, -2, 84, 0, 0, 61, 0, -2, 86, 0, 0, 62, 0, -2, 87, 0, 0, 63, 0, -2, 89, 0, 0, 64, 0, -2, 90, 0, 0, 65, 0, -2, 92, 0, 0, 66, 0, -2, 93, 0, 0, 67, 0, -2, 95, 0, 0, 68, 0, -2, 96, 0, 0, 69, 0, -2, 98, 0, 0, 70, 0, -2, 99, 0, 0, 71, 0, 0, 71, 0, 0, 72, 0, -2, 101, 0, 0, 73, 0, 0, 73, 0, 0, 74, 0, -2, 102, 0, 0, 75, 0, -2, 103, 0, 27, 1, 2, 6, 5, 4, 6, 5, 7, 6, 24, 10, 6, 24, 282], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 102, 103], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 28, 1, 1, 1, 1, 29, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 1, 7, 8, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 7, 8, 9, 10, 2, 1, 7, 8, 9, 10, 1, 3, 30, -1, -2, 31, 32, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, 2, 2, 2, 3, 2, 2, 3, 2, 2, 3, 2, 2, 3, 2, 2, 3, 2, 2, 3, 2, 2, 3, 2, 2], [16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 5, 26, 27, 28, 29, 30, 6, 31, 32, 6, 5, 33, 34, 35, 36, 37, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 3, 4, 1, 1, 1, 1, 1, 1, 1, 38, 0, 2, 2, 2, 3, 4, 0, 2, 2, 2, 3, 4, 7, 7, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0]]]]