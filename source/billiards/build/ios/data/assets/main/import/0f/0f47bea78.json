[1, ["fd0c36d8-11b4-4071-9acf-20a783b46e64", "20835ba4-6145-4fbc-a58a-051ce700aa3e@f9941", "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@f9941", "544e49d6-3f05-4fa8-9a9e-091f98fc2ce8@f9941", "951249e0-9f16-456d-8b85-a6ca954da16b@f9941", "9800039e-478e-4818-8228-129c713b3a14", "71350878-a712-4da3-982c-119b2afbb5a5", "6bb9b7d4-7e62-4501-a638-ba268f838f00@bb1a3", "6bb9b7d4-7e62-4501-a638-ba268f838f00@93043", "6bb9b7d4-7e62-4501-a638-ba268f838f00@a6b9d", "6bb9b7d4-7e62-4501-a638-ba268f838f00@ecffe", "6bb9b7d4-7e62-4501-a638-ba268f838f00@74ef1", "6bb9b7d4-7e62-4501-a638-ba268f838f00@7d214", "6bb9b7d4-7e62-4501-a638-ba268f838f00@fea17", "6bb9b7d4-7e62-4501-a638-ba268f838f00@d7755", "6bb9b7d4-7e62-4501-a638-ba268f838f00@fe069", "6bb9b7d4-7e62-4501-a638-ba268f838f00@0f466", "6bb9b7d4-7e62-4501-a638-ba268f838f00@d6710", "6bb9b7d4-7e62-4501-a638-ba268f838f00@6422a", "6bb9b7d4-7e62-4501-a638-ba268f838f00@cdab0", "6bb9b7d4-7e62-4501-a638-ba268f838f00@c10f9", "6bb9b7d4-7e62-4501-a638-ba268f838f00@a8656", "6bb9b7d4-7e62-4501-a638-ba268f838f00@979f3", "5c8f6625-c192-4e23-b762-a276b1d9ad0f", "7d8f9b89-4fd1-4c9f-a3ab-38ec7cded7ca@6c48a"], ["node", "_font", "_spriteFrame", "_cameraComponent", "tipLabel", "test<PERSON><PERSON><PERSON>", "_target", "scene", "_parent", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite", "b1", "b2", "testBall", "_textureSource"], [["cc.Node", ["_name", "_layer", "_id", "_active", "_obj<PERSON><PERSON>s", "_components", "_parent", "_lpos", "_children"], -2, 9, 1, 5, 2], ["cc.Label", ["_actualFontSize", "_isSystemFontUsed", "_overflow", "_fontSize", "_string", "_horizontalAlign", "_lineHeight", "_enableWrapText", "_isBold", "_cacheMode", "node", "_font", "_color"], -7, 1, 6, 5], ["cc.UITransform", ["node", "_contentSize", "_anchorPoint"], 3, 1, 5, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_spriteFrame", "_color"], 0, 1, 6, 5], "cc.SpriteFrame", ["cc.SceneAsset", ["_name"], 2], ["cc.Node", ["_name", "_active", "_layer", "_parent", "_components", "_lpos"], 0, 1, 12, 5], ["cc.Node", ["_name", "_parent", "_components", "_lpos"], 2, 1, 2, 5], ["cc.<PERSON>", ["node", "_cameraComponent"], 3, 1, 1], ["cc.Widget", ["_alignFlags", "_left", "_right", "_top", "_bottom", "node"], -2, 1], ["f02eewRratLjYzYl4+t/qIV", ["node", "textures", "test<PERSON><PERSON><PERSON>", "tipLabel", "b1", "b2", "testBall"], 3, 1, 3, 1, 1, 6, 6, 6], ["cc.<PERSON><PERSON>", ["_transition", "node", "clickEvents", "_normalColor", "_target", "_normalSprite", "_hoverSprite", "_pressedSprite", "_disabledSprite"], 2, 1, 9, 5, 1, 6, 6, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.BoxCollider2D", ["_friction", "node", "_size"], 2, 1, 5], ["cc.RigidBody2D", ["_type", "node"], 2, 1], ["cc.RichText", ["_horizontalAlign", "_verticalAlign", "_fontSize", "_maxWidth", "_isSystemFontUsed", "node", "_font"], -2, 1, 6], ["cc.Scene", ["_name", "_children", "_prefab", "_globals"], 2, 2, 4, 4], ["cc.PrefabInfo", ["root", "asset", "fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots"], -3], ["cc.SceneGlobals", ["ambient", "shadows", "_skybox", "fog", "octree", "skin", "lightProbeInfo", "postSettings"], 3, 4, 4, 4, 4, 4, 4, 4, 4], ["cc.AmbientInfo", ["_skyIllumLDR", "_skyColorHDR", "_groundAlbedoHDR", "_skyColorLDR", "_groundAlbedoLDR"], 2, 5, 5, 5, 5], ["cc.ShadowsInfo", ["_shadowColor", "_size"], 3, 5, 5], ["cc.SkyboxInfo", ["_useHDR"], 2], ["cc.FogInfo", [], 3], ["cc.OctreeInfo", [], 3], ["cc.SkinInfo", [], 3], ["cc.LightProbeInfo", [], 3], ["cc.PostSettingsInfo", [], 3], ["cc.Camera", ["_projection", "_priority", "_orthoHeight", "_far", "_visibility", "node", "_color"], -2, 1, 5]], [[2, 0, 1, 1], [0, 0, 1, 6, 5, 7, 3], [13, 0, 1, 2, 2], [14, 0, 1, 2], [0, 0, 1, 6, 5, 3], [6, 0, 1, 2, 3, 4, 5, 4], [2, 0, 1], [5, 0, 2], [0, 0, 1, 2, 8, 5, 7, 4], [0, 0, 1, 6, 8, 5, 3], [0, 0, 1, 6, 8, 5, 7, 3], [0, 0, 3, 1, 6, 5, 7, 4], [0, 0, 4, 1, 6, 5, 4], [7, 0, 1, 2, 3, 2], [2, 0, 1, 2, 1], [8, 0, 1, 1], [9, 0, 1, 2, 3, 4, 5, 6], [10, 0, 1, 2, 3, 4, 5, 6, 1], [3, 0, 1, 3, 3], [3, 2, 0, 3, 4, 3], [3, 0, 3, 5, 4, 2], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 2], [12, 0, 1, 2, 3], [1, 5, 0, 6, 2, 1, 10, 11, 6], [1, 4, 0, 3, 2, 7, 1, 10, 12, 11, 7], [1, 0, 3, 1, 10, 4], [1, 4, 0, 3, 2, 1, 8, 9, 10, 8], [15, 0, 1, 2, 3, 4, 5, 6, 6], [16, 0, 1, 2, 3, 2], [17, 0, 1, 2, 3, 4, 5, 7], [18, 0, 1, 2, 3, 4, 5, 6, 7, 1], [19, 0, 1, 2, 3, 4, 2], [20, 0, 1, 1], [21, 0, 2], [22, 1], [23, 1], [24, 1], [25, 1], [26, 1], [27, 0, 1, 2, 3, 4, 5, 6, 6]], [[[[7, "test"], [8, "<PERSON><PERSON>", 33554432, "4ed6CEVlxDdKG4E9WFDzfj", [-8, -9, -10, -11, -12, -13, -14, -15, -16, -17], [[0, -1, [5, 750, 1334]], [15, -3, -2], [16, 45, -5.684341886080802e-14, -5.684341886080802e-14, -1.1368683772161603e-13, -1.1368683772161603e-13, -4], [17, -7, [11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26], -6, -5, 9, 10, 27]], [1, 374.99999999999994, 666.9999999999999, 0]], [9, "table", 33554432, 1, [-20, -21, -22, -23], [[0, -18, [5, 520, 890]], [18, 0, false, -19]]], [10, "<PERSON><PERSON>", 33554432, 1, [-28], [[0, -24, [5, 100, 40]], [19, 1, 0, -25, 4], [21, 2, -27, [[22, "f02eewRratLjYzYl4+t/qIV", "test8", 1]], [4, 4292269782], -26, 5, 6, 7, 8]], [1, 0, 396.916, 0]], [1, "Node", 33554432, 2, [[0, -29, [5, 36, 800]], [2, 0, -30, [5, 36, 800]], [3, 0, -31]], [1, -208, 0, 0]], [1, "Node-001", 33554432, 2, [[0, -32, [5, 36, 800]], [2, 0, -33, [5, 36, 800]], [3, 0, -34]], [1, 208, 0, 0]], [1, "Node-002", 33554432, 2, [[0, -35, [5, 400, 36]], [2, 0, -36, [5, 400, 36]], [3, 0, -37]], [1, 5.684341886080802e-14, 394, 0]], [1, "Node-003", 33554432, 2, [[0, -38, [5, 400, 36]], [2, 0, -39, [5, 400, 36]], [3, 0, -40]], [1, 5.684341886080802e-14, -394, 0]], [4, "Sprite", 33554432, 1, [[0, -41, [5, 750, 1623.99]], [20, 0, -42, [4, 4284706615], 0]]], [1, "Label", 33554432, 1, [[0, -43, [5, 300, 63]], [23, 0, 40, 50, 3, false, -44, 1]], [1, 70.247, 585.285, 0]], [5, "test<PERSON><PERSON><PERSON>", false, 33554432, 1, [[[0, -45, [5, 54.93, 50.4]], -46], 4, 1], [1, 0, 525.963, 0]], [11, "testRichTxt", false, 33554432, 1, [[0, -47, [5, 420, 50.4]], [27, 1, 1, 26, 420, false, -48, 2]], [1, 1.92, 312.475, 0]], [12, "Label", 512, 33554432, 3, [[0, -49, [5, 100, 40]], [24, "button", 20, 20, 1, false, false, -50, [4, 4278190080], 3]]], [5, "tipLabel", false, 33554432, 1, [[[14, -51, [5, 420, 50.4], [0, 0.5, 1]], -52], 4, 1], [1, 10.356, 469.492, 0]], [28, "test", [1], [29, null, null, "0463653e-daaa-46af-960d-ef0eef23f62b", null, null, null], [30, [31, 0.5208, [2, 0.242613, 0.362617, 0.798746, 0.520833125], [2, 0.241814, 0.361945, 0.798799, 0], [2, 0.5176470588235293, 0.6274509803921569, 0.9019607843137255, 0.5208], [2, 0.5176470588235293, 0.6274509803921569, 0.9019607843137255, 1]], [32, [4, 4283190348], [0, 512, 512]], [33, false], [34], [35], [36], [37], [38]]], [13, "Camera", 1, [-53], [1, 0, 0, 1000]], [39, 0, 1073741824, 667, 2000, 41943040, 15, [4, 0]], [4, "ballBackNode", 33554432, 1, [[6, -54]]], [4, "ballFrontNode", 33554432, 1, [[6, -55]]], [25, 26, 26, false, 10], [26, "", 26, 26, 1, false, true, 1, 13]], 0, [0, 0, 1, 0, 3, 16, 0, 0, 1, 0, 0, 1, 0, 4, 20, 0, 5, 19, 0, 0, 1, 0, -1, 8, 0, -2, 15, 0, -3, 2, 0, -4, 17, 0, -5, 18, 0, -6, 9, 0, -7, 10, 0, -8, 11, 0, -9, 3, 0, -10, 13, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, 0, 3, 0, 0, 3, 0, 6, 3, 0, 0, 3, 0, -1, 12, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, -2, 19, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, -2, 20, 0, -1, 16, 0, 0, 17, 0, 0, 18, 0, 7, 14, 1, 8, 14, 55], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 20], [2, 1, 1, 1, 2, 9, 10, 11, 12, 13, 14, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, 15, 1, 1], [2, 0, 0, 0, 1, 1, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 0, 0]], [[{"name": "default_sprite_splash", "rect": {"x": 0, "y": 0, "width": 2, "height": 2}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 2, "height": 2}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-1, -1, 0, 1, -1, 0, -1, 1, 0, 1, 1, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 2, 2, 2, 0, 0, 2, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -1, "y": -1, "z": 0}, "maxPos": {"x": 1, "y": 1, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [16], [24]]]]