System.register([], (function (exports) {
	'use strict';
	return {
		execute: (function () {

			function getDefaultExportFromCjs (x) {
				return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;
			}

			var box2d_release_asm$2 = {exports: {}};

			(function (module, exports) {
				var BOX2D = (() => {
				  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
				  
				  return (
				function(BOX2D = {})  {

				var Module=typeof BOX2D!="undefined"?BOX2D:{};var readyPromiseResolve,readyPromiseReject;Module["ready"]=new Promise((resolve,reject)=>{readyPromiseResolve=resolve;readyPromiseReject=reject;});var moduleOverrides=Object.assign({},Module);var ENVIRONMENT_IS_WEB=true;var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var readBinary;{if(typeof document!="undefined"&&document.currentScript){scriptDirectory=document.currentScript.src;}if(_scriptDir){scriptDirectory=_scriptDir;}if(scriptDirectory.indexOf("blob:")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1);}else {scriptDirectory="";}}var out=Module["print"]||console.log.bind(console);var err=Module["printErr"]||console.error.bind(console);Object.assign(Module,moduleOverrides);moduleOverrides=null;if(Module["arguments"])Module["arguments"];if(Module["thisProgram"])Module["thisProgram"];if(Module["quit"])Module["quit"];var wasmBinary;if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];Module["noExitRuntime"]||true;var WebAssembly={Memory:function(opts){this.buffer=new ArrayBuffer(opts["initial"]*65536);},Module:function(binary){},Instance:function(module,info){this.exports=(
				// EMSCRIPTEN_START_ASM
				function instantiate(Qa){function c(d){d.set=function(a,b){this[a]=b;};d.get=function(a){return this[a]};return d}var e;var f=new Uint8Array(123);for(var a=25;a>=0;--a){f[48+a]=52+a;f[65+a]=a;f[97+a]=26+a;}f[43]=62;f[47]=63;function l(m,n,o){var g,h,a=0,i=n,j=o.length,k=n+(j*3>>2)-(o[j-2]=="=")-(o[j-1]=="=");for(;a<j;a+=4){g=f[o.charCodeAt(a+1)];h=f[o.charCodeAt(a+2)];m[i++]=f[o.charCodeAt(a)]<<2|g>>4;if(i<k)m[i++]=g<<4|h>>2;if(i<k)m[i++]=h<<6|f[o.charCodeAt(a+3)];}}function p(q){l(e,1024,"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");l(e,12752,"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");l(e,17939,"QPsh+T8AAAAALUR0PgAAAICYRvg8AAAAYFHMeDsAAACAgxvwOQAAAEAgJXo4AAAAgCKC4zYAAAAAHfNpNRkACgAZGRkAAAAABQAAAAAAAAkAAAAACwAAAAAAAAAAGQARChkZGQMKBwABAAkLGAAACQYLAAALAAYZAAAAGRkZ");l(e,18081,"DgAAAAAAAAAAGQAKDRkZGQANAAACAAkOAAAACQAOAAAO");l(e,18139,"DA==");l(e,18151,"EwAAAAATAAAAAAkMAAAAAAAMAAAM");l(e,18197,"EA==");l(e,18209,"DwAAAAQPAAAAAAkQAAAAAAAQAAAQ");l(e,18255,"Eg==");l(e,18267,"EQAAAAARAAAAAAkSAAAAAAASAAASAAAaAAAAGhoa");l(e,18322,"GgAAABoaGgAAAAAAAAk=");l(e,18371,"FA==");l(e,18383,"FwAAAAAXAAAAAAkUAAAAAAAUAAAU");l(e,18429,"Fg==");l(e,18441,"FQAAAAAVAAAAAAkWAAAAAAAWAAAWAAAwMTIzNDU2Nzg5QUJDREVGTjEwX19jeHhhYml2MTE2X19zaGltX3R5cGVfaW5mb0UAAAAA7EoAADBIAADISwAATjEwX19jeHhhYml2MTE3X19jbGFzc190eXBlX2luZm9FAAAA7EoAAGBIAABUSAAATjEwX19jeHhhYml2MTE3X19wYmFzZV90eXBlX2luZm9FAAAA7EoAAJBIAABUSAAATjEwX19jeHhhYml2MTE5X19wb2ludGVyX3R5cGVfaW5mb0UA7EoAAMBIAAC0SAAATjEwX19jeHhhYml2MTIwX19mdW5jdGlvbl90eXBlX2luZm9FAAAAAOxKAADwSAAAVEgAAE4xMF9fY3h4YWJpdjEyOV9fcG9pbnRlcl90b19tZW1iZXJfdHlwZV9pbmZvRQAAAOxKAAAkSQAAtEgAAAAAAACkSQAAaAMAAGkDAABqAwAAawMAAGwDAABOMTBfX2N4eGFiaXYxMjNfX2Z1bmRhbWVudGFsX3R5cGVfaW5mb0UA7EoAAHxJAABUSAAAdgAAAGhJAACwSQAARG4AAGhJAAC8SQAAYgAAAGhJAADISQAAYwAAAGhJAADUSQAAaAAAAGhJAADgSQAAYQAAAGhJAADsSQAAcwAAAGhJAAD4SQAAdAAAAGhJAAAESgAAaQAAAGhJAAAQSgAAagAAAGhJAAAcSgAAbAAAAGhJAAAoSgAAbQAAAGhJAAA0SgAAeAAAAGhJAABASgAAeQAAAGhJAABMSgAAZgAAAGhJAABYSgAAZAAAAGhJAABkSgAAAAAAALBKAABoAwAAbQMAAGoDAABrAwAAbgMAAE4xMF9fY3h4YWJpdjExNl9fZW51bV90eXBlX2luZm9FAAAAAOxKAACMSgAAVEgAAAAAAACESAAAaAMAAG8DAABqAwAAawMAAHADAABxAwAAcgMAAHMDAAAAAAAANEsAAGgDAAB0AwAAagMAAGsDAABwAwAAdQMAAHYDAAB3AwAATjEwX19jeHhhYml2MTIwX19zaV9jbGFzc190eXBlX2luZm9FAAAAAOxKAAAMSwAAhEgAAAAAAACQSwAAaAMAAHgDAABqAwAAawMAAHADAAB5AwAAegMAAHsDAABOMTBfX2N4eGFiaXYxMjFfX3ZtaV9jbGFzc190eXBlX2luZm9FAAAA7EoAAGhLAACESAAAAAAAAORIAABoAwAAfAMAAGoDAABrAwAAfQMAAFN0OXR5cGVfaW5mbwAAAADESgAAuEs=");l(e,19408,"XEoAACBKAAAUSgAA6EsAAPBLAADoSwAAxEoAAMAnAADESgAAyCcAALRJAAAgSgAAzEkAAMxJAAAgSgAAtEkAACBKAABcSgAAXEoAACBKAAC0SQAAIEoAAOhLAAAgSgAAIEoAACBKAAC0SQAAIEoAACBKAAAgSgAAIEoAABRKAAAgSgAAFEoAACBKAAAAAAAAtEkAACBKAAAgSgAAIEoAAFxKAABcSgAAXEoAAMxJAAAISgAACEoAAPxJAAC0SQAAIEoAAJhMAADESgAAASgAAJhMAAAgSgAAzEkAACBKAADoSwAAwEwAACBKAAAUSgAAxEoAAAsoAAC0SQAAIEoAABRK");l(e,19680,"zEkAACBKAADwSwAA6EsAALRJAAAgSgAAXEoAAFxKAADoSwAAIEo=");l(e,19728,"tEkAACBKAAAgSgAAFEoAALRJAAAgSgAAXEoAAFxKAABcSgAAXEoAAFxKAAAgSgAAIEoAACBKAADoSwAAIEoAABRK");l(e,19808,"tEkAACBKAAAUSgAAXEoAAFxKAAB4SgAANSgAAHhKAABFKAAAxEoAAFIoAACkSwAAdigAAAAAAACETQAApEsAAJsoAAABAAAAhE0AAIxNAAC0SQAAjE0AABRKAAAAAAAAtEkAAIxNAAA4SgAAFEoAADhKAACcTQAA5E0AAIRNAAA4SgAAxEoAAMYoAAAAAAAAzEkAAIRNAAA4SgAAFEoAAMRKAADbKAAApEsAAAcpAAAAAAAAAE4AAKRLAAA0KQAAAQAAAABOAAAITgAAtEkAAAhOAADoSw==");l(e,20032,"tEkAAAhOAAA4SgAA6EsAADhKAAAYTgAA5E0AAABOAAA4Sg==");l(e,20080,"zEkAAABOAAA4SgAA6EsAAMRKAABiKQAAxEoAAGkpAADESgAAcikAAMRKAACDKQAAxEoAAJUpAADESgAAoikAAKRLAAC0KQAAAAAAAKhOAACkSwAAxykAAAEAAACoTgAAzEkAALBOAAAgSgAAxEoAAB4qAABISwAA9CkAAAAAAAACAAAAqE4AAAIAAADcTgAAAgQAAOxKAADbKQAA5E4AAKRLAABDKgAAAAAAAARPAACkSwAAXSoAAAEAAAAETwAAtEkAAARPAAAQTwAA5E0AAAAAAAAETwAAnwIAAKACAAChAgAAAAAAAOROAACfAgAAogIAAKMCAAC0SQAAzEkAACBKAADkTQAAgE8AAORNAADESgAAeCoAAMRKAAC3KgAApEsAAMsqAAAAAAAAiE8AAKRLAADgKgAAAQAAAIhPAABcSgAAkE8AACBKAADoSwAA6EsAAFxKAABISwAAGSsAAAAAAAACAAAAiE8AAAIAAADcTgAAAgQAAOxKAAD+KgAAyE8AAKRLAABFKwAAAAAAAOhPAACkSwAAYSsAAAEAAADoTwAAtEkAAOhPAAD0TwAA5E0AAAAAAADoTwAApAIAAKUCAACmAgAAAAAAAMhPAACkAgAApwIAAKMCAAAAAAAAXEoAACBKAADoSwAA6EsAAFxKAADESgAAfisAAKRLAACSKwAAAAAAAGRQAACkSwAApysAAAEAAABkUAAAtEkAAGxQAAAgSg==");l(e,20640,"tEkAAGxQAAAgSgAAIEoAAEhLAADYKwAAAAAAAAIAAABkUAAAAgAAANxOAAACBAAA7EoAAL0rAACwUAAAtEkAAOhQAAAgSgAApEsAAAQsAAAAAAAA0FAAAMxJAADoUAAAIEoAAKRLAAAgLAAAAQAAANBQAAC0SQAA0FAAAOhQAADkTQAAAAAAANBQAACoAgAAqQIAAKoCAACrAgAArAIAAK0CAAAAAAAAsFAAAK4CAACvAgAAsAIAALECAACyAgAAswIAALRJAAAgSgAAtEkAACBKAAAgSgAAxEoAAD0sAACkSwAARSwAAAAAAAB4UQAApEsAAE4sAAABAAAAeFEAALRJAACAUQAAIEoAACBKAACQUQ==");l(e,20928,"tEkAAIBRAAAgSgAAFEoAAIhO");l(e,20960,"tEkAAIBRAADoSwAAXEoAAIhO");l(e,20992,"tEkAAIBRAADoSwAAXEoAAOhLAACITg==");l(e,21024,"tEkAAIBRAADoSwAA6EsAAIhOAAC0SQAAgFEAAPBLAABISwAAfiwAAAAAAAACAAAAeFEAAAIAAADcTgAAAggAAOxKAABuLAAAQFIAAKRLAACeLAAAAAAAAGBSAACkSwAArywAAAEAAABgUgAAtEkAAGBSAABsUgAA5E0AAAAAAABgUgAAtAIAALUCAAC2AgAAtwIAALgCAAC5AgAAugIAALsCAAC8AgAAAAAAAEBSAAC0AgAAvQIAAKMCAACjAgAAowIAAKMCAACjAgAAowIAAKMC");l(e,21248,"tEkAACBKAAAUSgAAiE4AALRJAADoSwAAXEoAAIhOAAC0SQAA6EsAAFxKAADoSwAAiE4=");l(e,21312,"tEkAAOhLAADoSwAAiE4AALRJAADwSwAApEsAAMEsAAAAAAAAwEwAAKRLAADKLAAAAQAAAMBMAABYUwAAzEkAAGhTAADoSwAAaFMAAFxKAABoUwAAtEkAAFhTAABYUwAAtEkAAFhTAABYUwAAWFMAAMxJAABoUwAAwEwAAAAAAADMSQAAaFMAANBTAACQTgAApEsAANQsAAAAAAAAmE4AAMRKAADnLAAApEsAAPAsAAAAAAAA4FMAAKRLAAD6LAAAAQAAAOBTAADoUwAA6EsAALRJAADoUwAAbFAAALRJAADoUwAAgFEAALRJAADoUwAARFQAAOhTAABcVAAAxEoAAA4tAACkSwAABS0AAAAAAAA8VAAAxEoAACMtAACkSwAAFi0AAAEAAABUVAAAtEkAAOhTAABEVAAAjFQAAOhTAACkVAAAxEoAADgtAACkSwAALi0AAAAAAACEVAAAxEoAAFAtAACkSwAAQS0AAAEAAACcVAAAtEkAAOhTAACMVAAAtEkAAOhTAABcSgAAFEoAABRK");l(e,21728,"tEkAAPhTAACwTgAAwEwAALRJAAD4UwAAkE8AAOhLAADoSwAAtEkAAOhTAADMSQAAzEkAAPhTAAC0SQAA6FMAAOhLAADoSwAA+FMAAKRLAABkLQAAAAAAAFRUAAAsVQAApEsAAHAtAAABAAAAPFQAALRJAABEVAAA6EsAAFxKAADwSwAAQFUAAOhLAABAVQAAXEoAAEBVAAC0SQAARFQAAOhLAAC0SQAARFQAAFxKAAC0SQAARFQAAOhLAADoSwAAzEk=");l(e,21936,"tEkAAERUAADoSwAAzEkAALRJAABEVAAAXEoAAMxJAAC0SQAAQFUAANxVAACkSwAAhi0AAAAAAACgTgAAtEkAAERUAACgTgAAtEkAAERUAADoSwAAQFUAAOhLAAC0SQAARFQAAHxNAAB8TQAAQFUAALRJAABEVAAAzEkAAMxJAABAVQAAIEoAAERUAADoUwAARFQAAKRLAACULQAAAAAAAJxUAABEVgAAeEoAAKItAAC0SQAARFYAAERUAABEVAAARFYAAKRLAACwLQAAAQAAAIRUAABYVgAAdFYAAERUAACMVAAA6EsAAHRWAADoSwAAdFYAAFxKAABcSgAAdFYAAFxKAADMSQAAdFYAALRJAACMVAAA7EoAAMUtAACcVAAApEsAANotAAAAAAAAxFYAAKRLAADwLQAAAQAAAMRWAADQVgAApEsAAAcuAAAAAAAATDMAAKRLAAAaLgAAAQAAAEwzAADoSwAABFcAALRJAAD0VgAAXEoAAFxKAAAEVwAAtEkAAPRWAADsSgAALi4AAJxUAACkSwAAQC4AAAAAAAA4VwAApEsAAFMuAAABAAAAOFcAAERXAACkSwAAZy4AAAAAAAAYNQAApEsAAHcuAAABAAAAGDUAALRJAABoVwAA6EsAAOhLAAB4VwAAtEkAAGhXAABcSgAAXEoAAHhXAAC0SQAAaFcAAOxKAACILgAAnFQAAKRLAACaLgAAAAAAALhXAACkSwAArS4AAAEAAAC4VwAAxFcAAKRLAADBLgAAAAAAAGg1AACkSwAA0S4AAAEAAABoNQAAtEkAAOhXAADoSwAA6EsAAPhXAAC0SQAA6FcAAFxKAABcSgAA+FcAALRJAADoVwAA7EoAAOIuAACcVAAApEsAAPguAAAAAAAAOFgAAKRLAAAPLwAAAQAAADhYAABEWAAApEsAACcvAAAAAAAALDYAAKRLAAA7LwAAAQAAACw2AADoSwAAeFgAAFxKAAB4WAAAzEkAAHhYAAC0SQAAaFgAAMxJAAAAAAAAtEkAAGhYAABcSgAAXEoAALRJAABoWAAAXEoAAFxKAAB4WAAAXEoAALRJAABoWAAA7EoAAFAvAACcVAAApEsAAGUvAAAAAAAA4FgAAKRLAAB7LwAAAQAAAOBYAADsWAAApEsAAJIvAAAAAAAA0DYAAKRLAAClLwAAAQAAANA2AADoSwAAIFkAAFxKAAAgWQAAzEkAACBZAAC0SQAAEFkAAMxJ");l(e,22880,"tEkAABBZAABcSgAAXEoAALRJAAAQWQAAXEoAAFxKAAAgWQAAXEoAALRJAAAQWQAA7EoAALkvAACcVAAApEsAAMovAAAAAAAAkFkAAKRLAADcLwAAAQAAAJBZAACcWQAApEsAAO8vAAAAAAAAIDcAAKRLAAD+LwAAAQAAACA3AADoSwAA0FkAAOhLAADQWQAAXEoAAFxKAADQWQAAXEoAALRJAADAWQAAXEoAAFxKAADQWQAAtEkAAMBZAADsSgAADjAAAJxUAACkSwAAHzAAAAAAAAAcWgAApEsAADEwAAABAAAAHFoAAChaAACkSwAARDAAAAAAAABwNwAApEsAAFMwAAABAAAAcDcAAOhLAABcWgAAXEoAAFxaAAC0SQAATFoAAFxKAAC0SQAATFoAAOxKAABjMAAAnFQAAKRLAAB1MAAAAAAAAJBaAACkSwAAiDAAAAEAAACQWgAAnFoAAKRLAACcMAAAAAAAAMA3AACkSwAArDAAAAEAAADANwAA6EsAANBaAABcSgAA0FoAAMxJAADQWgAAtEkAAMBaAADMSQAAtEkAAMBaAABcSgAAXEoAANBaAABcSgAAtEkAAMBaAAACAAAABAAAAAAAAADUNwAA7DIAAAEAAAAAAAAABQ==");l(e,23372,"YwM=");l(e,23396,"ZAMAAGUDAADIXwAAAAQ=");l(e,23420,"AQ==");l(e,23436,"/////wo=");l(e,23504,"gGYB");}var r=new ArrayBuffer(16);var s=new Int32Array(r);var t=new Float32Array(r);var u=new Float64Array(r);function v(w){return s[w]}function x(w,y){s[w]=y;}function z(){return u[0]}function A(y){u[0]=y;}function B(){throw new Error("abort")}function C(y){t[2]=y;}function D(){return t[2]}function Pa(q){var E=q.a;var F=E.a;var G=F.buffer;F.grow=Na;var H=new Int8Array(G);var I=new Int16Array(G);var J=new Int32Array(G);var K=new Uint8Array(G);var L=new Uint16Array(G);var M=new Uint32Array(G);var N=new Float32Array(G);var O=new Float64Array(G);var P=Math.imul;var Q=Math.fround;var R=Math.abs;var S=Math.clz32;var V=Math.floor;var Y=Math.sqrt;var Z=E.b;var _=E.c;var $=E.d;var aa=E.e;var ba=E.f;var ca=E.g;var da=E.h;var ea=E.i;var fa=E.j;var ga=E.k;var ha=E.l;var ia=E.m;var ja=E.n;var ka=E.o;var la=E.p;var ma=E.q;var na=E.r;var oa=E.s;var pa=E.t;var qa=E.u;var ra=E.v;var sa=E.w;var ta=E.x;var ua=E.y;var va=E.z;var wa=E.A;var xa=E.B;var ya=E.C;var za=E.D;var Aa=E.E;var Ba=E.F;var Ca=E.G;var Da=E.H;var Ea=E.I;var Fa=91776;var Ga=0;
				// EMSCRIPTEN_START_FUNCS
				function Vd(){var a=0,b=0;oa(6069,18964,+J[5833]);oa(6055,18964,+J[5834]);oa(6083,18964,+J[5835]);oa(2475,18964,8);_(7014,3,19408,10171,1,2,0);_(6989,3,19420,10198,3,4,0);_(5457,3,19448,10203,5,6,0);_(4042,2,19460,10208,7,8,0);_(5523,3,19468,10212,9,10,0);_(5546,2,19480,10217,11,12,0);_(3325,3,19468,10212,9,13,0);_(3363,2,19480,10217,11,14,0);_(3304,2,19488,10221,15,16,0);_(3141,3,19468,10212,9,17,0);_(3185,2,19480,10217,11,18,0);_(3117,2,19488,10221,15,19,0);_(4749,2,19496,10208,20,21,0);_(6295,2,19504,10208,22,23,0);_(6168,2,19504,10208,22,24,0);_(5331,3,19512,10203,25,26,0);_(5312,2,19504,10208,22,27,0);_(4860,2,19504,10208,22,28,0);_(1677,2,19504,10208,22,29,0);_(2494,3,19524,10198,30,31,0);_(5969,2,19480,10217,11,32,0);_(5812,2,19480,10217,11,33,0);_(6026,2,19480,10217,11,34,0);_(5869,2,19480,10217,11,35,0);_(5898,2,19480,10217,11,36,0);_(5741,2,19480,10217,11,37,0);_(4655,2,19480,10217,11,38,0);_(4553,2,19480,10217,11,39,0);_(1471,1,19536,10225,40,41,0);_(5941,3,19408,10171,1,42,0);_(5784,3,19408,10171,1,43,0);_(4430,3,19408,10171,1,44,0);_(5997,2,19480,10217,11,45,0);_(5840,2,19480,10217,11,46,0);_(4533,2,19488,10221,15,47,0);_(4685,3,19408,10171,1,48,0);_(4584,3,19408,10171,1,49,0);_(1699,2,19540,10208,50,51,0);_(2939,2,19504,10208,22,52,0);_(1900,2,19504,10208,22,53,0);_(1501,2,19504,10208,22,54,0);_(1533,2,19504,10208,22,55,0);_(1426,1,19536,10225,40,56,0);_(4479,2,19488,10221,15,57,0);_(3619,11,19552,10228,58,59,0);_(4832,2,19504,10208,22,60,0);_(4886,2,19504,10208,22,61,0);_(2673,3,19448,10203,5,62,0);_(2690,2,19460,10208,7,63,0);_(5650,3,19596,10203,64,65,0);_(5671,2,19616,10208,66,67,0);_(2805,2,19488,10221,15,68,0);_(1253,2,19504,10208,22,69,0);_(1518,2,19504,10208,22,70,0);_(5711,2,19504,10208,22,71,0);_(5692,3,19512,10203,25,72,0);_(1763,3,19624,10198,73,74,0);_(1100,3,19468,10212,9,75,0);_(1118,2,19480,10217,11,76,0);_(3382,2,19480,10217,11,77,0);_(3344,3,19468,10212,9,78,0);_(3207,2,19480,10217,11,79,0);_(3163,3,19468,10212,9,80,0);_(6197,3,19636,10198,81,82,0);_(2989,3,19656,10203,83,84,0);_(2354,2,19480,10217,11,85,0);_(2339,3,19468,10212,9,86,0);_(4847,2,19504,10208,22,87,0);_(1734,2,19504,10208,22,88,0);_(1780,4,19680,10259,89,90,0);_(1456,1,19536,10225,40,91,0);_(4515,2,19488,10221,15,92,0);_(3238,4,19696,10265,93,94,0);_(3261,2,19712,10208,95,96,0);_(1440,1,19536,10225,40,97,0);_(4496,2,19488,10221,15,98,0);_(2259,4,19728,10271,99,100,0);_(1351,4,19696,10265,93,101,0);_(4994,7,19744,10277,102,103,0);_(4792,3,19772,10198,104,105,0);_(4716,3,19512,10203,25,106,0);_(1412,1,19536,10225,40,107,0);_(4462,2,19488,10221,15,108,0);_(3762,4,19696,10265,93,109,0);_(4264,2,19540,10208,50,110,0);_(5926,3,19408,10171,1,111,0);_(5769,3,19408,10171,1,112,0);_(2275,3,19784,10198,113,114,0);_(2522,2,19504,10208,22,115,0);_(4247,5,19808,10286,116,117,0);_(2971,2,19488,10221,15,118,0);va(19828,4876,4,0);la(19828,5033,0);la(19828,5144,1);la(19828,3523,2);la(19828,3562,3);la(19828,1722,4);va(19836,4815,4,0);la(19836,1279,0);la(19836,1293,1);la(19836,1310,2);aa(19844,19852,19868,0,10225,643,10433,0,10433,0,2604,10435,642);ga(19844,1,19884,10225,645,644);a=Ra(8);J[a+4>>2]=0;J[a>>2]=646;Z(19844,3689,3,19888,10203,647,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=648;Z(19844,4240,4,19904,10271,649,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=650;Z(19844,4259,2,19920,10208,651,a|0,0,0);a=Ra(4);J[a>>2]=652;Z(19844,2255,3,19928,10198,653,a|0,0,0);a=Ra(4);J[a>>2]=654;Z(19844,2202,4,19952,10259,655,a|0,0,0);ka(19432,7009,10457,120,10435,119);a=Ra(4);J[a>>2]=0;b=Ra(4);J[b>>2]=0;ca(19432,1410,19036,10217,122,a|0,19036,10212,121,b|0);a=Ra(4);J[a>>2]=4;b=Ra(4);J[b>>2]=4;ca(19432,1349,19036,10217,122,a|0,19036,10212,121,b|0);ja(19432);aa(19968,19976,19992,0,10225,657,10433,0,10433,0,2593,10435,656);ga(19968,1,20008,10225,659,658);a=Ra(8);J[a+4>>2]=0;J[a>>2]=660;Z(19968,3689,3,20012,10203,661,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=662;Z(19968,4240,4,20032,10271,663,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=664;Z(19968,4259,2,20048,10208,665,a|0,0,0);a=Ra(4);J[a>>2]=666;Z(19968,2255,3,20056,10198,667,a|0,0,0);a=Ra(4);J[a>>2]=668;Z(19968,2202,4,20080,10259,669,a|0,0,0);ka(20096,1628,10457,124,10435,123);a=Ra(4);J[a>>2]=0;b=Ra(4);J[b>>2]=0;ca(20096,2492,19036,10217,126,a|0,19036,10212,125,b|0);a=Ra(4);J[a>>2]=4;b=Ra(4);J[b>>2]=4;ca(20096,5608,19036,10217,126,a|0,19036,10212,125,b|0);ja(20096);ka(19440,3604,10457,128,10435,127);a=Ra(4);J[a>>2]=0;b=Ra(4);J[b>>2]=0;ca(19440,3015,19432,10208,130,a|0,19432,10203,129,b|0);a=Ra(4);J[a>>2]=8;b=Ra(4);J[b>>2]=8;ca(19440,2987,20096,10208,132,a|0,20096,10203,131,b|0);ja(19440);ka(20104,2706,10457,134,10435,133);a=Ra(4);J[a>>2]=0;b=Ra(4);J[b>>2]=0;ca(20104,2985,19036,10217,136,a|0,19036,10212,135,b|0);a=Ra(4);J[a>>2]=4;b=Ra(4);J[b>>2]=4;ca(20104,4058,19036,10217,136,a|0,19036,10212,135,b|0);a=Ra(4);J[a>>2]=8;b=Ra(4);J[b>>2]=8;ca(20104,5610,19036,10217,136,a|0,19036,10212,135,b|0);a=Ra(4);J[a>>2]=12;b=Ra(4);J[b>>2]=12;ca(20104,5739,19036,10217,136,a|0,19036,10212,135,b|0);ja(20104);ka(20112,1564,10457,138,10435,137);a=Ra(4);J[a>>2]=0;b=Ra(4);J[b>>2]=0;ca(20112,7025,19432,10208,140,a|0,19432,10203,139,b|0);a=Ra(4);J[a>>2]=8;b=Ra(4);J[b>>2]=8;ca(20112,7006,19432,10208,140,a|0,19432,10203,139,b|0);a=Ra(4);J[a>>2]=16;b=Ra(4);J[b>>2]=16;ca(20112,3410,19036,10217,142,a|0,19036,10212,141,b|0);ja(20112);ka(20120,1550,10457,144,10435,143);a=Ra(4);J[a>>2]=0;b=Ra(4);J[b>>2]=0;ca(20120,3652,19432,10208,146,a|0,19432,10203,145,b|0);a=Ra(4);J[a>>2]=8;b=Ra(4);J[b>>2]=8;ca(20120,3401,19036,10217,148,a|0,19036,10212,147,b|0);ja(20120);ka(20128,5641,10457,150,10435,149);a=Ra(4);J[a>>2]=0;b=Ra(4);J[b>>2]=0;ca(20128,2412,19036,10217,152,a|0,19036,10212,151,b|0);a=Ra(4);J[a>>2]=4;b=Ra(4);J[b>>2]=4;ca(20128,2712,19432,10208,154,a|0,19432,10203,153,b|0);a=Ra(4);J[a>>2]=12;b=Ra(4);J[b>>2]=12;ca(20128,6104,19036,10217,152,a|0,19036,10212,151,b|0);ja(20128);ka(19608,2821,10457,156,10435,155);a=Ra(4);J[a>>2]=0;b=Ra(4);J[b>>2]=0;ca(19608,2390,18952,10208,158,a|0,18952,10203,157,b|0);a=Ra(4);J[a>>2]=2;b=Ra(4);J[b>>2]=2;ca(19608,2403,18952,10208,158,a|0,18952,10203,157,b|0);a=Ra(4);J[a>>2]=4;b=Ra(4);J[b>>2]=4;ca(19608,1372,18940,10208,160,a|0,18940,10203,159,b|0);ja(19608);aa(20136,20144,20160,0,10225,162,10433,0,10433,0,3659,10435,161);a=Ra(8);J[a>>2]=8;J[a+4>>2]=1;Z(20136,4735,3,20176,10198,163,a|0,0,0);aa(20228,20240,20256,20136,10225,167,10225,166,10225,165,2876,10435,164);a=Ra(4);J[a>>2]=168;Z(20228,3284,2,20272,10221,169,a|0,0,0);ia(20136,2051,2,20280,10208,171,170,0);ia(20136,5296,3,20340,10198,173,172,0);aa(20360,20368,20384,0,10225,175,10433,0,10433,0,3673,10435,174);a=Ra(8);J[a>>2]=8;J[a+4>>2]=1;Z(20360,4735,6,20400,10998,176,a|0,1,0);aa(20456,20468,20484,20360,10225,180,10225,179,10225,178,2897,10435,177);a=Ra(4);J[a>>2]=181;Z(20456,3284,2,20500,10221,182,a|0,0,0);ia(20360,2051,2,20508,10208,184,183,0);ia(20360,5296,3,20340,10198,173,185,0);aa(20580,20588,20604,0,10225,187,10433,0,10433,0,2923,10435,186);a=Ra(8);J[a>>2]=8;J[a+4>>2]=1;Z(20580,2300,3,20620,10203,188,a|0,0,0);a=Ra(8);J[a>>2]=12;J[a+4>>2]=1;Z(20580,2313,3,20620,10203,188,a|0,0,0);a=Ra(8);J[a>>2]=16;J[a+4>>2]=1;Z(20580,4289,4,20640,10271,189,a|0,0,0);a=Ra(8);J[a>>2]=20;J[a+4>>2]=1;Z(20580,4279,4,20640,10271,189,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=190;Z(20688,4769,3,20700,10203,191,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=192;Z(20688,4767,3,20700,10203,191,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=193;Z(20688,4192,3,20728,10198,194,a|0,0,0);aa(20688,20712,20740,20580,10225,198,10225,197,10225,196,2853,10435,195);a=Ra(4);J[a>>2]=199;Z(20688,3284,2,20756,10221,200,a|0,0,0);ia(20580,2051,2,20764,10208,202,201,0);ia(20580,5296,3,20340,10198,173,203,0);aa(20856,20864,20880,0,10225,205,10433,0,10433,0,1496,10435,204);a=Ra(8);J[a+4>>2]=0;J[a>>2]=206;Z(20856,2434,3,20896,10203,207,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=208;Z(20856,2443,2,20908,10208,209,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=210;Z(20856,2463,3,20896,10203,207,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=211;Z(20856,2452,3,20896,10203,207,a|0,0,0);a=Ra(8);J[a>>2]=8;J[a+4>>2]=1;Z(20856,3533,5,20928,11352,212,a|0,0,0);a=Ra(8);J[a>>2]=12;J[a+4>>2]=1;Z(20856,3545,5,20928,11352,212,a|0,0,0);a=Ra(8);J[a>>2]=16;J[a+4>>2]=1;Z(20856,5042,5,20960,11359,213,a|0,0,0);a=Ra(8);J[a>>2]=20;J[a+4>>2]=1;Z(20856,5053,6,20992,11366,214,a|0,0,0);a=Ra(8);J[a>>2]=24;J[a+4>>2]=1;Z(20856,2039,5,21024,11352,215,a|0,0,0);a=Ra(8);J[a>>2]=28;J[a+4>>2]=1;Z(20856,3574,3,21044,10203,216,a|0,0,0);a=Ra(8);J[a>>2]=32;J[a+4>>2]=1;Z(20856,1753,5,20960,11359,213,a|0,0,0);aa(21088,21100,21116,20856,10225,220,10225,219,10225,218,2841,10435,217);a=Ra(4);J[a>>2]=221;Z(21088,3284,2,21132,10221,222,a|0,0,0);ia(20856,2051,2,21140,10208,224,223,0);ia(20856,5296,3,20340,10198,173,225,0);aa(19648,21336,21352,0,10225,227,10433,0,10433,0,6207,10435,226);ga(19648,1,21368,10225,229,228);a=Ra(8);J[a+4>>2]=0;J[a>>2]=230;Z(19648,5360,2,21372,10208,231,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=232;Z(19648,2719,2,21380,10208,233,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=234;Z(19648,2369,2,21380,10208,233,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=235;Z(19648,2828,2,21388,10217,236,a|0,0,0);a=Ra(4);J[a>>2]=237;Z(19648,4902,3,21396,10203,238,a|0,0,0);a=Ra(4);J[a>>2]=239;Z(19648,3017,4,21408,10271,240,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=241;Z(19648,2425,3,21424,10198,242,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=243;Z(19648,1605,4,21440,10259,244,a|0,0,0);a=Ra(4);J[a>>2]=0;b=Ra(4);J[b>>2]=0;$(19648,5274,19432,10208,246,a|0,19432,10203,245,b|0);a=Ra(4);J[a>>2]=8;b=Ra(4);J[b>>2]=8;$(19648,5285,19432,10208,246,a|0,19432,10203,245,b|0);aa(21472,21480,21496,0,10225,248,10433,0,10433,0,5306,10435,247);ga(21472,2,21512,10208,250,249);a=Ra(8);J[a+4>>2]=0;J[a>>2]=251;Z(21472,2920,3,21520,10203,252,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=253;Z(21472,1488,3,21532,10203,254,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=255;Z(21472,1491,2,21544,10221,256,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=257;Z(21472,1268,3,21552,10198,258,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=259;Z(21472,1241,3,21612,10203,260,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=261;Z(21472,1954,3,21624,10198,262,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=263;Z(21472,1887,3,21684,10203,264,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=265;Z(21472,3001,5,21696,11613,266,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=267;Z(21472,6187,4,21728,10271,268,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=269;Z(21472,1605,5,21744,11352,270,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=271;Z(21472,4008,3,21764,10203,272,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=273;Z(21472,4025,2,21776,10208,274,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=275;Z(21472,1078,3,21784,10203,276,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=277;Z(21472,1089,2,21796,10208,278,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=279;Z(21472,5500,2,21776,10208,274,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=280;Z(21472,2996,2,21544,10221,256,a|0,0,0);aa(21588,21804,21596,0,10225,282,10433,0,10433,0,4064,10435,281);ga(21588,1,21820,10225,284,283);a=Ra(4);J[a>>2]=0;b=Ra(4);J[b>>2]=0;$(21588,4810,19836,10208,286,a|0,19836,10203,285,b|0);a=Ra(4);J[a>>2]=4;b=Ra(4);J[b>>2]=4;$(21588,3229,19432,10208,288,a|0,19432,10203,287,b|0);a=Ra(4);J[a>>2]=12;b=Ra(4);J[b>>2]=12;$(21588,4910,19036,10217,290,a|0,19036,10212,289,b|0);a=Ra(4);J[a>>2]=16;b=Ra(4);J[b>>2]=16;$(21588,1190,19432,10208,288,a|0,19432,10203,287,b|0);a=Ra(4);J[a>>2]=24;b=Ra(4);J[b>>2]=24;$(21588,1136,19036,10217,290,a|0,19036,10212,289,b|0);a=Ra(4);J[a>>2]=28;b=Ra(4);J[b>>2]=28;$(21588,3960,19036,10217,290,a|0,19036,10212,289,b|0);a=Ra(4);J[a>>2]=32;b=Ra(4);J[b>>2]=32;$(21588,3909,19036,10217,290,a|0,19036,10212,289,b|0);a=Ra(4);J[a>>2]=36;b=Ra(4);J[b>>2]=36;$(21588,3006,18892,10208,292,a|0,18892,10203,291,b|0);a=Ra(4);J[a>>2]=37;b=Ra(4);J[b>>2]=37;$(21588,5121,18892,10208,292,a|0,18892,10203,291,b|0);a=Ra(4);J[a>>2]=38;b=Ra(4);J[b>>2]=38;$(21588,3422,18892,10208,292,a|0,18892,10203,291,b|0);a=Ra(4);J[a>>2]=39;b=Ra(4);J[b>>2]=39;$(21588,2206,18892,10208,292,a|0,18892,10203,291,b|0);a=Ra(4);J[a>>2]=48;b=Ra(4);J[b>>2]=48;$(21588,5076,19036,10217,290,a|0,19036,10212,289,b|0);aa(21564,21572,21824,0,10225,294,10433,0,10433,0,1320,10435,293);a=Ra(8);J[a+4>>2]=0;J[a>>2]=295;Z(21564,3588,4,21840,11642,296,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=297;Z(21564,3601,2,21856,10208,298,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=299;Z(21564,3272,2,21864,10208,300,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=301;Z(21564,4930,2,21872,10217,302,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=303;Z(21564,2790,2,21864,10208,300,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=304;Z(21564,2775,2,21864,10208,300,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=305;Z(21564,1205,3,21880,10203,306,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=307;Z(21564,1223,2,21864,10208,300,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=308;Z(21564,1152,3,21892,10212,309,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=310;Z(21564,1171,2,21872,10217,302,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=311;Z(21564,5151,5,21904,11352,312,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=313;Z(21564,2756,4,21936,10271,314,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=315;Z(21564,4298,4,21952,11648,316,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=317;Z(21564,4636,5,21904,11352,312,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=318;Z(21564,2729,4,21936,10271,314,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=319;Z(21564,4616,4,21952,11648,316,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=320;Z(21564,2417,2,21872,10217,302,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=321;Z(21564,5730,2,21872,10217,302,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=322;Z(21564,5638,3,21968,10203,323,a|0,0,0);a=Ra(4);J[a>>2]=324;Z(21564,5626,3,21996,10203,325,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=326;Z(21564,5612,2,22008,10221,327,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=328;Z(21564,1841,3,22016,10198,329,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=330;Z(21564,2578,3,22016,10198,329,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=331;Z(21564,1795,3,22016,10198,329,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=332;Z(21564,2563,3,22016,10198,329,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=333;Z(21564,1855,3,22016,10198,329,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=334;Z(21564,1809,3,22016,10198,329,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=335;Z(21564,3991,2,21872,10217,302,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=336;Z(21564,3974,3,21892,10212,309,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=337;Z(21564,3942,2,21872,10217,302,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=338;Z(21564,3924,3,21892,10212,309,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=339;Z(21564,5105,2,21872,10217,302,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=340;Z(21564,5089,3,21892,10212,309,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=341;Z(21564,4824,3,22028,10203,342,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=343;Z(21564,4868,2,22040,10208,344,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=345;Z(21564,2213,3,22048,10203,346,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=347;Z(21564,2223,2,22060,10208,348,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=349;Z(21564,5368,3,22048,10203,346,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=350;Z(21564,5387,2,22060,10208,348,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=351;Z(21564,5127,3,22048,10203,346,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=352;Z(21564,5136,2,22060,10208,348,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=353;Z(21564,5464,3,22048,10203,346,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=354;Z(21564,5475,2,22060,10208,348,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=355;Z(21564,3436,3,22048,10203,346,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=356;Z(21564,3453,2,22060,10208,348,a|0,0,0);a=Ra(4);J[a>>2]=357;Z(21564,1590,2,22068,10208,358,a|0,0,0);a=Ra(4);J[a>>2]=359;Z(21564,1577,2,22068,10208,358,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=360;Z(21564,5303,2,22076,10208,361,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=362;Z(21564,2996,2,22008,10221,327,a|0,0,0);aa(21660,22084,21668,0,10225,364,10433,0,10433,0,4183,10435,363);ga(21660,1,22100,10225,366,365);a=Ra(4);J[a>>2]=0;b=Ra(4);J[b>>2]=0;$(21660,4810,22104,10208,368,a|0,22104,10203,367,b|0);a=Ra(4);J[a>>2]=369;Z(21660,6212,3,22112,10203,370,a|0,0,0);a=Ra(4);J[a>>2]=371;Z(21660,6221,2,22124,10208,372,a|0,0,0);a=Ra(4);J[a>>2]=373;Z(21660,6110,3,22112,10203,370,a|0,0,0);a=Ra(4);J[a>>2]=374;Z(21660,6119,2,22124,10208,372,a|0,0,0);a=Ra(4);J[a>>2]=16;b=Ra(4);J[b>>2]=16;$(21660,5405,18892,10208,376,a|0,18892,10203,375,b|0);aa(21636,21644,22132,0,10225,378,10433,0,10433,0,2020,10435,377);a=Ra(8);J[a+4>>2]=0;J[a>>2]=379;Z(21636,4868,2,22148,10208,380,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=381;Z(21636,6221,2,22156,10208,382,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=383;Z(21636,6119,2,22156,10208,382,a|0,0,0);a=Ra(8);J[a+4>>2]=1;J[a>>2]=0;Z(21636,6255,2,22164,10208,384,a|0,0,0);a=Ra(8);J[a+4>>2]=1;J[a>>2]=4;Z(21636,6128,2,22164,10208,384,a|0,0,0);a=Ra(8);J[a+4>>2]=1;J[a>>2]=8;Z(21636,5257,3,22172,11707,385,a|0,0,0);a=Ra(8);J[a+4>>2]=1;J[a>>2]=12;Z(21636,4412,3,22184,11712,386,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=387;Z(21636,5422,2,22196,10208,388,a|0,0,0);a=Ra(8);J[a+4>>2]=1;J[a>>2]=16;Z(21636,2996,2,22204,10221,389,a|0,0,0);aa(22212,22224,22240,21660,10225,393,10225,392,10225,391,4144,10435,390);ga(22212,1,22256,10225,395,394);a=Ra(4);J[a>>2]=20;b=Ra(4);J[b>>2]=20;$(22212,6266,19432,10208,397,a|0,19432,10203,396,b|0);a=Ra(4);J[a>>2]=28;b=Ra(4);J[b>>2]=28;$(22212,6139,19432,10208,397,a|0,19432,10203,396,b|0);a=Ra(4);J[a>>2]=36;b=Ra(4);J[b>>2]=36;$(22212,3699,19036,10217,399,a|0,19036,10212,398,b|0);a=Ra(4);J[a>>2]=40;b=Ra(4);J[b>>2]=40;$(22212,1024,19036,10217,399,a|0,19036,10212,398,b|0);a=Ra(4);J[a>>2]=44;b=Ra(4);J[b>>2]=44;$(22212,3028,19036,10217,399,a|0,19036,10212,398,b|0);aa(13132,22260,22276,21636,10225,403,10225,402,10225,401,1987,10435,400);a=Ra(8);J[a+4>>2]=0;J[a>>2]=404;Z(13132,6279,2,22292,10208,405,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=406;Z(13132,6152,2,22292,10208,405,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=407;Z(13132,3742,3,22300,10212,408,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=409;Z(13132,3752,2,22312,10217,410,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=411;Z(13132,1325,3,22300,10212,408,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=412;Z(13132,1338,2,22312,10217,410,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=413;Z(13132,3041,3,22300,10212,408,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=414;Z(13132,3057,2,22312,10217,410,a|0,0,0);a=Ra(8);J[a+4>>2]=1;J[a>>2]=16;Z(13132,2996,2,22320,10221,415,a|0,0,0);aa(22328,22340,22356,21660,10225,419,10225,418,10225,417,4072,10435,416);ga(22328,1,22372,10225,421,420);a=Ra(4);J[a>>2]=20;b=Ra(4);J[b>>2]=20;$(22328,2161,19432,10208,423,a|0,19432,10203,422,b|0);a=Ra(4);J[a>>2]=28;b=Ra(4);J[b>>2]=28;$(22328,2113,19036,10217,425,a|0,19036,10212,424,b|0);a=Ra(4);J[a>>2]=32;b=Ra(4);J[b>>2]=32;$(22328,5162,19036,10217,425,a|0,19036,10212,424,b|0);a=Ra(4);J[a>>2]=36;b=Ra(4);J[b>>2]=36;$(22328,4310,19036,10217,425,a|0,19036,10212,424,b|0);a=Ra(4);J[a>>2]=40;b=Ra(4);J[b>>2]=40;$(22328,2616,19036,10217,425,a|0,19036,10212,424,b|0);aa(13592,22376,22392,21636,10225,429,10225,428,10225,427,1918,10435,426);a=Ra(8);J[a+4>>2]=0;J[a>>2]=430;Z(13592,2174,3,22408,10203,431,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=432;Z(13592,2190,2,22420,10208,433,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=434;Z(13592,2127,3,22428,10212,435,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=436;Z(13592,2144,2,22440,10217,437,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=438;Z(13592,5171,3,22428,10212,435,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=439;Z(13592,5183,2,22440,10217,437,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=440;Z(13592,4320,3,22428,10212,435,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=441;Z(13592,4333,2,22440,10217,437,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=442;Z(13592,2633,3,22428,10212,435,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=443;Z(13592,2653,2,22440,10217,437,a|0,0,0);a=Ra(8);J[a+4>>2]=1;J[a>>2]=16;Z(13592,2996,2,22448,10221,444,a|0,0,0);aa(22456,22468,22484,21660,10225,448,10225,447,10225,446,4117,10435,445);ga(22456,1,22500,10225,450,449);a=Ra(4);J[a>>2]=20;b=Ra(4);J[b>>2]=20;$(22456,2232,19432,10208,452,a|0,19432,10203,451,b|0);a=Ra(4);J[a>>2]=28;b=Ra(4);J[b>>2]=28;$(22456,5162,19036,10217,454,a|0,19036,10212,453,b|0);a=Ra(4);J[a>>2]=32;b=Ra(4);J[b>>2]=32;$(22456,1024,19036,10217,454,a|0,19036,10212,453,b|0);a=Ra(4);J[a>>2]=36;b=Ra(4);J[b>>2]=36;$(22456,3028,19036,10217,454,a|0,19036,10212,453,b|0);aa(13672,22504,22520,21636,10225,458,10225,457,10225,456,1966,10435,455);a=Ra(8);J[a+4>>2]=0;J[a>>2]=459;Z(13672,2239,3,22536,10203,460,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=461;Z(13672,2249,2,22548,10208,462,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=463;Z(13672,5171,3,22556,10212,464,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=465;Z(13672,5183,2,22568,10217,466,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=467;Z(13672,1325,3,22556,10212,464,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=468;Z(13672,1338,2,22568,10217,466,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=469;Z(13672,3041,3,22556,10212,464,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=470;Z(13672,3057,2,22568,10217,466,a|0,0,0);a=Ra(8);J[a+4>>2]=1;J[a>>2]=16;Z(13672,2996,2,22576,10221,471,a|0,0,0);aa(22584,22596,22612,21660,10225,475,10225,474,10225,473,4174,10435,472);ga(22584,1,22628,10225,477,476);a=Ra(4);J[a>>2]=20;b=Ra(4);J[b>>2]=20;$(22584,6266,19432,10208,479,a|0,19432,10203,478,b|0);a=Ra(4);J[a>>2]=28;b=Ra(4);J[b>>2]=28;$(22584,6139,19432,10208,479,a|0,19432,10203,478,b|0);a=Ra(4);J[a>>2]=36;b=Ra(4);J[b>>2]=36;$(22584,6230,19432,10208,479,a|0,19432,10203,478,b|0);a=Ra(4);J[a>>2]=44;b=Ra(4);J[b>>2]=44;$(22584,4961,19036,10217,481,a|0,19036,10212,480,b|0);a=Ra(4);J[a>>2]=48;b=Ra(4);J[b>>2]=48;$(22584,2089,18892,10208,483,a|0,18892,10203,482,b|0);a=Ra(4);J[a>>2]=52;b=Ra(4);J[b>>2]=52;$(22584,3489,19036,10217,481,a|0,19036,10212,480,b|0);a=Ra(4);J[a>>2]=56;b=Ra(4);J[b>>2]=56;$(22584,3506,19036,10217,481,a|0,19036,10212,480,b|0);a=Ra(4);J[a>>2]=60;b=Ra(4);J[b>>2]=60;$(22584,2539,18892,10208,483,a|0,18892,10203,482,b|0);a=Ra(4);J[a>>2]=68;b=Ra(4);J[b>>2]=68;$(22584,5569,19036,10217,481,a|0,19036,10212,480,b|0);a=Ra(4);J[a>>2]=64;b=Ra(4);J[b>>2]=64;$(22584,5195,19036,10217,481,a|0,19036,10212,480,b|0);aa(13868,22632,22648,21636,10225,487,10225,486,10225,485,2011,10435,484);a=Ra(8);J[a+4>>2]=0;J[a>>2]=488;Z(13868,6279,2,22664,10208,489,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=490;Z(13868,6152,2,22664,10208,489,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=491;Z(13868,6241,2,22664,10208,489,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=492;Z(13868,4976,2,22672,10217,493,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=494;Z(13868,3469,2,22672,10217,493,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=495;Z(13868,5509,2,22672,10217,493,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=496;Z(13868,5442,2,22680,10208,497,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=498;Z(13868,2101,3,22688,10203,499,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=500;Z(13868,2061,2,22672,10217,493,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=501;Z(13868,2075,2,22672,10217,493,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=502;Z(13868,2380,4,22704,10265,503,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=504;Z(13868,5485,2,22680,10208,497,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=505;Z(13868,2551,3,22688,10203,499,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=506;Z(13868,5580,3,22720,10212,507,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=508;Z(13868,5594,2,22672,10217,493,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=509;Z(13868,5209,3,22720,10212,507,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=510;Z(13868,5226,2,22672,10217,493,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=511;Z(13868,5243,3,22732,11712,512,a|0,0,0);a=Ra(8);J[a+4>>2]=1;J[a>>2]=16;Z(13868,2996,2,22744,10221,513,a|0,0,0);aa(22752,22764,22780,21660,10225,517,10225,516,10225,515,4100,10435,514);ga(22752,1,22796,10225,519,518);a=Ra(4);J[a>>2]=20;b=Ra(4);J[b>>2]=20;$(22752,6266,19432,10208,521,a|0,19432,10203,520,b|0);a=Ra(4);J[a>>2]=28;b=Ra(4);J[b>>2]=28;$(22752,6139,19432,10208,521,a|0,19432,10203,520,b|0);a=Ra(4);J[a>>2]=36;b=Ra(4);J[b>>2]=36;$(22752,4961,19036,10217,523,a|0,19036,10212,522,b|0);a=Ra(4);J[a>>2]=40;b=Ra(4);J[b>>2]=40;$(22752,2089,18892,10208,525,a|0,18892,10203,524,b|0);a=Ra(4);J[a>>2]=44;b=Ra(4);J[b>>2]=44;$(22752,4939,19036,10217,523,a|0,19036,10212,522,b|0);a=Ra(4);J[a>>2]=48;b=Ra(4);J[b>>2]=48;$(22752,4950,19036,10217,523,a|0,19036,10212,522,b|0);a=Ra(4);J[a>>2]=52;b=Ra(4);J[b>>2]=52;$(22752,2539,18892,10208,525,a|0,18892,10203,524,b|0);a=Ra(4);J[a>>2]=56;b=Ra(4);J[b>>2]=56;$(22752,5569,19036,10217,523,a|0,19036,10212,522,b|0);a=Ra(4);J[a>>2]=60;b=Ra(4);J[b>>2]=60;$(22752,4346,19036,10217,523,a|0,19036,10212,522,b|0);aa(14032,22800,22816,21636,10225,529,10225,528,10225,527,1940,10435,526);a=Ra(8);J[a+4>>2]=0;J[a>>2]=530;Z(14032,6279,2,22832,10208,531,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=532;Z(14032,6152,2,22832,10208,531,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=533;Z(14032,4976,2,22840,10217,534,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=535;Z(14032,4916,2,22840,10217,534,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=536;Z(14032,5509,2,22840,10217,534,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=537;Z(14032,5442,2,22848,10208,538,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=539;Z(14032,2101,3,22856,10203,540,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=541;Z(14032,2061,2,22840,10217,534,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=542;Z(14032,2075,2,22840,10217,534,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=543;Z(14032,2380,4,22880,10265,544,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=545;Z(14032,5485,2,22848,10208,538,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=546;Z(14032,2551,3,22856,10203,540,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=547;Z(14032,5580,3,22896,10212,548,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=549;Z(14032,5594,2,22840,10217,534,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=550;Z(14032,4361,3,22896,10212,548,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=551;Z(14032,4379,2,22840,10217,534,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=552;Z(14032,4397,3,22908,11712,553,a|0,0,0);a=Ra(8);J[a+4>>2]=1;J[a>>2]=16;Z(14032,2996,2,22920,10221,554,a|0,0,0);aa(22928,22940,22956,21660,10225,558,10225,557,10225,556,4131,10435,555);ga(22928,1,22972,10225,560,559);a=Ra(4);J[a>>2]=20;b=Ra(4);J[b>>2]=20;$(22928,6266,19432,10208,562,a|0,19432,10203,561,b|0);a=Ra(4);J[a>>2]=28;b=Ra(4);J[b>>2]=28;$(22928,6139,19432,10208,562,a|0,19432,10203,561,b|0);a=Ra(4);J[a>>2]=36;b=Ra(4);J[b>>2]=36;$(22928,3706,19036,10217,564,a|0,19036,10212,563,b|0);aa(14112,22976,22992,21636,10225,568,10225,567,10225,566,1977,10435,565);a=Ra(8);J[a+4>>2]=0;J[a>>2]=569;Z(14112,6279,2,23008,10208,570,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=571;Z(14112,6152,2,23008,10208,570,a|0,0,0);a=Ra(8);J[a+4>>2]=1;J[a>>2]=8;Z(14112,5257,3,23016,11707,572,a|0,0,0);a=Ra(8);J[a+4>>2]=1;J[a>>2]=12;Z(14112,4412,3,23028,11712,573,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=574;Z(14112,3716,3,23040,10212,575,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=576;Z(14112,3729,2,23052,10217,577,a|0,0,0);a=Ra(8);J[a+4>>2]=1;J[a>>2]=16;Z(14112,2996,2,23060,10221,578,a|0,0,0);aa(23068,23080,23096,21660,10225,582,10225,581,10225,580,4161,10435,579);ga(23068,1,23112,10225,584,583);a=Ra(4);J[a>>2]=20;b=Ra(4);J[b>>2]=20;$(23068,6266,19432,10208,586,a|0,19432,10203,585,b|0);a=Ra(4);J[a>>2]=28;b=Ra(4);J[b>>2]=28;$(23068,6139,19432,10208,586,a|0,19432,10203,585,b|0);a=Ra(4);J[a>>2]=36;b=Ra(4);J[b>>2]=36;$(23068,4961,19036,10217,588,a|0,19036,10212,587,b|0);a=Ra(4);J[a>>2]=40;b=Ra(4);J[b>>2]=40;$(23068,1024,19036,10217,588,a|0,19036,10212,587,b|0);a=Ra(4);J[a>>2]=44;b=Ra(4);J[b>>2]=44;$(23068,3028,19036,10217,588,a|0,19036,10212,587,b|0);aa(14192,23116,23132,21636,10225,592,10225,591,10225,590,2001,10435,589);a=Ra(8);J[a+4>>2]=0;J[a>>2]=593;Z(14192,6279,2,23148,10208,594,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=595;Z(14192,6152,2,23148,10208,594,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=596;Z(14192,4976,2,23156,10217,597,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=598;Z(14192,1325,3,23164,10212,599,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=600;Z(14192,1338,2,23156,10217,597,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=601;Z(14192,3041,3,23164,10212,599,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=602;Z(14192,3057,2,23156,10217,597,a|0,0,0);a=Ra(8);J[a+4>>2]=1;J[a>>2]=16;Z(14192,2996,2,23176,10221,603,a|0,0,0);aa(23184,23196,23212,21660,10225,607,10225,606,10225,605,4086,10435,604);ga(23184,1,23228,10225,609,608);a=Ra(4);J[a>>2]=20;b=Ra(4);J[b>>2]=20;$(23184,6266,19432,10208,611,a|0,19432,10203,610,b|0);a=Ra(4);J[a>>2]=28;b=Ra(4);J[b>>2]=28;$(23184,6139,19432,10208,611,a|0,19432,10203,610,b|0);a=Ra(4);J[a>>2]=36;b=Ra(4);J[b>>2]=36;$(23184,6230,19432,10208,611,a|0,19432,10203,610,b|0);a=Ra(4);J[a>>2]=44;b=Ra(4);J[b>>2]=44;$(23184,2539,18892,10208,613,a|0,18892,10203,612,b|0);a=Ra(4);J[a>>2]=48;b=Ra(4);J[b>>2]=48;$(23184,4346,19036,10217,615,a|0,19036,10212,614,b|0);a=Ra(4);J[a>>2]=52;b=Ra(4);J[b>>2]=52;$(23184,5569,19036,10217,615,a|0,19036,10212,614,b|0);a=Ra(4);J[a>>2]=56;b=Ra(4);J[b>>2]=56;$(23184,1024,19036,10217,615,a|0,19036,10212,614,b|0);a=Ra(4);J[a>>2]=60;b=Ra(4);J[b>>2]=60;$(23184,3028,19036,10217,615,a|0,19036,10212,614,b|0);aa(14272,23232,23248,21636,10225,619,10225,618,10225,617,1929,10435,616);a=Ra(8);J[a+4>>2]=0;J[a>>2]=620;Z(14272,6279,2,23264,10208,621,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=622;Z(14272,6152,2,23264,10208,621,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=623;Z(14272,6241,2,23264,10208,621,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=624;Z(14272,3469,2,23272,10217,625,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=626;Z(14272,5485,2,23280,10208,627,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=628;Z(14272,2551,3,23288,10203,629,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=630;Z(14272,5580,3,23300,10212,631,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=632;Z(14272,5594,2,23272,10217,625,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=633;Z(14272,4361,3,23300,10212,631,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=634;Z(14272,4379,2,23272,10217,625,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=635;Z(14272,4397,3,23312,11712,636,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=637;Z(14272,1036,3,23300,10212,631,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=638;Z(14272,1057,2,23272,10217,625,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=639;Z(14272,3073,3,23300,10212,631,a|0,0,0);a=Ra(8);J[a+4>>2]=0;J[a>>2]=640;Z(14272,3095,2,23272,10217,625,a|0,0,0);a=Ra(8);J[a+4>>2]=1;J[a>>2]=16;Z(14272,2996,2,23324,10221,641,a|0,0,0);}function vf(a,b,c,d){a=a|0;b=Q(b);c=c|0;d=d|0;var e=0,f=0,g=Q(0),h=0,i=0,j=0,k=Q(0),l=0,m=0,n=Q(0),o=Q(0),p=0,q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=0,x=0,y=Q(0),z=0,A=0,B=Q(0),C=Q(0),D=Q(0),E=Q(0),F=Q(0),G=Q(0),M=Q(0),O=0,R=Q(0),S=0,T=0,U=Q(0),W=Q(0),X=Q(0),Z=0,_=Q(0),$=0,aa=0,ba=0,ca=Q(0),da=Q(0),ea=Q(0),fa=Q(0),ga=0,ha=0,ia=Q(0);w=Fa-32|0;Fa=w;if(K[a+102988|0]){Ub(a+102868|0);H[a+102988|0]=0;}H[a+102989|0]=1;J[w+20>>2]=d;J[w+16>>2]=c;N[w+4>>2]=b;m=b>Q(0);N[w+8>>2]=m?Q(Q(1)/b):Q(0);N[w+12>>2]=N[a+102984>>2]*b;H[w+24|0]=K[a+102991|0];f=a+102868|0;c=J[f+60>>2];if(c){while(1){d=c;l=J[c+60>>2];p=J[c+56>>2];h=J[c+52>>2];e=J[h+8>>2];j=J[c+48>>2];i=J[j+8>>2];a:{b:{if(K[c+4|0]&8){if(!vd(e,i)){break b}c=J[f+68>>2];if(c){if(!(Ha[J[J[c>>2]+8>>2]](c,j,h)|0)){break b}}J[d+4>>2]=J[d+4>>2]&-9;}c=0;if((K[i+4|0]&2?J[i>>2]:c)|(K[e+4|0]&2?J[e>>2]:0)){e=J[f+4>>2];c=e+P(J[(J[h+24>>2]+P(l,28)|0)+24>>2],40)|0;e=e+P(J[(J[j+24>>2]+P(p,28)|0)+24>>2],40)|0;if(Q(N[c>>2]-N[e+8>>2])>Q(0)|Q(N[c+4>>2]-N[e+12>>2])>Q(0)|(Q(N[e>>2]-N[c+8>>2])>Q(0)|Q(N[e+4>>2]-N[c+12>>2])>Q(0))){break b}tc(d,J[f+72>>2]);}c=J[d+12>>2];break a}c=J[d+12>>2];Ib(f,d);}if(c){continue}break}}N[a+103e3>>2]=0;if(!(!K[a+102994|0]|!m)){p=Fa-96|0;Fa=p;J[a+103016>>2]=0;c=a+103008|0;J[c>>2]=0;J[c+4>>2]=0;Z=a+68|0;e=_c(p+44|0,J[a+102956>>2],J[a+102932>>2],J[a+102960>>2],Z,J[a+102940>>2]);d=J[a+102948>>2];if(d){while(1){I[d+4>>1]=L[d+4>>1]&65534;d=J[d+96>>2];if(d){continue}break}}d=J[a+102928>>2];if(d){while(1){J[d+4>>2]=J[d+4>>2]&-2;d=J[d+12>>2];if(d){continue}break}}d=J[a+102952>>2];if(d){while(1){H[d+60|0]=0;d=J[d+12>>2];if(d){continue}break}}S=wb(Z,J[a+102956>>2]<<2);A=J[a+102948>>2];if(A){T=a+102964|0;while(1){c=L[A+4>>1];c:{if(!J[A>>2]|(c&35)!=34){break c}J[S>>2]=A;f=1;I[A+4>>1]=c|1;l=0;h=0;i=0;while(1){f=f-1|0;c=J[S+(f<<2)>>2];J[c+8>>2]=l;J[J[e+8>>2]+(l<<2)>>2]=c;I[c+4>>1]=L[c+4>>1]|2;d:{if(!J[c>>2]){break d}d=J[c+112>>2];if(d){while(1){j=J[d+4>>2];m=J[j+4>>2];e:{if((m&7)!=6|K[J[j+48>>2]+38|0]|K[J[j+52>>2]+38|0]){break e}J[J[e+12>>2]+(h<<2)>>2]=j;J[j+4>>2]=m|1;h=h+1|0;j=J[d>>2];m=L[j+4>>1];if(m&1){break e}J[S+(f<<2)>>2]=j;I[j+4>>1]=m|1;f=f+1|0;}d=J[d+12>>2];if(d){continue}break}}d=J[c+108>>2];if(!d){break d}while(1){m=J[d+4>>2];f:{if(K[m+60|0]){break f}c=J[d>>2];j=L[c+4>>1];if(!(j&32)){break f}J[J[e+16>>2]+(i<<2)>>2]=m;H[J[d+4>>2]+60|0]=1;i=i+1|0;if(j&1){break f}J[S+(f<<2)>>2]=c;I[c+4>>1]=j|1;f=f+1|0;}d=J[d+12>>2];if(d){continue}break}}l=l+1|0;if((f|0)>0){continue}break}J[e+36>>2]=h;J[e+28>>2]=l;J[e+32>>2]=i;ga=K[a+102972|0];i=0;f=0;h=Fa-160|0;Fa=h;o=N[w+4>>2];if(J[e+28>>2]>0){while(1){d=J[J[e+8>>2]+(i<<2)>>2];r=N[d+56>>2];N[d+52>>2]=r;j=J[d+48>>2];c=J[d+44>>2];J[d+36>>2]=c;J[d+40>>2]=j;b=N[d+72>>2];k=N[d+64>>2];g=N[d+68>>2];if(J[d>>2]==2){n=Q(o*N[d+120>>2]);q=Q(N[d+140>>2]*N[d+116>>2]);B=Q(Q(1)/Q(Q(o*N[d+132>>2])+Q(1)));g=Q(Q(g+Q(n*Q(Q(q*N[T+4>>2])+N[d+80>>2])))*B);k=Q(Q(k+Q(n*Q(Q(N[T>>2]*q)+N[d+76>>2])))*B);b=Q(Q(Q(Q(o*N[d+128>>2])*N[d+84>>2])+b)*Q(Q(1)/Q(Q(o*N[d+136>>2])+Q(1))));}d=P(i,12);m=d+J[e+20>>2]|0;J[m>>2]=c;J[m+4>>2]=j;N[(d+J[e+20>>2]|0)+8>>2]=r;c=d+J[e+24>>2]|0;N[c+4>>2]=g;N[c>>2]=k;N[(d+J[e+24>>2]|0)+8>>2]=b;i=i+1|0;if((i|0)<J[e+28>>2]){continue}break}}c=J[w+24>>2];J[h+120>>2]=J[w+20>>2];J[h+124>>2]=c;c=J[w+16>>2];J[h+112>>2]=J[w+12>>2];J[h+116>>2]=c;c=J[w+8>>2];J[h+104>>2]=J[w+4>>2];J[h+108>>2]=c;c=J[e+20>>2];J[h+128>>2]=c;d=J[e+24>>2];J[h+132>>2]=d;i=J[w+24>>2];J[h+72>>2]=J[w+20>>2];J[h+76>>2]=i;j=J[w+16>>2];i=h- -64|0;J[i>>2]=J[w+12>>2];J[i+4>>2]=j;i=J[w+8>>2];J[h+56>>2]=J[w+4>>2];J[h+60>>2]=i;J[h+80>>2]=J[e+12>>2];i=J[e+36>>2];J[h+92>>2]=d;J[h+88>>2]=c;J[h+84>>2]=i;J[h+96>>2]=J[e>>2];m=dd(h+4|0,h+56|0);bd(m);if(J[m+48>>2]>0?K[w+24|0]:0){i=J[m+28>>2];while(1){c=J[m+40>>2]+P(f,156)|0;j=P(J[c+116>>2],12);d=j+i|0;b=N[d+8>>2];k=N[d+4>>2];g=N[d>>2];x=P(J[c+112>>2],12);i=x+i|0;n=N[i+8>>2];q=N[i+4>>2];s=N[i>>2];z=J[c+148>>2];if((z|0)>0){t=N[c+72>>2];D=N[c+132>>2];v=N[c+124>>2];y=N[c+120>>2];E=N[c+76>>2];U=Q(-N[c+128>>2]);l=0;while(1){d=c+P(l,36)|0;B=N[d+16>>2];W=N[d+20>>2];r=Q(Q(E*B)-Q(t*W));B=Q(Q(t*B)+Q(E*W));b=Q(Q(D*Q(Q(N[d+8>>2]*r)-Q(B*N[d+12>>2])))+b);n=Q(Q(U*Q(Q(N[d>>2]*r)-Q(B*N[d+4>>2])))+n);k=Q(k+Q(v*r));g=Q(g+Q(v*B));q=Q(q-Q(y*r));s=Q(s-Q(y*B));l=l+1|0;if((z|0)!=(l|0)){continue}break}}N[i+4>>2]=q;N[i>>2]=s;c=J[m+28>>2];N[(c+x|0)+8>>2]=n;c=c+j|0;N[c+4>>2]=k;N[c>>2]=g;i=J[m+28>>2];N[(j+i|0)+8>>2]=b;f=f+1|0;if((f|0)<J[m+48>>2]){continue}break}}if(J[e+32>>2]>0){d=0;while(1){c=J[J[e+16>>2]+(d<<2)>>2];Ha[J[J[c>>2]+32>>2]](c,h+104|0);d=d+1|0;if((d|0)<J[e+32>>2]){continue}break}}N[p+24>>2]=0;if(J[w+16>>2]>0){j=0;while(1){d=0;if(J[e+32>>2]>0){while(1){c=J[J[e+16>>2]+(d<<2)>>2];Ha[J[J[c>>2]+36>>2]](c,h+104|0);d=d+1|0;if((d|0)<J[e+32>>2]){continue}break}}ad(m);j=j+1|0;if((j|0)<J[w+16>>2]){continue}break}}j=0;x=J[m+48>>2];if((x|0)>0){z=J[m+44>>2];ba=J[m+40>>2];while(1){c=ba+P(j,156)|0;i=J[c+148>>2];g:{if((i|0)<=0){break g}l=J[z+(J[c+152>>2]<<2)>>2]- -64|0;d=0;if((i|0)!=1){$=i&-2;f=0;while(1){O=l+P(d,20)|0;aa=c+P(d,36)|0;N[O+8>>2]=N[aa+16>>2];N[O+12>>2]=N[aa+20>>2];O=d|1;aa=l+P(O,20)|0;O=c+P(O,36)|0;N[aa+8>>2]=N[O+16>>2];N[aa+12>>2]=N[O+20>>2];d=d+2|0;f=f+2|0;if(($|0)!=(f|0)){continue}break}}if(!(i&1)){break g}f=l+P(d,20)|0;c=c+P(d,36)|0;N[f+8>>2]=N[c+16>>2];N[f+12>>2]=N[c+20>>2];}j=j+1|0;if((x|0)!=(j|0)){continue}break}}N[p+28>>2]=0;f=1;if(J[e+28>>2]>0){i=J[e+24>>2];j=0;while(1){c=P(j,12);i=c+i|0;b=N[i>>2];g=Q(o*b);r=Q(g*g);k=N[i+4>>2];g=Q(o*k);g=Q(r+Q(g*g));if(g>Q(4)){g=Q(Q(2)/Q(Y(g)));k=Q(k*g);b=Q(b*g);}d=c+J[e+20>>2]|0;n=N[d+8>>2];q=N[d+4>>2];B=N[d>>2];g=N[i+8>>2];r=Q(o*g);if(Q(r*r)>Q(2.4674012660980225)){g=Q(g*Q(Q(1.5707963705062866)/(r>Q(0)?r:Q(-r))));}N[d+4>>2]=q+Q(o*k);N[d>>2]=B+Q(o*b);N[(c+J[e+20>>2]|0)+8>>2]=Q(o*g)+n;d=c+J[e+24>>2]|0;N[d+4>>2]=k;N[d>>2]=b;i=J[e+24>>2];N[(c+i|0)+8>>2]=g;j=j+1|0;if((j|0)<J[e+28>>2]){continue}break}}if(J[w+20>>2]>0){j=0;while(1){B=Q(0);l=0;c=Fa+-64|0;Fa=c;if(J[m+48>>2]>0){f=J[m+24>>2];while(1){d=J[m+36>>2]+P(l,88)|0;i=P(J[d+36>>2],12);x=i+f|0;b=N[x+8>>2];k=N[x+4>>2];g=N[x>>2];x=P(J[d+32>>2],12);z=x+f|0;n=N[z+8>>2];q=N[z+4>>2];s=N[z>>2];z=J[d+84>>2];if((z|0)>0){v=N[d+68>>2];y=N[d+56>>2];E=N[d+48>>2];D=N[d+60>>2];U=N[d+52>>2];W=N[d+40>>2];_=N[d+44>>2];ca=Q(W+_);R=N[d+64>>2];F=Q(-R);f=0;while(1){r=Ua(n);N[c+60>>2]=r;t=Ta(n);N[c+56>>2]=t;C=Ua(b);N[c+44>>2]=C;G=Ta(b);N[c+40>>2]=G;N[c+52>>2]=q-Q(Q(t*E)+Q(U*r));N[c+48>>2]=s-Q(Q(r*E)-Q(U*t));N[c+36>>2]=k-Q(Q(G*y)+Q(D*C));N[c+32>>2]=g-Q(Q(C*y)-Q(D*G));$c(c+8|0,d,c+48|0,c+32|0,f);t=Q(0);r=N[c+24>>2];ba=r>B;C=N[c+20>>2];u=Q(C-k);M=Q(-u);X=Q(C-q);ea=Q(-X);da=N[c+16>>2];fa=Q(da-g);C=N[c+12>>2];G=N[c+8>>2];u=Q(Q(fa*C)-Q(G*u));ia=Q(Q(v*u)*u);u=Q(da-s);X=Q(Q(u*C)-Q(G*X));X=Q(ia+Q(Q(Q(R*X)*X)+ca));if(X>Q(0)){t=Q(Q(r+Q(.004999999888241291))*Q(.20000000298023224));t=t<Q(0)?t:Q(0);t=Q(Q(-(t<Q(-.20000000298023224)?Q(-.20000000298023224):t))/X);}B=ba?B:r;r=Q(C*t);t=Q(G*t);b=Q(Q(v*Q(Q(fa*r)+Q(t*M)))+b);n=Q(Q(F*Q(Q(u*r)+Q(t*ea)))+n);k=Q(k+Q(_*r));g=Q(g+Q(_*t));q=Q(q-Q(W*r));s=Q(s-Q(W*t));f=f+1|0;if((z|0)!=(f|0)){continue}break}f=J[m+24>>2];}d=f+x|0;N[d+4>>2]=q;N[d>>2]=s;d=J[m+24>>2];N[(d+x|0)+8>>2]=n;d=d+i|0;N[d+4>>2]=k;N[d>>2]=g;f=J[m+24>>2];N[(i+f|0)+8>>2]=b;l=l+1|0;if((l|0)<J[m+48>>2]){continue}break}}Fa=c- -64|0;i=1;d=0;if(J[e+32>>2]>0){while(1){c=J[J[e+16>>2]+(d<<2)>>2];i=Ha[J[J[c>>2]+40>>2]](c,h+104|0)&i;d=d+1|0;if((d|0)<J[e+32>>2]){continue}break}}c=B>=Q(-.014999999664723873)&i;if(!c){j=j+1|0;if((j|0)<J[w+20>>2]){continue}}break}f=!c;}if(J[e+28>>2]>0){i=0;while(1){l=P(i,12);d=l+J[e+20>>2]|0;c=d;n=N[c>>2];c=J[c>>2];q=N[d+4>>2];d=J[d+4>>2];j=J[J[e+8>>2]+(i<<2)>>2];J[j+44>>2]=c;J[j+48>>2]=d;b=N[(l+J[e+20>>2]|0)+8>>2];N[j+56>>2]=b;c=l+J[e+24>>2]|0;x=J[c+4>>2];J[j+64>>2]=J[c>>2];J[j+68>>2]=x;k=N[(l+J[e+24>>2]|0)+8>>2];g=Ua(b);N[j+24>>2]=g;b=Ta(b);N[j+20>>2]=b;N[j+72>>2]=k;k=N[j+28>>2];r=N[j+32>>2];N[j+16>>2]=q-Q(Q(b*k)+Q(g*r));N[j+12>>2]=n-Q(Q(g*k)-Q(r*b));i=i+1|0;if((i|0)<J[e+28>>2]){continue}break}}N[p+32>>2]=0;if(!(!J[e+4>>2]|J[e+36>>2]<=0)){x=J[m+40>>2];i=0;while(1){z=J[J[e+12>>2]+(i<<2)>>2];l=x+P(i,156)|0;c=J[l+148>>2];J[h+156>>2]=c;h:{if((c|0)<=0){break h}d=0;if((c|0)!=1){ba=c&-2;j=0;while(1){$=d<<2;O=h+140|0;aa=l+P(d,36)|0;N[$+O>>2]=N[aa+16>>2];ha=h+148|0;N[$+ha>>2]=N[aa+20>>2];aa=O;$=d|1;O=$<<2;$=l+P($,36)|0;N[aa+O>>2]=N[$+16>>2];N[O+ha>>2]=N[$+20>>2];d=d+2|0;j=j+2|0;if((ba|0)!=(j|0)){continue}break}}if(!(c&1)){break h}c=(h+140|0)+(d<<2)|0;d=l+P(d,36)|0;N[c>>2]=N[d+16>>2];N[c+8>>2]=N[d+20>>2];}c=J[e+4>>2];Ha[J[J[c>>2]+20>>2]](c,z,h+140|0);i=i+1|0;if((i|0)<J[e+36>>2]){continue}break}}i:{if(!ga){break i}i=J[e+28>>2];if((i|0)<=0){break i}j=J[e+8>>2];b=Q(34028234663852886e22);d=0;while(1){c=J[j+(d<<2)>>2];j:{if(!J[c>>2]){break j}k:{l:{if(!(K[c+4|0]&4)){break l}g=N[c+72>>2];if(Q(g*g)>Q(.001218469929881394)){break l}g=N[c+64>>2];k=Q(g*g);g=N[c+68>>2];if(!(Q(k+Q(g*g))>Q(9999999747378752e-20))){break k}}J[c+144>>2]=0;b=Q(0);break j}g=Q(o+N[c+144>>2]);N[c+144>>2]=g;b=b<g?b:g;}d=d+1|0;if((i|0)!=(d|0)){continue}break}if(!(b>=Q(.5))|f|J[e+28>>2]<=0){break i}i=0;while(1){c=J[J[e+8>>2]+(i<<2)>>2];J[c+64>>2]=0;J[c+68>>2]=0;J[c+144>>2]=0;J[c+72>>2]=0;J[c+76>>2]=0;J[c+80>>2]=0;J[c+84>>2]=0;I[c+4>>1]=L[c+4>>1]&65533;i=i+1|0;if((i|0)<J[e+28>>2]){continue}break}}cd(m);Fa=h+160|0;N[a+103008>>2]=N[p+24>>2]+N[a+103008>>2];N[a+103012>>2]=N[p+28>>2]+N[a+103012>>2];N[a+103016>>2]=N[p+32>>2]+N[a+103016>>2];c=J[e+28>>2];if((c|0)<=0){break c}f=J[e+8>>2];d=0;if((c|0)!=1){h=c&-2;l=0;while(1){j=d<<2;i=J[j+f>>2];if(!J[i>>2]){I[i+4>>1]=L[i+4>>1]&65534;}i=J[f+(j|4)>>2];if(!J[i>>2]){I[i+4>>1]=L[i+4>>1]&65534;}d=d+2|0;l=l+2|0;if((h|0)!=(l|0)){continue}break}}if(!(c&1)){break c}c=J[f+(d<<2)>>2];if(J[c>>2]){break c}I[c+4>>1]=L[c+4>>1]&65534;}A=J[A+96>>2];if(A){continue}break}}vb(Z,S);d=J[a+102948>>2];if(d){while(1){if(!(!(H[d+4|0]&1)|!J[d>>2])){wd(d);}d=J[d+96>>2];if(d){continue}break}}Ub(a+102868|0);N[a+103020>>2]=0;Zc(e);Fa=p+96|0;N[a+103004>>2]=0;b=N[w+4>>2];}if(!(!K[a+102992|0]|!(b>Q(0)))){i=Fa-272|0;Fa=i;m=_c(i+220|0,64,32,0,a+68|0,J[a+102940>>2]);m:{if(!K[a+102994|0]){break m}j=J[a+102948>>2];if(j){while(1){J[j+60>>2]=0;I[j+4>>1]=L[j+4>>1]&65534;j=J[j+96>>2];if(j){continue}break}}j=J[a+102928>>2];if(!j){break m}while(1){J[j+128>>2]=0;J[j+132>>2]=1065353216;J[j+4>>2]=J[j+4>>2]&-34;j=J[j+12>>2];if(j){continue}break}}ga=a+102868|0;ba=i+116|0;while(1){n:{d=0;t=Q(1);o:{j=J[a+102928>>2];p:{if(!j){break p}while(1){c=J[j+4>>2];q:{if(!(c&4)|J[j+128>>2]>8){break q}r:{if(c&32){g=N[j+132>>2];break r}h=J[j+48>>2];if(K[h+38|0]){break q}l=J[j+52>>2];if(K[l+38|0]){break q}f=J[l+8>>2];c=J[f>>2];e=J[h+8>>2];p=L[e+4>>1];A=J[e>>2];S=L[f+4>>1];if(!(p>>>1&(A|0)!=0|S>>>1&(c|0)!=0)){break q}if(!(S&8|(p&8|(A|0)!=2))){if((c|0)==2){break q}}b=N[f+60>>2];g=N[e+60>>2];s:{if(b>g){N[e+60>>2]=b;k=N[e+40>>2];g=Q(Q(b-g)/Q(Q(1)-g));N[e+40>>2]=k+Q(g*Q(N[e+48>>2]-k));k=N[e+36>>2];N[e+36>>2]=k+Q(g*Q(N[e+44>>2]-k));k=g;g=N[e+52>>2];N[e+52>>2]=Q(k*Q(N[e+56>>2]-g))+g;break s}if(b<g){N[f+60>>2]=g;k=N[f+40>>2];b=Q(Q(g-b)/Q(Q(1)-b));N[f+40>>2]=k+Q(b*Q(N[f+48>>2]-k));k=N[f+36>>2];N[f+36>>2]=k+Q(b*Q(N[f+44>>2]-k));k=b;b=N[f+52>>2];N[f+52>>2]=Q(k*Q(N[f+56>>2]-b))+b;}b=g;}p=J[j+60>>2];A=J[j+56>>2];J[i+140>>2]=0;J[i+132>>2]=0;J[i+136>>2]=0;J[i+112>>2]=0;J[i+104>>2]=0;J[i+108>>2]=0;c=i+88|0;Wb(c,J[h+12>>2],A);Wb(ba,J[l+12>>2],p);J[i+176>>2]=J[e+60>>2];h=J[e+56>>2];J[i+168>>2]=J[e+52>>2];J[i+172>>2]=h;h=J[e+48>>2];J[i+160>>2]=J[e+44>>2];J[i+164>>2]=h;h=J[e+40>>2];J[i+152>>2]=J[e+36>>2];J[i+156>>2]=h;h=J[e+32>>2];J[i+144>>2]=J[e+28>>2];J[i+148>>2]=h;J[i+212>>2]=J[f+60>>2];e=J[f+56>>2];J[i+204>>2]=J[f+52>>2];J[i+208>>2]=e;e=J[f+48>>2];J[i+196>>2]=J[f+44>>2];J[i+200>>2]=e;e=J[f+40>>2];J[i+188>>2]=J[f+36>>2];J[i+192>>2]=e;e=J[f+32>>2];J[i+180>>2]=J[f+28>>2];J[i+184>>2]=e;J[i+216>>2]=1065353216;f=0;B=Q(0);e=Fa-320|0;Fa=e;J[6119]=J[6119]+1;J[i+48>>2]=0;r=N[c+128>>2];N[i+52>>2]=r;J[e+312>>2]=J[c+88>>2];h=J[c+84>>2];J[e+304>>2]=J[c+80>>2];J[e+308>>2]=h;h=J[c+76>>2];J[e+296>>2]=J[c+72>>2];J[e+300>>2]=h;h=c- -64|0;l=J[h+4>>2];J[e+288>>2]=J[h>>2];J[e+292>>2]=l;h=J[c+60>>2];J[e+280>>2]=J[c+56>>2];J[e+284>>2]=h;J[e+272>>2]=J[c+124>>2];h=J[c+120>>2];J[e+264>>2]=J[c+116>>2];J[e+268>>2]=h;h=J[c+112>>2];J[e+256>>2]=J[c+108>>2];J[e+260>>2]=h;h=J[c+104>>2];J[e+248>>2]=J[c+100>>2];J[e+252>>2]=h;h=J[c+96>>2];J[e+240>>2]=J[c+92>>2];J[e+244>>2]=h;k=N[e+304>>2];g=Q(Q(V(Q(k/Q(6.2831854820251465))))*Q(6.2831854820251465));v=Q(k-g);N[e+304>>2]=v;o=N[e+264>>2];k=Q(Q(V(Q(o/Q(6.2831854820251465))))*Q(6.2831854820251465));s=Q(o-k);N[e+264>>2]=s;n=Q(N[e+268>>2]-k);N[e+268>>2]=n;q=Q(N[e+308>>2]-g);N[e+308>>2]=q;g=N[c+52>>2];k=N[c+24>>2];I[e+232>>1]=0;J[e+160>>2]=J[c+24>>2];h=J[c+20>>2];J[e+152>>2]=J[c+16>>2];J[e+156>>2]=h;h=J[c+12>>2];J[e+144>>2]=J[c+8>>2];J[e+148>>2]=h;h=J[c+4>>2];J[e+136>>2]=J[c>>2];J[e+140>>2]=h;h=J[c+40>>2];J[e+172>>2]=J[c+36>>2];J[e+176>>2]=h;h=J[c+48>>2];J[e+180>>2]=J[c+44>>2];J[e+184>>2]=h;J[e+188>>2]=J[c+52>>2];h=J[c+32>>2];J[e+164>>2]=J[c+28>>2];J[e+168>>2]=h;H[e+224|0]=0;g=Q(Q(k+g)+Q(-.014999999664723873));y=g<Q(.004999999888241291)?Q(.004999999888241291):g;ca=Q(y+Q(-.0012499999720603228));E=Q(y+Q(.0012499999720603228));S=c+28|0;k=Q(0);t:{while(1){u:{g=Q(Q(1)-k);n=Q(Q(g*s)+Q(k*n));o=Ua(n);N[e+220>>2]=o;n=Ta(n);N[e+216>>2]=n;s=Q(Q(g*v)+Q(k*q));q=Ua(s);N[e+204>>2]=q;s=Ta(s);N[e+200>>2]=s;v=N[e+240>>2];D=N[e+244>>2];N[e+212>>2]=Q(Q(g*N[e+252>>2])+Q(k*N[e+260>>2]))-Q(Q(n*v)+Q(o*D));N[e+208>>2]=Q(Q(g*N[e+248>>2])+Q(k*N[e+256>>2]))-Q(Q(o*v)-Q(D*n));o=N[e+280>>2];n=N[e+284>>2];N[e+196>>2]=Q(Q(g*N[e+292>>2])+Q(k*N[e+300>>2]))-Q(Q(s*o)+Q(q*n));N[e+192>>2]=Q(Q(g*N[e+288>>2])+Q(k*N[e+296>>2]))-Q(Q(q*o)-Q(n*s));Cd(e+112|0,e+228|0,e+136|0);g=N[e+128>>2];if(g<=Q(0)){h=2;break u}if(g<E){h=3;B=k;break u}h=e+12|0;J[h+4>>2]=S;J[h>>2]=c;l=L[e+232>>1];J[h+40>>2]=J[e+312>>2];p=J[e+308>>2];J[h+32>>2]=J[e+304>>2];J[h+36>>2]=p;p=J[e+300>>2];J[h+24>>2]=J[e+296>>2];J[h+28>>2]=p;p=J[e+292>>2];J[h+16>>2]=J[e+288>>2];J[h+20>>2]=p;p=J[e+284>>2];J[h+8>>2]=J[e+280>>2];J[h+12>>2]=p;p=J[e+244>>2];J[h+44>>2]=J[e+240>>2];J[h+48>>2]=p;p=J[e+252>>2];J[h+52>>2]=J[e+248>>2];J[h+56>>2]=p;p=J[e+260>>2];J[h+60>>2]=J[e+256>>2];J[h+64>>2]=p;p=J[e+268>>2];J[h+68>>2]=J[e+264>>2];J[h+72>>2]=p;J[h+76>>2]=J[e+272>>2];n=N[h+48>>2];g=Q(Q(1)-k);s=Q(Q(g*N[h+68>>2])+Q(N[h+72>>2]*k));o=Ua(s);q=N[h+44>>2];s=Ta(s);W=Q(Q(Q(g*N[h+56>>2])+Q(N[h- -64>>2]*k))-Q(Q(s*q)+Q(n*o)));_=Q(Q(Q(g*N[h+52>>2])+Q(N[h+60>>2]*k))-Q(Q(o*q)-Q(n*s)));n=N[h+12>>2];v=Q(Q(g*N[h+32>>2])+Q(N[h+36>>2]*k));q=Ua(v);D=N[h+8>>2];v=Ta(v);C=Q(Q(Q(g*N[h+20>>2])+Q(N[h+28>>2]*k))-Q(Q(v*D)+Q(n*q)));G=Q(Q(Q(g*N[h+16>>2])+Q(N[h+24>>2]*k))-Q(Q(q*D)-Q(n*v)));D=Q(-s);U=Q(-v);v:{if((l|0)==1){J[h+80>>2]=0;l=J[c+44>>2]+(K[e+237|0]<<3)|0;n=N[l>>2];g=Q(s*n);s=N[l+4>>2];M=Q(W+Q(g+Q(o*s)));g=v;l=J[c+16>>2]+(K[e+234|0]<<3)|0;v=N[l>>2];W=N[l+4>>2];g=Q(M-Q(C+Q(Q(g*v)+Q(q*W))));N[h+96>>2]=g;o=Q(Q(_+Q(Q(o*n)+Q(s*D)))-Q(G+Q(Q(q*v)+Q(W*U))));N[h+92>>2]=o;n=Q(Y(Q(Q(o*o)+Q(g*g))));if(n<Q(1.1920928955078125e-7)){break v}q=g;g=Q(Q(1)/n);N[h+96>>2]=q*g;N[h+92>>2]=o*g;break v}w:{if(K[e+234|0]==K[e+235|0]){J[h+80>>2]=2;l=J[c+44>>2];p=l+(K[e+238|0]<<3)|0;R=N[p>>2];l=l+(K[e+237|0]<<3)|0;F=N[l>>2];M=N[p+4>>2];X=N[l+4>>2];g=Q(M-X);N[h+92>>2]=g;u=Q(R-F);n=Q(-u);N[h+96>>2]=n;u=Q(Y(Q(Q(g*g)+Q(u*u))));if(!(u<Q(1.1920928955078125e-7))){u=Q(Q(1)/u);n=Q(u*n);N[h+96>>2]=n;g=Q(g*u);N[h+92>>2]=g;}u=Q(Q(X+M)*Q(.5));N[h+88>>2]=u;R=Q(Q(F+R)*Q(.5));N[h+84>>2]=R;M=G;l=J[c+16>>2]+(K[e+234|0]<<3)|0;G=N[l>>2];F=N[l+4>>2];if(!(Q(Q(Q(Q(M+Q(Q(q*G)+Q(F*U)))-Q(_+Q(Q(o*R)+Q(u*D))))*Q(Q(o*g)+Q(n*D)))+Q(Q(Q(s*g)+Q(o*n))*Q(Q(C+Q(Q(v*G)+Q(q*F)))-Q(W+Q(Q(s*R)+Q(o*u))))))<Q(0))){break v}break w}J[h+80>>2]=1;l=J[c+16>>2];p=l+(K[e+235|0]<<3)|0;R=N[p>>2];l=l+(K[e+234|0]<<3)|0;F=N[l>>2];M=N[p+4>>2];X=N[l+4>>2];g=Q(M-X);N[h+92>>2]=g;u=Q(R-F);n=Q(-u);N[h+96>>2]=n;u=Q(Y(Q(Q(g*g)+Q(u*u))));if(!(u<Q(1.1920928955078125e-7))){u=Q(Q(1)/u);n=Q(u*n);N[h+96>>2]=n;g=Q(g*u);N[h+92>>2]=g;}u=Q(Q(X+M)*Q(.5));N[h+88>>2]=u;R=Q(Q(F+R)*Q(.5));N[h+84>>2]=R;M=_;l=J[c+44>>2]+(K[e+237|0]<<3)|0;_=N[l>>2];F=N[l+4>>2];if(!(Q(Q(Q(Q(M+Q(Q(o*_)+Q(F*D)))-Q(G+Q(Q(q*R)+Q(u*U))))*Q(Q(q*g)+Q(n*U)))+Q(Q(Q(v*g)+Q(q*n))*Q(Q(W+Q(Q(s*_)+Q(o*F)))-Q(C+Q(Q(v*R)+Q(q*u))))))<Q(0))){break v}}N[h+96>>2]=-n;N[h+92>>2]=-g;}A=0;l=4;x:{g=r;s=Yc(h,e+8|0,e+4|0,g);y:{if(!(E<s)){while(1){z:{if(!(s>ca)){v=Xc(e+12|0,J[e+8>>2],J[e+4>>2],k);if(ca>v){l=1;break y}h=0;n=k;q=g;if(v<=E){l=3;break y}A:{while(1){o=h&1?Q(n+Q(Q(Q(y-v)*Q(q-n))/Q(s-v))):Q(Q(n+q)*Q(.5));J[6122]=J[6122]+1;h=h+1|0;D=Xc(e+12|0,J[e+8>>2],J[e+4>>2],o);U=Q(D-y);if((U>Q(0)?U:Q(-U))<Q(.0012499999720603228)){g=o;break A}p=y<D;q=p?q:o;n=p?o:n;v=p?D:v;s=p?s:D;if((h|0)!=50){continue}break}h=50;}p=J[6123];J[6123]=(h|0)<(p|0)?p:h;A=A+1|0;if((A|0)!=8){break z}g=k;}J[6120]=J[6120]+1;f=f+1|0;if((f|0)!=20){break x}N[i+52>>2]=g;J[i+48>>2]=1;f=20;break t}s=Yc(e+12|0,e+8|0,e+4|0,g);if(!(E<s)){continue}break}}k=r;}N[i+52>>2]=k;J[i+48>>2]=l;J[6120]=J[6120]+1;f=f+1|0;break t}n=N[e+268>>2];s=N[e+264>>2];q=N[e+308>>2];v=N[e+304>>2];k=g;continue}break}N[i+52>>2]=B;J[i+48>>2]=h;}c=J[6121];J[6121]=(c|0)>(f|0)?c:f;N[6117]=N[6117]+Q(0);g=N[6118];N[6118]=g>Q(0)?g:Q(0);Fa=e+320|0;g=Q(1);if(J[i+48>>2]==3){b=Q(Q(Q(Q(1)-b)*N[i+52>>2])+b);g=b<Q(1)?b:Q(1);}N[j+132>>2]=g;J[j+4>>2]=J[j+4>>2]|32;}c=g<t;d=c?j:d;t=c?g:t;}j=J[j+12>>2];if(j){continue}break}if(!d){break p}if(!(t>Q(.9999988079071045))){break o}}H[a+102994|0]=1;break n}e=J[J[d+52>>2]+8>>2];h=J[J[d+48>>2]+8>>2];f=h;J[i+120>>2]=J[f+60>>2];c=J[f+56>>2];J[i+112>>2]=J[f+52>>2];J[i+116>>2]=c;c=J[f+48>>2];J[i+104>>2]=J[f+44>>2];J[i+108>>2]=c;c=J[f+40>>2];J[i+96>>2]=J[f+36>>2];J[i+100>>2]=c;c=J[f+32>>2];J[i+88>>2]=J[f+28>>2];J[i+92>>2]=c;J[i+80>>2]=J[e+60>>2];c=J[e+56>>2];J[i+72>>2]=J[e+52>>2];J[i+76>>2]=c;l=J[e+48>>2];j=i- -64|0;c=j;J[c>>2]=J[e+44>>2];J[c+4>>2]=l;c=J[e+40>>2];J[i+56>>2]=J[e+36>>2];J[i+60>>2]=c;c=J[e+32>>2];J[i+48>>2]=J[e+28>>2];J[i+52>>2]=c;g=N[f+36>>2];b=N[f+60>>2];b=Q(Q(t-b)/Q(Q(1)-b));N[f+36>>2]=g+Q(b*Q(N[f+44>>2]-g));g=N[f+40>>2];N[f+40>>2]=g+Q(b*Q(N[f+48>>2]-g));N[f+60>>2]=t;g=b;b=N[f+52>>2];b=Q(Q(g*Q(N[f+56>>2]-b))+b);N[f+56>>2]=b;N[f+52>>2]=b;g=Ua(b);N[f+24>>2]=g;b=Ta(b);N[f+20>>2]=b;n=N[f+40>>2];l=J[f+40>>2];r=N[f+36>>2];J[f+44>>2]=J[f+36>>2];J[f+48>>2]=l;k=N[f+28>>2];o=N[f+32>>2];N[f+16>>2]=n-Q(Q(b*k)+Q(g*o));N[f+12>>2]=r-Q(Q(g*k)-Q(o*b));g=N[e+40>>2];b=N[e+60>>2];b=Q(Q(t-b)/Q(Q(1)-b));N[e+40>>2]=g+Q(b*Q(N[e+48>>2]-g));g=N[e+36>>2];N[e+36>>2]=g+Q(b*Q(N[e+44>>2]-g));N[e+60>>2]=t;g=b;b=N[e+52>>2];b=Q(Q(g*Q(N[e+56>>2]-b))+b);N[e+56>>2]=b;N[e+52>>2]=b;g=Ua(b);N[e+24>>2]=g;b=Ta(b);N[e+20>>2]=b;n=N[e+40>>2];f=J[e+40>>2];k=N[e+36>>2];J[e+44>>2]=J[e+36>>2];J[e+48>>2]=f;r=k;k=N[e+28>>2];o=N[e+32>>2];N[e+12>>2]=r-Q(Q(g*k)-Q(b*o));N[e+16>>2]=n-Q(Q(b*k)+Q(g*o));tc(d,J[a+102940>>2]);c=J[d+4>>2];J[d+4>>2]=c&-33;J[d+128>>2]=J[d+128>>2]+1;B:{if((c&6)!=6){J[d+4>>2]=c&-37;J[h+60>>2]=J[i+120>>2];c=J[i+116>>2];J[h+52>>2]=J[i+112>>2];J[h+56>>2]=c;c=J[i+108>>2];J[h+44>>2]=J[i+104>>2];J[h+48>>2]=c;c=J[i+100>>2];J[h+36>>2]=J[i+96>>2];J[h+40>>2]=c;c=J[i+92>>2];J[h+28>>2]=J[i+88>>2];J[h+32>>2]=c;J[e+60>>2]=J[i+80>>2];c=J[i+76>>2];J[e+52>>2]=J[i+72>>2];J[e+56>>2]=c;c=J[j+4>>2];J[e+44>>2]=J[j>>2];J[e+48>>2]=c;c=J[i+60>>2];J[e+36>>2]=J[i+56>>2];J[e+40>>2]=c;c=J[i+52>>2];J[e+28>>2]=J[i+48>>2];J[e+32>>2]=c;g=N[h+56>>2];b=Ua(g);N[h+24>>2]=b;g=Ta(g);N[h+20>>2]=g;k=N[h+28>>2];o=N[h+32>>2];N[h+16>>2]=N[h+48>>2]-Q(Q(g*k)+Q(b*o));N[h+12>>2]=N[h+44>>2]-Q(Q(b*k)-Q(o*g));g=N[e+56>>2];b=Ta(g);N[e+20>>2]=b;g=Ua(g);N[e+24>>2]=g;k=N[e+28>>2];o=N[e+32>>2];N[e+16>>2]=N[e+48>>2]-Q(Q(b*k)+Q(g*o));N[e+12>>2]=N[e+44>>2]-Q(Q(g*k)-Q(o*b));c=9;break B}j=0;J[h+144>>2]=0;I[h+4>>1]=L[h+4>>1]|2;J[e+144>>2]=0;I[e+4>>1]=L[e+4>>1]|2;J[m+32>>2]=0;J[h+8>>2]=0;J[J[m+8>>2]>>2]=h;c=1;J[e+8>>2]=1;J[J[m+8>>2]+4>>2]=e;J[m+28>>2]=2;J[m+36>>2]=1;J[J[m+12>>2]>>2]=d;I[h+4>>1]=L[h+4>>1]|1;I[e+4>>1]=L[e+4>>1]|1;J[d+4>>2]=J[d+4>>2]|1;J[i+44>>2]=e;J[i+40>>2]=h;while(1){p=J[(i+40|0)+(j<<2)>>2];C:{if(J[p>>2]!=2){break C}j=J[p+112>>2];if(!j){break C}while(1){if(J[m+28>>2]==J[m+40>>2]|J[m+36>>2]==J[m+44>>2]){break C}l=J[j+4>>2];D:{if(H[l+4|0]&1){break D}f=J[j>>2];if(!(K[p+4|0]&8|J[f>>2]!=2|K[f+4|0]&8)|(K[J[l+48>>2]+38|0]|K[J[l+52>>2]+38|0])){break D}J[i+32>>2]=J[f+60>>2];d=J[f+56>>2];J[i+24>>2]=J[f+52>>2];J[i+28>>2]=d;d=J[f+48>>2];J[i+16>>2]=J[f+44>>2];J[i+20>>2]=d;d=J[f+40>>2];J[i+8>>2]=J[f+36>>2];J[i+12>>2]=d;d=J[f+32>>2];J[i>>2]=J[f+28>>2];J[i+4>>2]=d;if(!(H[f+4|0]&1)){g=N[f+40>>2];b=N[f+60>>2];b=Q(Q(t-b)/Q(Q(1)-b));N[f+40>>2]=g+Q(b*Q(N[f+48>>2]-g));g=N[f+36>>2];N[f+36>>2]=g+Q(b*Q(N[f+44>>2]-g));N[f+60>>2]=t;g=b;b=N[f+52>>2];b=Q(Q(g*Q(N[f+56>>2]-b))+b);N[f+56>>2]=b;N[f+52>>2]=b;g=Ua(b);N[f+24>>2]=g;b=Ta(b);N[f+20>>2]=b;n=N[f+40>>2];A=J[f+40>>2];k=N[f+36>>2];J[f+44>>2]=J[f+36>>2];J[f+48>>2]=A;r=k;k=N[f+28>>2];o=N[f+32>>2];N[f+12>>2]=r-Q(Q(g*k)-Q(b*o));N[f+16>>2]=n-Q(Q(b*k)+Q(g*o));}tc(l,J[a+102940>>2]);d=J[l+4>>2];if(!(d&4)){d=J[i+4>>2];J[f+28>>2]=J[i>>2];J[f+32>>2]=d;J[f+60>>2]=J[i+32>>2];d=J[i+28>>2];J[f+52>>2]=J[i+24>>2];J[f+56>>2]=d;d=J[i+20>>2];J[f+44>>2]=J[i+16>>2];J[f+48>>2]=d;d=J[i+12>>2];J[f+36>>2]=J[i+8>>2];J[f+40>>2]=d;g=N[f+56>>2];b=Ua(g);N[f+24>>2]=b;g=Ta(g);N[f+20>>2]=g;k=N[f+28>>2];o=N[f+32>>2];N[f+16>>2]=N[f+48>>2]-Q(Q(g*k)+Q(b*o));N[f+12>>2]=N[f+44>>2]-Q(Q(b*k)-Q(o*g));break D}if(!(d&2)){d=J[i+4>>2];J[f+28>>2]=J[i>>2];J[f+32>>2]=d;J[f+60>>2]=J[i+32>>2];d=J[i+28>>2];J[f+52>>2]=J[i+24>>2];J[f+56>>2]=d;d=J[i+20>>2];J[f+44>>2]=J[i+16>>2];J[f+48>>2]=d;d=J[i+12>>2];J[f+36>>2]=J[i+8>>2];J[f+40>>2]=d;g=N[f+56>>2];b=Ua(g);N[f+24>>2]=b;g=Ta(g);N[f+20>>2]=g;k=N[f+28>>2];o=N[f+32>>2];N[f+16>>2]=N[f+48>>2]-Q(Q(g*k)+Q(b*o));N[f+12>>2]=N[f+44>>2]-Q(Q(b*k)-Q(o*g));break D}J[l+4>>2]=d|1;d=J[m+36>>2];J[m+36>>2]=d+1;J[J[m+12>>2]+(d<<2)>>2]=l;d=L[f+4>>1];if(d&1){break D}I[f+4>>1]=d|1;if(J[f>>2]){J[f+144>>2]=0;I[f+4>>1]=d|3;}d=J[m+28>>2];J[f+8>>2]=d;J[J[m+8>>2]+(d<<2)>>2]=f;J[m+28>>2]=d+1;}j=J[j+12>>2];if(j){continue}break}}j=1;d=c;c=0;if(d){continue}break}b=N[w+4>>2];J[i+16>>2]=20;J[i+8>>2]=1065353216;b=Q(b*Q(Q(1)-t));N[i>>2]=b;N[i+4>>2]=Q(1)/b;c=J[w+16>>2];d=0;H[i+20|0]=0;J[i+12>>2]=c;A=J[h+8>>2];S=J[e+8>>2];c=0;j=Fa-128|0;Fa=j;E:{if(J[m+28>>2]<=0){f=J[m+24>>2];break E}while(1){e=J[J[m+8>>2]+(c<<2)>>2];l=J[e+48>>2];h=P(c,12);f=h+J[m+20>>2]|0;J[f>>2]=J[e+44>>2];J[f+4>>2]=l;N[(h+J[m+20>>2]|0)+8>>2]=N[e+56>>2];l=J[e+68>>2];f=h+J[m+24>>2]|0;J[f>>2]=J[e+64>>2];J[f+4>>2]=l;f=J[m+24>>2];N[(h+f|0)+8>>2]=N[e+72>>2];c=c+1|0;if((c|0)<J[m+28>>2]){continue}break}}J[j+88>>2]=J[m+12>>2];J[j+92>>2]=J[m+36>>2];J[j+104>>2]=J[m>>2];c=J[i+12>>2];J[j+72>>2]=J[i+8>>2];J[j+76>>2]=c;c=J[i+20>>2];J[j+80>>2]=J[i+16>>2];J[j+84>>2]=c;c=J[i+4>>2];J[j+64>>2]=J[i>>2];J[j+68>>2]=c;c=J[m+20>>2];J[j+100>>2]=f;J[j+96>>2]=c;l=dd(j+12|0,j- -64|0);c=0;F:{if(J[i+16>>2]<=0){break F}while(1){n=Q(0);f=0;h=Fa+-64|0;Fa=h;if(J[l+48>>2]>0){while(1){p=J[l+36>>2]+P(f,88)|0;Z=J[p+36>>2];T=J[p+32>>2];G:{if((T|0)!=(A|0)){s=Q(0);o=Q(0);if((S|0)!=(T|0)){break G}}s=N[p+64>>2];o=N[p+40>>2];}x=J[p+84>>2];H:{if((A|0)!=(Z|0)){v=Q(0);r=Q(0);if((S|0)!=(Z|0)){break H}}v=N[p+68>>2];r=N[p+44>>2];}e=J[l+24>>2];Z=P(Z,12);z=e+Z|0;b=N[z+8>>2];k=N[z+4>>2];g=N[z>>2];T=P(T,12);z=T+e|0;t=N[z+8>>2];B=N[z+4>>2];q=N[z>>2];if((x|0)>0){D=N[p+56>>2];U=N[p+48>>2];W=N[p+60>>2];_=N[p+52>>2];ca=Q(o+r);R=Q(-s);e=0;while(1){y=Ua(t);N[h+60>>2]=y;E=Ta(t);N[h+56>>2]=E;C=Ua(b);N[h+44>>2]=C;G=Ta(b);N[h+40>>2]=G;N[h+52>>2]=B-Q(Q(E*U)+Q(_*y));N[h+48>>2]=q-Q(Q(y*U)-Q(_*E));N[h+36>>2]=k-Q(Q(G*D)+Q(W*C));N[h+32>>2]=g-Q(Q(C*D)-Q(W*G));$c(h+8|0,p,h+48|0,h+32|0,e);y=Q(0);E=N[h+24>>2];z=E>n;C=N[h+20>>2];F=Q(C-k);u=Q(-F);M=Q(C-B);X=Q(-M);ea=N[h+16>>2];da=Q(ea-g);C=N[h+12>>2];G=N[h+8>>2];F=Q(Q(da*C)-Q(G*F));fa=Q(Q(v*F)*F);F=Q(ea-q);M=Q(Q(F*C)-Q(G*M));M=Q(fa+Q(Q(Q(s*M)*M)+ca));if(M>Q(0)){y=Q(Q(E+Q(.004999999888241291))*Q(.75));y=y<Q(0)?y:Q(0);y=Q(Q(-(y<Q(-.20000000298023224)?Q(-.20000000298023224):y))/M);}n=z?n:E;E=Q(C*y);y=Q(G*y);b=Q(Q(v*Q(Q(da*E)+Q(y*u)))+b);t=Q(Q(R*Q(Q(F*E)+Q(y*X)))+t);k=Q(k+Q(r*E));g=Q(g+Q(r*y));B=Q(B-Q(o*E));q=Q(q-Q(o*y));e=e+1|0;if((x|0)!=(e|0)){continue}break}e=J[l+24>>2];}e=e+T|0;N[e+4>>2]=B;N[e>>2]=q;e=J[l+24>>2];N[(e+T|0)+8>>2]=t;e=e+Z|0;N[e+4>>2]=k;N[e>>2]=g;N[(Z+J[l+24>>2]|0)+8>>2]=b;f=f+1|0;if((f|0)<J[l+48>>2]){continue}break}}Fa=h- -64|0;if(n>=Q(-.007499999832361937)){break F}c=c+1|0;if((c|0)<J[i+16>>2]){continue}break}}e=P(A,12);h=e+J[m+20>>2]|0;p=J[h+4>>2];c=A<<2;f=J[c+J[m+8>>2]>>2];J[f+36>>2]=J[h>>2];J[f+40>>2]=p;h=c;c=J[m+8>>2];f=J[m+20>>2];N[J[h+c>>2]+52>>2]=N[(f+e|0)+8>>2];h=f;f=P(S,12);h=h+f|0;p=J[h+4>>2];x=c;c=S<<2;e=J[x+c>>2];J[e+36>>2]=J[h>>2];J[e+40>>2]=p;N[J[c+J[m+8>>2]>>2]+52>>2]=N[(f+J[m+20>>2]|0)+8>>2];bd(l);c=0;if(J[i+12>>2]>0){while(1){ad(l);c=c+1|0;if((c|0)<J[i+12>>2]){continue}break}}h=0;if(J[m+28>>2]>0){b=N[i>>2];while(1){c=P(h,12);e=c+J[m+24>>2]|0;n=N[e>>2];g=Q(b*n);k=Q(g*g);q=N[e+4>>2];g=Q(b*q);g=Q(k+Q(g*g));if(g>Q(4)){g=Q(Q(2)/Q(Y(g)));q=Q(q*g);n=Q(n*g);}f=c+J[m+20>>2]|0;r=N[f+8>>2];k=N[f+4>>2];o=N[f>>2];s=N[e+8>>2];g=Q(b*s);if(Q(g*g)>Q(2.4674012660980225)){s=Q(s*Q(Q(1.5707963705062866)/(g>Q(0)?g:Q(-g))));}k=Q(k+Q(b*q));N[f+4>>2]=k;o=Q(o+Q(b*n));N[f>>2]=o;g=Q(Q(b*s)+r);N[(c+J[m+20>>2]|0)+8>>2]=g;f=c+J[m+24>>2]|0;N[f+4>>2]=q;N[f>>2]=n;N[(c+J[m+24>>2]|0)+8>>2]=s;c=J[J[m+8>>2]+(h<<2)>>2];N[c+72>>2]=s;N[c+64>>2]=n;N[c+68>>2]=q;N[c+56>>2]=g;N[c+48>>2]=k;N[c+44>>2]=o;r=Ua(g);N[c+24>>2]=r;g=Ta(g);N[c+20>>2]=g;q=k;k=N[c+28>>2];n=N[c+32>>2];N[c+16>>2]=q-Q(Q(g*k)+Q(r*n));N[c+12>>2]=o-Q(Q(r*k)-Q(n*g));h=h+1|0;if((h|0)<J[m+28>>2]){continue}break}}if(!(!J[m+4>>2]|J[m+36>>2]<=0)){A=J[l+40>>2];f=0;while(1){S=J[J[m+12>>2]+(f<<2)>>2];p=A+P(f,156)|0;e=J[p+148>>2];J[j+124>>2]=e;I:{if((e|0)<=0){break I}c=0;if((e|0)!=1){Z=e&-2;h=0;while(1){T=c<<2;x=j+108|0;z=p+P(c,36)|0;N[T+x>>2]=N[z+16>>2];O=T;T=j+116|0;N[O+T>>2]=N[z+20>>2];O=x;x=c|1;z=x<<2;x=p+P(x,36)|0;N[O+z>>2]=N[x+16>>2];N[z+T>>2]=N[x+20>>2];c=c+2|0;h=h+2|0;if((Z|0)!=(h|0)){continue}break}}if(!(e&1)){break I}e=(j+108|0)+(c<<2)|0;c=p+P(c,36)|0;N[e>>2]=N[c+16>>2];N[e+8>>2]=N[c+20>>2];}c=J[m+4>>2];Ha[J[J[c>>2]+20>>2]](c,S,j+108|0);f=f+1|0;if((f|0)<J[m+36>>2]){continue}break}}cd(l);Fa=j+128|0;if(J[m+28>>2]>0){while(1){c=J[J[m+8>>2]+(d<<2)>>2];I[c+4>>1]=L[c+4>>1]&65534;J:{if(J[c>>2]!=2){break J}wd(c);j=J[c+112>>2];if(!j){break J}while(1){c=J[j+4>>2];J[c+4>>2]=J[c+4>>2]&-34;j=J[j+12>>2];if(j){continue}break}}d=d+1|0;if((d|0)<J[m+28>>2]){continue}break}}Ub(ga);c=0;if(!K[a+102993|0]){break B}H[a+102994|0]=0;c=8;}if((c|0)!=8){continue}}break}Zc(m);Fa=i+272|0;N[a+103024>>2]=0;b=N[w+4>>2];}if(b>Q(0)){N[a+102984>>2]=N[w+8>>2];}K:{if(!K[a+102990|0]){break K}d=J[a+102948>>2];if(!d){break K}while(1){J[d+84>>2]=0;J[d+76>>2]=0;J[d+80>>2]=0;d=J[d+96>>2];if(d){continue}break}}H[a+102989|0]=0;N[a+102996>>2]=0;Fa=w+32|0;}function rd(a,b,c,d,e){var f=0,g=0,h=Q(0),i=Q(0),j=Q(0),k=0,l=Q(0),m=Q(0),n=Q(0),o=0,p=Q(0),q=0,r=Q(0),s=Q(0),t=0,u=Q(0),v=Q(0),w=0,y=Q(0),z=Q(0),A=0,B=Q(0),C=Q(0),E=Q(0),F=0,G=Q(0),L=Q(0),M=Q(0),O=Q(0),R=0,S=0;A=Fa-256|0;Fa=A;g=Fa-160|0;Fa=g;m=N[c>>2];n=N[e>>2];p=N[c+4>>2];s=N[e+4>>2];f=A+4|0;h=N[c+12>>2];l=N[e+12>>2];u=N[e+8>>2];i=N[c+8>>2];j=Q(Q(h*l)+Q(u*i));N[f+144>>2]=j;l=Q(Q(h*u)-Q(l*i));N[f+140>>2]=l;p=Q(s-p);m=Q(n-m);n=Q(Q(h*p)-Q(i*m));N[f+136>>2]=n;h=Q(Q(h*m)+Q(i*p));N[f+132>>2]=h;i=N[d+12>>2];m=N[d+16>>2];s=Q(n+Q(Q(l*i)+Q(j*m)));N[f+152>>2]=s;u=Q(h+Q(Q(j*i)-Q(m*l)));N[f+148>>2]=u;v=N[b+32>>2];e=J[b+32>>2];l=N[b+28>>2];J[f+156>>2]=J[b+28>>2];J[f+160>>2]=e;r=N[b+16>>2];k=J[b+16>>2];j=N[b+12>>2];J[f+164>>2]=J[b+12>>2];J[f+168>>2]=k;y=N[b+24>>2];o=J[b+24>>2];m=N[b+20>>2];J[f+172>>2]=J[b+20>>2];J[f+176>>2]=o;z=N[b+40>>2];q=J[b+40>>2];B=N[b+36>>2];J[f+180>>2]=J[b+36>>2];J[f+184>>2]=q;t=K[b+44|0];h=Q(m-j);i=Q(y-r);n=Q(Y(Q(Q(h*h)+Q(i*i))));if(!(n<Q(1.1920928955078125e-7))){n=Q(Q(1)/n);i=Q(i*n);h=Q(h*n);}k=K[b+45|0];N[f+196>>2]=i;N[f+200>>2]=-h;p=Q(Q(i*Q(u-j))-Q(Q(s-r)*h));a:{b:{c:{d:{e:{f:{g:{h:{if(t){n=l;l=Q(j-l);j=r;r=v;j=Q(j-r);v=Q(Y(Q(Q(l*l)+Q(j*j))));if(!(v<Q(1.1920928955078125e-7))){v=Q(Q(1)/v);j=Q(j*v);l=Q(l*v);}N[f+188>>2]=j;N[f+192>>2]=-l;n=Q(Q(j*Q(u-n))-Q(Q(s-r)*l));j=Q(Q(l*i)-Q(h*j));if(!k){break g}c=j>=Q(0);break h}if(!k){break d}n=Q(0);c=0;}l=Q(B-m);j=Q(z-y);r=Q(Y(Q(Q(l*l)+Q(j*j))));if(!(r<Q(1.1920928955078125e-7))){r=Q(Q(1)/r);j=Q(j*r);l=Q(l*r);}N[f+204>>2]=j;N[f+208>>2]=-l;r=Q(Q(h*j)-Q(l*i));s=Q(Q(j*Q(u-m))-Q(Q(s-y)*l));m=Q(-i);if(!(!t|!k)){if(!(!(r>Q(0))|c^1)){c=p>=Q(0)|n>=Q(0)|s>=Q(0);H[f+248|0]=c;if(c){c=J[f+200>>2];J[f+212>>2]=J[f+196>>2];J[f+216>>2]=c;c=J[f+192>>2];J[f+228>>2]=J[f+188>>2];J[f+232>>2]=c;c=J[f+208>>2];J[f+236>>2]=J[f+204>>2];J[f+240>>2]=c;e=1;break a}N[f+236>>2]=m;N[f+228>>2]=m;N[f+212>>2]=m;N[f+240>>2]=h;N[f+232>>2]=h;N[f+216>>2]=h;break b}if(c){i:{if(!(n>=Q(0))){c=p>=Q(0)&s>=Q(0);H[f+248|0]=c;if(c){break i}N[f+236>>2]=m;N[f+212>>2]=m;N[f+240>>2]=h;N[f+232>>2]=l;N[f+228>>2]=-j;N[f+216>>2]=h;break b}H[f+248|0]=1;}e=J[f+200>>2];c=J[f+196>>2];J[f+212>>2]=c;J[f+216>>2]=e;J[f+236>>2]=c;J[f+240>>2]=e;c=J[f+192>>2];J[f+228>>2]=J[f+188>>2];J[f+232>>2]=c;e=1;break a}if(r>Q(0)){j:{if(!(s>=Q(0))){c=p>=Q(0)&n>=Q(0);H[f+248|0]=c;if(c){break j}N[f+228>>2]=m;N[f+212>>2]=m;N[f+232>>2]=h;N[f+216>>2]=h;N[f+236>>2]=-N[f+188>>2];N[f+240>>2]=-N[f+192>>2];break b}H[f+248|0]=1;}e=J[f+200>>2];c=J[f+196>>2];J[f+228>>2]=c;J[f+232>>2]=e;J[f+212>>2]=c;J[f+216>>2]=e;c=J[f+208>>2];J[f+236>>2]=J[f+204>>2];J[f+240>>2]=c;e=1;break a}c=p>=Q(0)&n>=Q(0)&s>=Q(0);H[f+248|0]=c;e=1;if(c){k=J[f+200>>2];c=J[f+196>>2];J[f+236>>2]=c;J[f+240>>2]=k;J[f+228>>2]=c;J[f+232>>2]=k;J[f+212>>2]=c;J[f+216>>2]=k;break a}N[f+212>>2]=m;N[f+232>>2]=l;N[f+228>>2]=-j;N[f+216>>2]=h;N[f+236>>2]=-N[f+188>>2];N[f+240>>2]=-N[f+192>>2];break b}if(!t){break e}if(!c){break f}break c}if(j>=Q(0)){break c}}c=p>=Q(0)&n>=Q(0);H[f+248|0]=c;e=1;if(c){k=J[f+200>>2];c=J[f+196>>2];J[f+228>>2]=c;J[f+232>>2]=k;J[f+212>>2]=c;J[f+216>>2]=k;J[f+236>>2]=c^-2147483648;J[f+240>>2]=k^-2147483648;break a}N[f+216>>2]=h;N[f+212>>2]=-i;c=J[f+200>>2];J[f+228>>2]=J[f+196>>2];J[f+232>>2]=c;N[f+236>>2]=-N[f+188>>2];N[f+240>>2]=-N[f+192>>2];break b}if(r>Q(0)){c=p>=Q(0)|s>=Q(0);H[f+248|0]=c;e=1;if(c){c=J[f+200>>2];k=J[f+196>>2];J[f+212>>2]=k;J[f+216>>2]=c;o=J[f+208>>2];J[f+236>>2]=J[f+204>>2];J[f+240>>2]=o;J[f+228>>2]=k^-2147483648;J[f+232>>2]=c^-2147483648;break a}N[f+228>>2]=m;N[f+212>>2]=m;N[f+232>>2]=h;N[f+216>>2]=h;c=J[f+200>>2];J[f+236>>2]=J[f+196>>2];J[f+240>>2]=c;break b}c=p>=Q(0)&s>=Q(0);H[f+248|0]=c;e=1;if(c){k=J[f+200>>2];c=J[f+196>>2];J[f+236>>2]=c;J[f+240>>2]=k;J[f+212>>2]=c;J[f+216>>2]=k;J[f+228>>2]=c^-2147483648;J[f+232>>2]=k^-2147483648;break a}N[f+212>>2]=m;N[f+232>>2]=l;N[f+228>>2]=-j;N[f+216>>2]=h;c=J[f+200>>2];J[f+236>>2]=J[f+196>>2];J[f+240>>2]=c;break b}c=p>=Q(0);H[f+248|0]=c;if(c){e=J[f+200>>2];c=J[f+196>>2];J[f+212>>2]=c;J[f+216>>2]=e;c=c^-2147483648;J[f+236>>2]=c;J[f+228>>2]=c;c=e^-2147483648;J[f+240>>2]=c;J[f+232>>2]=c;e=1;break a}N[f+216>>2]=h;N[f+212>>2]=-i;e=J[f+200>>2];c=J[f+196>>2];J[f+236>>2]=c;J[f+240>>2]=e;J[f+228>>2]=c;J[f+232>>2]=e;break b}c=p>=Q(0)|n>=Q(0);H[f+248|0]=c;e=1;if(c){c=J[f+200>>2];k=J[f+196>>2];J[f+212>>2]=k;J[f+216>>2]=c;o=J[f+192>>2];J[f+228>>2]=J[f+188>>2];J[f+232>>2]=o;J[f+236>>2]=k^-2147483648;J[f+240>>2]=c^-2147483648;break a}N[f+216>>2]=h;N[f+212>>2]=-i;N[f+240>>2]=h;e=J[f+200>>2];c=J[f+196>>2];J[f+228>>2]=c;J[f+232>>2]=e;J[f+236>>2]=c^-2147483648;}e=0;}k=J[d+148>>2];J[f+128>>2]=k;c=0;if((k|0)>0){while(1){h=N[f+132>>2];k=c<<3;e=k+f|0;i=N[f+140>>2];k=d+k|0;j=N[k+20>>2];l=N[f+144>>2];m=N[k+24>>2];N[e+4>>2]=Q(Q(i*j)+Q(l*m))+N[f+136>>2];N[e>>2]=h+Q(Q(l*j)-Q(m*i));h=N[f+140>>2];i=N[k+84>>2];j=N[f+144>>2];l=N[k+88>>2];N[e+68>>2]=Q(h*i)+Q(j*l);N[e- -64>>2]=Q(j*i)-Q(l*h);c=c+1|0;if((c|0)<J[d+148>>2]){continue}break}k=J[f+128>>2];e=K[f+248|0];}s=Q(N[d+8>>2]+N[b+8>>2]);N[f+244>>2]=s;c=0;J[a+60>>2]=0;k:{if((k|0)<=0){j=Q(34028234663852886e22);break k}t=k&1;h=N[f+216>>2];l=N[f+212>>2];m=N[f+168>>2];n=N[f+164>>2];l:{if((k|0)==1){j=Q(34028234663852886e22);i=Q(34028234663852886e22);break l}w=k&-2;j=Q(34028234663852886e22);i=Q(34028234663852886e22);b=0;while(1){o=c<<3;q=(o|8)+f|0;p=Q(Q(l*Q(N[q>>2]-n))+Q(h*Q(N[q+4>>2]-m)));o=f+o|0;u=Q(Q(l*Q(N[o>>2]-n))+Q(h*Q(N[o+4>>2]-m)));o=u<i;i=o?u:i;q=i>p;i=q?p:i;j=q?p:o?u:j;c=c+2|0;b=b+2|0;if((w|0)!=(b|0)){continue}break}}if(!t){break k}b=(c<<3)+f|0;h=Q(Q(l*Q(N[b>>2]-n))+Q(h*Q(N[b+4>>2]-m)));j=h<i?h:j;}m:{if(j>s){break m}o=0;J[g+156>>2]=-8388609;J[g+148>>2]=0;J[g+152>>2]=-1;n:{o:{c=J[f+128>>2];if((c|0)>0){l=N[f+216>>2];m=N[f+212>>2];u=N[f+232>>2];r=N[f+240>>2];y=N[f+176>>2];B=N[f+168>>2];v=N[f+228>>2];G=N[f+236>>2];L=N[f+244>>2];M=N[f+172>>2];O=N[f+164>>2];i=Q(-34028234663852886e22);while(1){b=(o<<3)+f|0;z=N[b- -64>>2];n=Q(-z);h=N[b>>2];C=N[b+4>>2];p=N[b+68>>2];E=Q(Q(n*Q(h-O))-Q(Q(C-B)*p));h=Q(Q(n*Q(h-M))-Q(Q(C-y)*p));h=h>E?E:h;if(h>L){break o}p=Q(-p);p:{q:{if(Q(Q(z*l)+Q(m*p))>=Q(0)){if(Q(Q(Q(n-G)*m)+Q(Q(p-r)*l))<Q(-.03490658849477768)){break p}if(h>i){break q}break p}if(!(h>i)|Q(Q(Q(n-v)*m)+Q(Q(p-u)*l))<Q(-.03490658849477768)){break p}}N[g+156>>2]=h;J[g+152>>2]=o;J[g+148>>2]=2;i=h;}o=o+1|0;if((c|0)!=(o|0)){continue}break}}break n}N[g+156>>2]=h;J[g+152>>2]=o;J[g+148>>2]=2;}r:{s:{t:{u:{b=J[g+148>>2];v:{if(!b){break v}h=N[g+156>>2];if(h>s){break m}if(!(h>Q(Q(j*Q(.9800000190734863))+Q(.0010000000474974513)))){break v}if((b|0)!=1){break u}}J[a+56>>2]=1;b=0;if((k|0)<2){break s}h=N[f+212>>2];j=N[f+216>>2];i=Q(Q(h*N[f+64>>2])+Q(j*N[f+68>>2]));c=k-1|0;R=c&1;if((k|0)==2){c=1;break t}S=c&-2;q=f- -64|0;c=1;o=0;while(1){t=q+(c<<3)|0;l=Q(Q(h*N[t>>2])+Q(j*N[t+4>>2]));t=l<i;F=c+1|0;w=q+(F<<3)|0;m=Q(Q(h*N[w>>2])+Q(j*N[w+4>>2]));i=t?l:i;w=m<i;i=w?m:i;b=w?F:t?c:b;c=c+2|0;o=o+2|0;if((S|0)!=(o|0)){continue}break}break t}c=J[g+152>>2];J[a+56>>2]=2;b=J[f+164>>2];e=J[f+168>>2];I[g+122>>1]=256;H[g+121|0]=c;H[g+120|0]=0;J[g+112>>2]=b;J[g+116>>2]=e;b=J[f+172>>2];e=J[f+176>>2];H[g+132|0]=0;I[g+134>>1]=256;H[g+133|0]=c;J[g+124>>2]=b;J[g+128>>2]=e;J[g+56>>2]=c;b=c+1|0;e=(b|0)<(k|0)?b:0;J[g+60>>2]=e;q=(c<<3)+f|0;k=q;b=J[k>>2];k=J[k+4>>2];J[g+64>>2]=b;J[g+68>>2]=k;o=(e<<3)+f|0;e=J[o>>2];o=J[o+4>>2];J[g+72>>2]=e;J[g+76>>2]=o;t=q- -64|0;q=t;h=N[q>>2];q=J[q>>2];i=N[t+4>>2];t=J[t+4>>2];J[g+80>>2]=q;J[g+84>>2]=t;q=0;break r}if(!R){break s}q=b;b=(c<<3)+f|0;b=Q(Q(h*N[b- -64>>2])+Q(j*N[b+68>>2]))<i?c:q;}c=(b<<3)+f|0;o=J[c>>2];c=J[c+4>>2];I[g+122>>1]=1;H[g+121|0]=b;H[g+120|0]=0;J[g+112>>2]=o;J[g+116>>2]=c;b=b+1|0;b=(b|0)<(k|0)?b:0;c=(b<<3)+f|0;k=J[c>>2];c=J[c+4>>2];H[g+132|0]=0;I[g+134>>1]=1;H[g+133|0]=b;J[g+124>>2]=k;J[g+128>>2]=c;if(e){J[g+56>>2]=0;J[g+60>>2]=1;c=J[f+168>>2];k=c;b=J[f+164>>2];J[g+64>>2]=b;J[g+68>>2]=c;c=J[f+176>>2];o=c;e=J[f+172>>2];J[g+72>>2]=e;J[g+76>>2]=c;i=N[f+200>>2];q=J[f+200>>2];h=N[f+196>>2];J[g+80>>2]=J[f+196>>2];J[g+84>>2]=q;c=0;q=1;break r}J[g+56>>2]=1;J[g+60>>2]=0;c=J[f+176>>2];k=c;b=J[f+172>>2];J[g+64>>2]=b;J[g+68>>2]=c;c=J[f+168>>2];o=c;e=J[f+164>>2];J[g+72>>2]=e;J[g+76>>2]=c;h=N[f+196>>2];i=Q(-N[f+200>>2]);N[g+84>>2]=i;h=Q(-h);N[g+80>>2]=h;c=1;q=1;}N[g+104>>2]=h;N[g+92>>2]=-h;N[g+88>>2]=i;N[g+100>>2]=-i;N[g+108>>2]=Q(h*(x(2,o),D()))-Q(i*(x(2,e),D()));h=Q(Q(i*(x(2,b),D()))-Q((x(2,k),D())*h));N[g+96>>2]=h;if((Xb(g+32|0,g+112|0,g+88|0,h,c)|0)<2){break m}if((Xb(g,g+32|0,g+100|0,N[g+108>>2],J[g+60>>2])|0)<2){break m}w:{if(q){i=N[g+84>>2];c=J[g+84>>2];l=N[g+80>>2];J[a+40>>2]=J[g+80>>2];J[a+44>>2]=c;d=J[g+68>>2];k=d;h=N[g+68>>2];j=N[g+64>>2];b=J[g+64>>2];break w}c=(J[g+56>>2]<<3)+d|0;b=J[c+88>>2];J[a+40>>2]=J[c+84>>2];J[a+44>>2]=b;k=J[c+24>>2];l=N[g+80>>2];h=N[g+68>>2];j=N[g+64>>2];i=N[g+84>>2];b=J[c+20>>2];}J[a+48>>2]=b;J[a+52>>2]=k;c=0;m=N[f+244>>2];n=N[g>>2];p=N[g+4>>2];if(m>=Q(Q(l*Q(n-j))+Q(Q(p-h)*i))){x:{if(q){m=N[f+144>>2];p=Q(p-N[f+136>>2]);s=N[f+140>>2];n=Q(n-N[f+132>>2]);N[a+4>>2]=Q(m*p)-Q(s*n);N[a>>2]=Q(m*n)+Q(p*s);J[a+16>>2]=J[g+8>>2];break x}b=J[g+4>>2];J[a>>2]=J[g>>2];J[a+4>>2]=b;H[a+18|0]=K[g+11|0];H[a+19|0]=K[g+10|0];H[a+16|0]=K[g+9|0];H[a+17|0]=K[g+8|0];}m=N[f+244>>2];c=1;}b=a;r=l;l=N[g+12>>2];r=Q(r*Q(l-j));j=N[g+16>>2];if(Q(r+Q(Q(j-h)*i))<=m){a=P(c,20)+a|0;y:{if(!q){d=J[g+16>>2];J[a>>2]=J[g+12>>2];J[a+4>>2]=d;H[a+18|0]=K[g+23|0];H[a+19|0]=K[g+22|0];H[a+16|0]=K[g+21|0];H[a+17|0]=K[g+20|0];break y}h=N[f+144>>2];i=Q(j-N[f+136>>2]);j=N[f+140>>2];l=Q(l-N[f+132>>2]);N[a+4>>2]=Q(h*i)-Q(j*l);N[a>>2]=Q(h*l)+Q(i*j);J[a+16>>2]=J[g+20>>2];}c=c+1|0;}J[b+60>>2]=c;}Fa=g+160|0;Fa=A+256|0;}function _a(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0;l=Fa-16|0;Fa=l;a:{b:{c:{d:{e:{f:{g:{h:{i:{j:{k:{l:{m:{n:{if(a>>>0<=244){g=J[6434];h=a>>>0<11?16:a+11&-8;c=h>>>3|0;b=g>>>c|0;if(b&3){c=c+((b^-1)&1)|0;a=c<<3;b=a+25776|0;d=J[a+25784>>2];a=J[d+8>>2];o:{if((b|0)==(a|0)){m=25736,n=ll(c)&g,J[m>>2]=n;break o}J[a+12>>2]=b;J[b+8>>2]=a;}a=d+8|0;b=c<<3;J[d+4>>2]=b|3;b=b+d|0;J[b+4>>2]=J[b+4>>2]|1;break a}k=J[6436];if(k>>>0>=h>>>0){break n}if(b){a=2<<c;a=(0-a|a)&b<<c;d=il(0-a&a);a=d<<3;b=a+25776|0;e=J[a+25784>>2];a=J[e+8>>2];p:{if((b|0)==(a|0)){g=ll(d)&g;J[6434]=g;break p}J[a+12>>2]=b;J[b+8>>2]=a;}J[e+4>>2]=h|3;c=e+h|0;a=d<<3;d=a-h|0;J[c+4>>2]=d|1;J[a+e>>2]=d;if(k){b=(k&-8)+25776|0;f=J[6439];a=1<<(k>>>3);q:{if(!(a&g)){J[6434]=a|g;a=b;break q}a=J[b+8>>2];}J[b+8>>2]=f;J[a+12>>2]=f;J[f+12>>2]=b;J[f+8>>2]=a;}a=e+8|0;J[6439]=c;J[6436]=d;break a}j=J[6435];if(!j){break n}c=J[(il(0-j&j)<<2)+26040>>2];f=(J[c+4>>2]&-8)-h|0;b=c;while(1){r:{a=J[b+16>>2];if(!a){a=J[b+20>>2];if(!a){break r}}b=(J[a+4>>2]&-8)-h|0;d=b>>>0<f>>>0;f=d?b:f;c=d?a:c;b=a;continue}break}i=J[c+24>>2];d=J[c+12>>2];if((d|0)!=(c|0)){a=J[c+8>>2];J[a+12>>2]=d;J[d+8>>2]=a;break b}b=c+20|0;a=J[b>>2];if(!a){a=J[c+16>>2];if(!a){break m}b=c+16|0;}while(1){e=b;d=a;b=a+20|0;a=J[b>>2];if(a){continue}b=d+16|0;a=J[d+16>>2];if(a){continue}break}J[e>>2]=0;break b}h=-1;if(a>>>0>4294967231){break n}a=a+11|0;h=a&-8;j=J[6435];if(!j){break n}f=0-h|0;g=0;s:{if(h>>>0<256){break s}g=31;if(h>>>0>16777215){break s}a=S(a>>>8|0);g=((h>>>38-a&1)-(a<<1)|0)+62|0;}b=J[(g<<2)+26040>>2];t:{u:{v:{if(!b){a=0;break v}a=0;c=h<<((g|0)!=31?25-(g>>>1|0)|0:0);while(1){w:{e=(J[b+4>>2]&-8)-h|0;if(e>>>0>=f>>>0){break w}d=b;f=e;if(e){break w}f=0;a=b;break u}e=J[b+20>>2];b=J[((c>>>29&4)+b|0)+16>>2];a=e?(e|0)==(b|0)?a:e:a;c=c<<1;if(b){continue}break}}if(!(a|d)){d=0;a=2<<g;a=(0-a|a)&j;if(!a){break n}a=J[(il(a&0-a)<<2)+26040>>2];}if(!a){break t}}while(1){b=(J[a+4>>2]&-8)-h|0;c=b>>>0<f>>>0;f=c?b:f;d=c?a:d;b=J[a+16>>2];if(b){a=b;}else {a=J[a+20>>2];}if(a){continue}break}}if(!d|J[6436]-h>>>0<=f>>>0){break n}g=J[d+24>>2];c=J[d+12>>2];if((d|0)!=(c|0)){a=J[d+8>>2];J[a+12>>2]=c;J[c+8>>2]=a;break c}b=d+20|0;a=J[b>>2];if(!a){a=J[d+16>>2];if(!a){break l}b=d+16|0;}while(1){e=b;c=a;b=a+20|0;a=J[b>>2];if(a){continue}b=c+16|0;a=J[c+16>>2];if(a){continue}break}J[e>>2]=0;break c}a=J[6436];if(a>>>0>=h>>>0){d=J[6439];b=a-h|0;x:{if(b>>>0>=16){c=d+h|0;J[c+4>>2]=b|1;J[a+d>>2]=b;J[d+4>>2]=h|3;break x}J[d+4>>2]=a|3;a=a+d|0;J[a+4>>2]=J[a+4>>2]|1;c=0;b=0;}J[6436]=b;J[6439]=c;a=d+8|0;break a}i=J[6437];if(i>>>0>h>>>0){b=i-h|0;J[6437]=b;c=J[6440];a=c+h|0;J[6440]=a;J[a+4>>2]=b|1;J[c+4>>2]=h|3;a=c+8|0;break a}a=0;j=h+47|0;if(J[6552]){c=J[6554];}else {J[6555]=-1;J[6556]=-1;J[6553]=4096;J[6554]=4096;J[6552]=l+12&-16^1431655768;J[6557]=0;J[6545]=0;c=4096;}e=j+c|0;f=0-c|0;b=e&f;if(b>>>0<=h>>>0){break a}d=J[6544];if(d){c=J[6542];g=c+b|0;if(d>>>0<g>>>0|c>>>0>=g>>>0){break a}}y:{if(!(K[26180]&4)){z:{A:{B:{C:{d=J[6440];if(d){a=26184;while(1){c=J[a>>2];if(c>>>0<=d>>>0&d>>>0<c+J[a+4>>2]>>>0){break C}a=J[a+8>>2];if(a){continue}break}}c=Cb(0);if((c|0)==-1){break z}g=b;d=J[6553];a=d-1|0;if(a&c){g=(b-c|0)+(a+c&0-d)|0;}if(g>>>0<=h>>>0){break z}d=J[6544];if(d){a=J[6542];f=a+g|0;if(d>>>0<f>>>0|a>>>0>=f>>>0){break z}}a=Cb(g);if((c|0)!=(a|0)){break B}break y}g=f&e-i;c=Cb(g);if((c|0)==(J[a>>2]+J[a+4>>2]|0)){break A}a=c;}if((a|0)==-1){break z}if(h+48>>>0<=g>>>0){c=a;break y}c=J[6554];c=c+(j-g|0)&0-c;if((Cb(c)|0)==-1){break z}g=c+g|0;c=a;break y}if((c|0)!=-1){break y}}J[6545]=J[6545]|4;}c=Cb(b);a=Cb(0);if((c|0)==-1|(a|0)==-1|a>>>0<=c>>>0){break i}g=a-c|0;if(g>>>0<=h+40>>>0){break i}}a=J[6542]+g|0;J[6542]=a;if(a>>>0>M[6543]){J[6543]=a;}D:{e=J[6440];if(e){a=26184;while(1){d=J[a>>2];b=J[a+4>>2];if((d+b|0)==(c|0)){break D}a=J[a+8>>2];if(a){continue}break}break k}a=J[6438];if(!(a>>>0<=c>>>0?a:0)){J[6438]=c;}a=0;J[6547]=g;J[6546]=c;J[6442]=-1;J[6443]=J[6552];J[6549]=0;while(1){d=a<<3;b=d+25776|0;J[d+25784>>2]=b;J[d+25788>>2]=b;a=a+1|0;if((a|0)!=32){continue}break}d=g-40|0;a=c+8&7?-8-c&7:0;b=d-a|0;J[6437]=b;a=a+c|0;J[6440]=a;J[a+4>>2]=b|1;J[(c+d|0)+4>>2]=40;J[6441]=J[6556];break j}if(J[a+12>>2]&8|(c>>>0<=e>>>0|d>>>0>e>>>0)){break k}J[a+4>>2]=b+g;a=e+8&7?-8-e&7:0;c=a+e|0;J[6440]=c;b=J[6437]+g|0;a=b-a|0;J[6437]=a;J[c+4>>2]=a|1;J[(b+e|0)+4>>2]=40;J[6441]=J[6556];break j}d=0;break b}c=0;break c}if(M[6438]>c>>>0){J[6438]=c;}b=c+g|0;a=26184;E:{F:{G:{while(1){if((b|0)!=J[a>>2]){a=J[a+8>>2];if(a){continue}break G}break}if(!(K[a+12|0]&8)){break F}}a=26184;while(1){b=J[a>>2];if(b>>>0<=e>>>0){f=b+J[a+4>>2]|0;if(f>>>0>e>>>0){break E}}a=J[a+8>>2];continue}}J[a>>2]=c;J[a+4>>2]=J[a+4>>2]+g;j=(c+8&7?-8-c&7:0)+c|0;J[j+4>>2]=h|3;g=b+(b+8&7?-8-b&7:0)|0;i=h+j|0;a=g-i|0;if((e|0)==(g|0)){J[6440]=i;a=J[6437]+a|0;J[6437]=a;J[i+4>>2]=a|1;break d}if(J[6439]==(g|0)){J[6439]=i;a=J[6436]+a|0;J[6436]=a;J[i+4>>2]=a|1;J[a+i>>2]=a;break d}f=J[g+4>>2];if((f&3)!=1){break e}e=f&-8;if(f>>>0<=255){c=J[g+12>>2];b=J[g+8>>2];if((c|0)==(b|0)){m=25736,n=J[6434]&ll(f>>>3|0),J[m>>2]=n;break f}J[b+12>>2]=c;J[c+8>>2]=b;break f}h=J[g+24>>2];c=J[g+12>>2];if((g|0)!=(c|0)){b=J[g+8>>2];J[b+12>>2]=c;J[c+8>>2]=b;break g}b=g+20|0;f=J[b>>2];if(!f){f=J[g+16>>2];if(!f){break h}b=g+16|0;}while(1){d=b;c=f;b=c+20|0;f=J[b>>2];if(f){continue}b=c+16|0;f=J[c+16>>2];if(f){continue}break}J[d>>2]=0;break g}d=g-40|0;a=c+8&7?-8-c&7:0;b=d-a|0;J[6437]=b;a=a+c|0;J[6440]=a;J[a+4>>2]=b|1;J[(c+d|0)+4>>2]=40;J[6441]=J[6556];a=(f+(f-39&7?39-f&7:0)|0)-47|0;d=a>>>0<e+16>>>0?e:a;J[d+4>>2]=27;a=J[6549];J[d+16>>2]=J[6548];J[d+20>>2]=a;a=J[6547];J[d+8>>2]=J[6546];J[d+12>>2]=a;J[6548]=d+8;J[6547]=g;J[6546]=c;J[6549]=0;a=d+24|0;while(1){J[a+4>>2]=7;b=a+8|0;a=a+4|0;if(b>>>0<f>>>0){continue}break}if((d|0)==(e|0)){break j}J[d+4>>2]=J[d+4>>2]&-2;f=d-e|0;J[e+4>>2]=f|1;J[d>>2]=f;if(f>>>0<=255){b=(f&-8)+25776|0;c=J[6434];a=1<<(f>>>3);H:{if(!(c&a)){J[6434]=a|c;a=b;break H}a=J[b+8>>2];}J[b+8>>2]=e;J[a+12>>2]=e;J[e+12>>2]=b;J[e+8>>2]=a;break j}a=31;if(f>>>0<=16777215){a=S(f>>>8|0);a=((f>>>38-a&1)-(a<<1)|0)+62|0;}J[e+28>>2]=a;J[e+16>>2]=0;J[e+20>>2]=0;b=(a<<2)+26040|0;I:{d=J[6435];c=1<<a;J:{if(!(d&c)){J[6435]=c|d;J[b>>2]=e;break J}a=f<<((a|0)!=31?25-(a>>>1|0)|0:0);d=J[b>>2];while(1){b=d;if((f|0)==(J[b+4>>2]&-8)){break I}c=a>>>29|0;a=a<<1;c=(c&4)+b|0;d=J[c+16>>2];if(d){continue}break}J[c+16>>2]=e;}J[e+24>>2]=b;J[e+12>>2]=e;J[e+8>>2]=e;break j}a=J[b+8>>2];J[a+12>>2]=e;J[b+8>>2]=e;J[e+24>>2]=0;J[e+12>>2]=b;J[e+8>>2]=a;}a=J[6437];if(a>>>0<=h>>>0){break i}b=a-h|0;J[6437]=b;c=J[6440];a=c+h|0;J[6440]=a;J[a+4>>2]=b|1;J[c+4>>2]=h|3;a=c+8|0;break a}J[6386]=48;a=0;break a}c=0;}if(!h){break f}d=J[g+28>>2];b=(d<<2)+26040|0;K:{if(J[b>>2]==(g|0)){J[b>>2]=c;if(c){break K}m=25740,n=J[6435]&ll(d),J[m>>2]=n;break f}J[h+(J[h+16>>2]==(g|0)?16:20)>>2]=c;if(!c){break f}}J[c+24>>2]=h;b=J[g+16>>2];if(b){J[c+16>>2]=b;J[b+24>>2]=c;}b=J[g+20>>2];if(!b){break f}J[c+20>>2]=b;J[b+24>>2]=c;}a=a+e|0;g=e+g|0;f=J[g+4>>2];}J[g+4>>2]=f&-2;J[i+4>>2]=a|1;J[a+i>>2]=a;if(a>>>0<=255){b=(a&-8)+25776|0;c=J[6434];a=1<<(a>>>3);L:{if(!(c&a)){J[6434]=a|c;a=b;break L}a=J[b+8>>2];}J[b+8>>2]=i;J[a+12>>2]=i;J[i+12>>2]=b;J[i+8>>2]=a;break d}f=31;if(a>>>0<=16777215){b=S(a>>>8|0);f=((a>>>38-b&1)-(b<<1)|0)+62|0;}J[i+28>>2]=f;J[i+16>>2]=0;J[i+20>>2]=0;b=(f<<2)+26040|0;M:{d=J[6435];c=1<<f;N:{if(!(d&c)){J[6435]=c|d;J[b>>2]=i;break N}f=a<<((f|0)!=31?25-(f>>>1|0)|0:0);c=J[b>>2];while(1){b=c;if((J[c+4>>2]&-8)==(a|0)){break M}c=f>>>29|0;f=f<<1;d=(c&4)+b|0;c=J[d+16>>2];if(c){continue}break}J[d+16>>2]=i;}J[i+24>>2]=b;J[i+12>>2]=i;J[i+8>>2]=i;break d}a=J[b+8>>2];J[a+12>>2]=i;J[b+8>>2]=i;J[i+24>>2]=0;J[i+12>>2]=b;J[i+8>>2]=a;}a=j+8|0;break a}O:{if(!g){break O}b=J[d+28>>2];a=(b<<2)+26040|0;P:{if(J[a>>2]==(d|0)){J[a>>2]=c;if(c){break P}j=ll(b)&j;J[6435]=j;break O}J[g+(J[g+16>>2]==(d|0)?16:20)>>2]=c;if(!c){break O}}J[c+24>>2]=g;a=J[d+16>>2];if(a){J[c+16>>2]=a;J[a+24>>2]=c;}a=J[d+20>>2];if(!a){break O}J[c+20>>2]=a;J[a+24>>2]=c;}Q:{if(f>>>0<=15){a=f+h|0;J[d+4>>2]=a|3;a=a+d|0;J[a+4>>2]=J[a+4>>2]|1;break Q}J[d+4>>2]=h|3;e=d+h|0;J[e+4>>2]=f|1;J[e+f>>2]=f;if(f>>>0<=255){b=(f&-8)+25776|0;c=J[6434];a=1<<(f>>>3);R:{if(!(c&a)){J[6434]=a|c;a=b;break R}a=J[b+8>>2];}J[b+8>>2]=e;J[a+12>>2]=e;J[e+12>>2]=b;J[e+8>>2]=a;break Q}a=31;if(f>>>0<=16777215){a=S(f>>>8|0);a=((f>>>38-a&1)-(a<<1)|0)+62|0;}J[e+28>>2]=a;J[e+16>>2]=0;J[e+20>>2]=0;b=(a<<2)+26040|0;S:{c=1<<a;T:{if(!(c&j)){J[6435]=c|j;J[b>>2]=e;break T}a=f<<((a|0)!=31?25-(a>>>1|0)|0:0);h=J[b>>2];while(1){b=h;if((J[b+4>>2]&-8)==(f|0)){break S}c=a>>>29|0;a=a<<1;c=(c&4)+b|0;h=J[c+16>>2];if(h){continue}break}J[c+16>>2]=e;}J[e+24>>2]=b;J[e+12>>2]=e;J[e+8>>2]=e;break Q}a=J[b+8>>2];J[a+12>>2]=e;J[b+8>>2]=e;J[e+24>>2]=0;J[e+12>>2]=b;J[e+8>>2]=a;}a=d+8|0;break a}U:{if(!i){break U}b=J[c+28>>2];a=(b<<2)+26040|0;V:{if(J[a>>2]==(c|0)){J[a>>2]=d;if(d){break V}m=25740,n=ll(b)&j,J[m>>2]=n;break U}J[i+(J[i+16>>2]==(c|0)?16:20)>>2]=d;if(!d){break U}}J[d+24>>2]=i;a=J[c+16>>2];if(a){J[d+16>>2]=a;J[a+24>>2]=d;}a=J[c+20>>2];if(!a){break U}J[d+20>>2]=a;J[a+24>>2]=d;}W:{if(f>>>0<=15){a=f+h|0;J[c+4>>2]=a|3;a=a+c|0;J[a+4>>2]=J[a+4>>2]|1;break W}J[c+4>>2]=h|3;d=c+h|0;J[d+4>>2]=f|1;J[d+f>>2]=f;if(k){b=(k&-8)+25776|0;e=J[6439];a=1<<(k>>>3);X:{if(!(a&g)){J[6434]=a|g;a=b;break X}a=J[b+8>>2];}J[b+8>>2]=e;J[a+12>>2]=e;J[e+12>>2]=b;J[e+8>>2]=a;}J[6439]=d;J[6436]=f;}a=c+8|0;}Fa=l+16|0;return a|0}function Cd(a,b,c){var d=0,e=Q(0),f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=0,m=Q(0),n=0,o=0,p=0,q=0,r=0,s=0,t=Q(0),u=0,v=Q(0),w=0,x=0,y=0,z=Q(0),A=Q(0),B=Q(0),C=0,D=0,E=Q(0),F=Q(0),G=Q(0),M=Q(0),O=Q(0),R=Q(0),S=Q(0),T=Q(0),U=0,V=Q(0),W=0,X=Q(0),Z=Q(0),_=Q(0),$=Q(0);d=Fa-176|0;Fa=d;J[5902]=J[5902]+1;r=c- -64|0;w=J[r+4>>2];J[d+168>>2]=J[r>>2];J[d+172>>2]=w;r=J[c+60>>2];J[d+160>>2]=J[c+56>>2];J[d+164>>2]=r;r=J[c+84>>2];J[d+152>>2]=J[c+80>>2];J[d+156>>2]=r;r=J[c+76>>2];J[d+144>>2]=J[c+72>>2];J[d+148>>2]=r;r=c;w=b;c=d+32|0;b=L[b+4>>1];J[c+108>>2]=b;a:{b:{if(!b){break b}while(1){b=c+P(o,36)|0;l=o+w|0;p=K[l+6|0];J[b+28>>2]=p;l=K[l+9|0];J[b+32>>2]=l;l=J[r+44>>2]+(l<<3)|0;e=N[l>>2];f=N[l+4>>2];m=N[d+160>>2];j=N[d+168>>2];l=J[r+16>>2]+(p<<3)|0;g=N[l>>2];h=N[l+4>>2];i=N[d+172>>2];k=Q(Q(Q(j*g)+Q(h*i))+N[d+164>>2]);N[b+4>>2]=k;g=Q(m+Q(Q(i*g)-Q(h*j)));N[b>>2]=g;h=N[d+144>>2];i=N[d+148>>2];m=N[d+152>>2];j=N[d+156>>2];J[b+24>>2]=0;i=Q(i+Q(Q(m*e)+Q(f*j)));N[b+12>>2]=i;e=Q(h+Q(Q(j*e)-Q(f*m)));N[b+8>>2]=e;N[b+20>>2]=i-k;N[b+16>>2]=e-g;o=o+1|0;b=J[c+108>>2];if((o|0)<(b|0)){continue}break}if((b|0)>1){f=N[w>>2];e=Q(0);c:{d:{switch(b-2|0){case 0:e=Q(N[c+16>>2]-N[c+52>>2]);j=Q(e*e);e=Q(N[c+20>>2]-N[c+56>>2]);e=Q(Y(Q(j+Q(e*e))));break c;case 1:break d;default:break c}}e=N[c+16>>2];m=N[c+20>>2];e=Q(Q(Q(N[c+52>>2]-e)*Q(N[c+92>>2]-m))-Q(Q(N[c+88>>2]-e)*Q(N[c+56>>2]-m)));}if(!(Q(f+f)<e|Q(f*Q(.5))>e|e<Q(1.1920928955078125e-7))){break a}J[c+108>>2]=0;break b}if(b){break a}}J[c+28>>2]=0;J[c+32>>2]=0;b=J[r+44>>2];e=N[b>>2];f=N[b+4>>2];m=N[d+160>>2];j=N[d+168>>2];b=J[r+16>>2];g=N[b>>2];h=N[b+4>>2];i=N[d+172>>2];k=Q(Q(Q(j*g)+Q(h*i))+N[d+164>>2]);N[c+4>>2]=k;g=Q(m+Q(Q(i*g)-Q(h*j)));N[c>>2]=g;m=N[d+156>>2];h=N[d+144>>2];i=N[d+148>>2];j=N[d+152>>2];J[c+108>>2]=1;J[c+24>>2]=1065353216;i=Q(i+Q(Q(j*e)+Q(f*m)));N[c+12>>2]=i;e=Q(h+Q(Q(m*e)-Q(f*j)));N[c+8>>2]=e;N[c+20>>2]=i-k;N[c+16>>2]=e-g;}v=N[d+156>>2];E=N[d+172>>2];m=N[3186];j=N[3185];C=J[5903];M=N[d+152>>2];V=Q(-M);F=N[d+168>>2];X=Q(-F);c=J[d+140>>2];Z=N[d+148>>2];_=N[d+144>>2];$=N[d+164>>2];O=N[d+160>>2];e:{f:{g:{h:{i:{j:{k:{l:{while(1){W=(c|0)<=0;m:{if(W){break m}b=0;if((c|0)!=1){l=c&-2;o=0;while(1){p=b<<2;n=d+20|0;s=d+32|0;q=s+P(b,36)|0;J[p+n>>2]=J[q+28>>2];x=p;p=d+8|0;J[x+p>>2]=J[q+32>>2];x=n;n=b|1;q=n<<2;n=s+P(n,36)|0;J[x+q>>2]=J[n+28>>2];J[p+q>>2]=J[n+32>>2];b=b+2|0;o=o+2|0;if((l|0)!=(o|0)){continue}break}}if(!(c&1)){break m}o=b<<2;b=(d+32|0)+P(b,36)|0;J[o+(d+20|0)>>2]=J[b+28>>2];J[o+(d+8|0)>>2]=J[b+32>>2];}n:{o:{p:{q:{r:{s:{t:{u:{b=c;switch(b-2|0){case 1:break t;case 0:break u;default:break s}}k=N[d+84>>2];g=N[d+48>>2];e=Q(k-g);t=N[d+88>>2];h=N[d+52>>2];f=Q(t-h);i=Q(Q(g*e)+Q(h*f));if(i>=Q(0)){J[d+140>>2]=1;J[d+56>>2]=1065353216;break o}k=Q(Q(k*e)+Q(t*f));if(!(k<=Q(0))){break r}J[d+140>>2]=1;J[d+92>>2]=1065353216;b=J[d+80>>2];J[d+40>>2]=J[d+76>>2];J[d+44>>2]=b;b=J[d+88>>2];J[d+48>>2]=J[d+84>>2];J[d+52>>2]=b;b=J[d+96>>2];J[d+56>>2]=J[d+92>>2];J[d+60>>2]=b;J[d- -64>>2]=J[d+100>>2];b=J[d+72>>2];J[d+32>>2]=J[d+68>>2];J[d+36>>2]=b;break o}e=N[d+120>>2];f=N[d+48>>2];z=Q(e-f);g=N[d+124>>2];h=N[d+52>>2];B=Q(g-h);i=N[d+84>>2];A=Q(i-f);k=N[d+88>>2];R=Q(k-h);G=Q(Q(f*A)+Q(h*R));t=Q(Q(f*z)+Q(h*B));v:{if(!(!(G>=Q(0))|!(t>=Q(0)))){J[d+140>>2]=1;J[d+56>>2]=1065353216;break v}S=Q(Q(A*B)-Q(z*R));T=Q(Q(Q(f*k)-Q(i*h))*S);A=Q(Q(i*A)+Q(k*R));if(!(!(T<=Q(0))|(!(A>Q(0))|!(G<Q(0))))){J[d+140>>2]=2;e=Q(Q(1)/Q(A-G));N[d+92>>2]=e*Q(-G);N[d+56>>2]=A*e;break v}z=Q(Q(e*z)+Q(g*B));B=Q(S*Q(Q(e*h)-Q(f*g)));if(!(!(t<Q(0))|(!(B<=Q(0))|!(z>Q(0))))){J[d+140>>2]=2;e=Q(Q(1)/Q(z-t));N[d+128>>2]=e*Q(-t);N[d+56>>2]=z*e;J[d+100>>2]=J[d+136>>2];b=J[d+132>>2];J[d+92>>2]=J[d+128>>2];J[d+96>>2]=b;b=J[d+124>>2];J[d+84>>2]=J[d+120>>2];J[d+88>>2]=b;b=J[d+116>>2];J[d+76>>2]=J[d+112>>2];J[d+80>>2]=b;b=J[d+108>>2];J[d+68>>2]=J[d+104>>2];J[d+72>>2]=b;break v}h=Q(e-i);t=Q(g-k);f=Q(Q(i*h)+Q(k*t));if(!(!(A<=Q(0))|!(f>=Q(0)))){J[d+140>>2]=1;J[d+92>>2]=1065353216;b=J[d+72>>2];J[d+32>>2]=J[d+68>>2];J[d+36>>2]=b;b=J[d+80>>2];J[d+40>>2]=J[d+76>>2];J[d+44>>2]=b;b=J[d+88>>2];J[d+48>>2]=J[d+84>>2];J[d+52>>2]=b;b=J[d+96>>2];J[d+56>>2]=J[d+92>>2];J[d+60>>2]=b;J[d+64>>2]=J[d+100>>2];break v}w:{h=Q(Q(e*h)+Q(g*t));if(!(!(z<=Q(0))|!(h<=Q(0)))){J[d+140>>2]=1;J[d+128>>2]=1065353216;break w}e=Q(Q(Q(i*g)-Q(e*k))*S);if(!(!(f<Q(0))|(!(e<=Q(0))|!(h>Q(0))))){J[d+140>>2]=2;e=Q(Q(1)/Q(h-f));N[d+128>>2]=e*Q(-f);N[d+92>>2]=h*e;break w}J[d+140>>2]=3;f=Q(Q(1)/Q(T+Q(e+B)));N[d+128>>2]=T*f;N[d+92>>2]=B*f;N[d+56>>2]=e*f;break v}b=J[d+108>>2];J[d+32>>2]=J[d+104>>2];J[d+36>>2]=b;b=J[d+116>>2];J[d+40>>2]=J[d+112>>2];J[d+44>>2]=b;b=J[d+124>>2];J[d+48>>2]=J[d+120>>2];J[d+52>>2]=b;b=J[d+132>>2];J[d+56>>2]=J[d+128>>2];J[d+60>>2]=b;J[d+64>>2]=J[d+136>>2];}b=J[d+140>>2];}f=j;e=m;x:{switch(b-1|0){case 0:break o;case 2:break p;case 1:break x;default:break n}}h=N[d+52>>2];f=Q(N[d+88>>2]-h);g=N[d+48>>2];e=Q(N[d+84>>2]-g);break q}J[d+140>>2]=2;t=Q(Q(1)/Q(k-i));N[d+92>>2]=t*Q(-i);N[d+56>>2]=k*t;}y:{if(Q(Q(g*f)-Q(e*h))>Q(0)){f=Q(-f);break y}e=Q(-e);}b=2;break n}b=J[5904];J[5904]=(b|0)>(u|0)?b:u;b=u;break j}e=Q(-N[d+52>>2]);f=Q(-N[d+48>>2]);b=1;}if(Q(Q(f*f)+Q(e*e))<Q(14210854715202004e-30)){break l}l=(d+32|0)+P(b,36)|0;n=J[r+16>>2];p=0;o=0;s=J[r+20>>2];z:{if((s|0)<2){break z}h=Q(Q(E*Q(-f))-Q(F*e));i=Q(Q(F*f)-Q(E*e));g=Q(Q(N[n>>2]*h)+Q(i*N[n+4>>2]));b=1;q=s-1|0;U=q&1;if((s|0)!=2){x=q&-2;s=0;while(1){q=n+(b<<3)|0;k=Q(Q(N[q>>2]*h)+Q(i*N[q+4>>2]));q=k>g;D=b+1|0;y=n+(D<<3)|0;t=Q(Q(N[y>>2]*h)+Q(i*N[y+4>>2]));g=q?k:g;y=t>g;g=y?t:g;o=y?D:q?b:o;b=b+2|0;s=s+2|0;if((x|0)!=(s|0)){continue}break}}if(!U){break z}x=b;b=n+(b<<3)|0;o=Q(Q(N[b>>2]*h)+Q(i*N[b+4>>2]))>g?x:o;}J[l+28>>2]=o;b=n+(o<<3)|0;g=N[b>>2];h=N[b+4>>2];i=Q(Q(Q(F*g)+Q(E*h))+$);N[l+4>>2]=i;k=Q(O+Q(Q(E*g)+Q(h*X)));N[l>>2]=k;n=J[r+44>>2];s=J[r+48>>2];A:{if((s|0)<2){break A}h=Q(Q(v*f)+Q(e*M));e=Q(Q(V*f)+Q(e*v));g=Q(Q(N[n>>2]*h)+Q(e*N[n+4>>2]));b=1;q=s-1|0;U=q&1;if((s|0)!=2){x=q&-2;s=0;while(1){q=n+(b<<3)|0;f=Q(Q(N[q>>2]*h)+Q(e*N[q+4>>2]));q=f>g;D=b+1|0;y=n+(D<<3)|0;t=Q(Q(N[y>>2]*h)+Q(e*N[y+4>>2]));f=q?f:g;y=t>f;g=y?t:f;p=y?D:q?b:p;b=b+2|0;s=s+2|0;if((x|0)!=(s|0)){continue}break}}if(!U){break A}x=b;b=n+(b<<3)|0;p=Q(Q(N[b>>2]*h)+Q(e*N[b+4>>2]))>g?x:p;}J[l+32>>2]=p;b=n+(p<<3)|0;e=N[b>>2];f=N[b+4>>2];g=Q(Q(Q(M*e)+Q(v*f))+Z);N[l+12>>2]=g;e=Q(_+Q(Q(v*e)+Q(f*V)));N[l+8>>2]=e;N[l+20>>2]=g-i;N[l+16>>2]=e-k;b=0;C=C+1|0;J[5903]=C;u=u+1|0;B:{if(!W){while(1){l=b<<2;if(J[l+(d+20|0)>>2]==(o|0)&J[l+(d+8|0)>>2]==(p|0)){break B}b=b+1|0;if((c|0)!=(b|0)){continue}break}}c=J[d+140>>2]+1|0;J[d+140>>2]=c;b=20;if((u|0)!=20){continue}break k}break}b=J[d+140>>2];}c=b;b=u;}u=J[5904];J[5904]=(b|0)<(u|0)?u:b;C:{switch(c-1|0){case 0:break h;case 2:break j;case 1:break C;default:break i}}m=N[d+72>>2];j=N[d+36>>2];e=N[d+56>>2];f=N[d+92>>2];g=Q(Q(e*N[d+32>>2])+Q(f*N[d+68>>2]));N[a>>2]=g;m=Q(Q(e*j)+Q(f*m));N[a+4>>2]=m;j=N[d+76>>2];h=N[d+44>>2];i=N[d+80>>2];k=N[d+40>>2];J[a+20>>2]=b;h=Q(Q(e*h)+Q(f*i));N[a+12>>2]=h;e=Q(Q(e*k)+Q(f*j));N[a+8>>2]=e;e=Q(g-e);j=Q(e*e);e=Q(m-h);N[a+16>>2]=Y(Q(j+Q(e*e)));e=Q(N[d+48>>2]-N[d+84>>2]);j=Q(e*e);e=Q(N[d+52>>2]-N[d+88>>2]);g=Q(Y(Q(j+Q(e*e))));u=a+16|0;b=2;c=2;break g}g=N[d+108>>2];h=N[d+72>>2];f=N[d+128>>2];i=N[d+36>>2];e=N[d+104>>2];k=N[d+32>>2];m=N[d+56>>2];v=N[d+68>>2];j=N[d+92>>2];J[a+20>>2]=b;e=Q(Q(Q(m*k)+Q(j*v))+Q(f*e));N[a+8>>2]=e;N[a>>2]=e;f=Q(Q(Q(m*i)+Q(j*h))+Q(f*g));N[a+12>>2]=f;N[a+4>>2]=f;e=Q(e-e);j=Q(e*e);e=Q(f-f);N[a+16>>2]=Y(Q(j+Q(e*e)));e=N[d+48>>2];f=N[d+52>>2];g=Q(Q(Q(N[d+84>>2]-e)*Q(N[d+124>>2]-f))-Q(Q(N[d+120>>2]-e)*Q(N[d+88>>2]-f)));u=a+16|0;b=3;c=3;break g}J[a+20>>2]=b;e=Q(N[a>>2]-N[a+8>>2]);j=Q(e*e);e=Q(N[a+4>>2]-N[a+12>>2]);N[a+16>>2]=Y(Q(j+Q(e*e)));I[w+4>>1]=c;J[w>>2]=0;u=a+16|0;if((c|0)>0){break f}break e}O=N[d+36>>2];o=J[d+36>>2];e=N[d+32>>2];J[a>>2]=J[d+32>>2];J[a+4>>2]=o;f=N[d+40>>2];u=J[d+40>>2];m=N[d+44>>2];l=J[d+44>>2];J[a+20>>2]=b;J[a+8>>2]=u;J[a+12>>2]=l;e=Q(e-f);j=Q(e*e);e=Q(O-m);N[a+16>>2]=Y(Q(j+Q(e*e)));u=a+16|0;g=Q(0);b=c;c=1;}I[w+4>>1]=b;N[w>>2]=g;}s=c&1;b=0;if((c|0)!=1){C=c&-2;o=w+6|0;l=w+9|0;c=0;while(1){n=d+32|0;p=n+P(b,36)|0;H[b+o|0]=J[p+28>>2];H[b+l|0]=J[p+32>>2];p=b|1;n=n+P(p,36)|0;H[o+p|0]=J[n+28>>2];H[l+p|0]=J[n+32>>2];b=b+2|0;c=c+2|0;if((C|0)!=(c|0)){continue}break}}if(!s){break e}c=b+w|0;b=(d+32|0)+P(b,36)|0;H[c+6|0]=J[b+28>>2];H[c+9|0]=J[b+32>>2];}D:{if(!K[r+88|0]){break D}e=N[u>>2];f=N[r+24>>2];m=N[r+52>>2];j=Q(f+m);if(!(!(e>j)|!(e>Q(1.1920928955078125e-7)))){N[a+16>>2]=e-j;e=N[a+8>>2];j=N[a>>2];g=Q(e-j);i=N[a+12>>2];k=N[a+4>>2];h=Q(i-k);v=Q(Y(Q(Q(g*g)+Q(h*h))));if(!(v<Q(1.1920928955078125e-7))){v=Q(Q(1)/v);h=Q(h*v);g=Q(g*v);}N[a+12>>2]=i-Q(m*h);N[a+8>>2]=e-Q(m*g);N[a+4>>2]=k+Q(f*h);N[a>>2]=j+Q(f*g);break D}J[a+16>>2]=0;e=Q(Q(N[a+4>>2]+N[a+12>>2])*Q(.5));N[a+12>>2]=e;f=Q(Q(N[a>>2]+N[a+8>>2])*Q(.5));N[a+8>>2]=f;N[a+4>>2]=e;N[a>>2]=f;}Fa=d+176|0;}function se(a,b,c){a=a|0;b=b|0;c=c|0;var d=Q(0),e=Q(0),f=0,g=Q(0),h=Q(0),i=0,j=0,k=0,l=0,m=0,n=Q(0),o=Q(0),p=0,q=0,r=0,s=Q(0),t=0,u=Q(0),v=Q(0),w=Q(0),x=0,y=Q(0),z=Q(0),A=Q(0),B=Q(0),C=Q(0),D=Q(0),E=Q(0),F=Q(0),G=Q(0),H=Q(0),I=Q(0);a:{k=a;b:{if((c|0)<=2){break b}a=J[b+4>>2];f=Fa-96|0;J[f+32>>2]=J[b>>2];J[f+36>>2]=a;i=1;c=(c|0)>=8?8:c;c:{if((c|0)==1){break c}m=J[b+12>>2];g=N[b+12>>2];l=J[b+8>>2];e=N[b+8>>2];a=0;d:{while(1){j=(f+32|0)+(a<<3)|0;d=Q(e-N[j>>2]);o=Q(d*d);d=Q(g-N[j+4>>2]);if(Q(o+Q(d*d))<Q(624999984211172e-20)){break d}a=a+1|0;if((a|0)!=1){continue}break}J[f+44>>2]=m;J[f+40>>2]=l;i=2;}if((c|0)==2){break c}m=J[b+20>>2];g=N[b+20>>2];l=J[b+16>>2];e=N[b+16>>2];a=0;e:{while(1){j=(f+32|0)+(a<<3)|0;d=Q(e-N[j>>2]);o=Q(d*d);d=Q(g-N[j+4>>2]);if(Q(o+Q(d*d))<Q(624999984211172e-20)){break e}a=a+1|0;if((i|0)!=(a|0)){continue}break}a=(f+32|0)+(i<<3)|0;J[a+4>>2]=m;J[a>>2]=l;i=i+1|0;}if((c|0)==3){break c}g=N[b+28>>2];m=J[b+28>>2];e=N[b+24>>2];l=J[b+24>>2];f:{if((i|0)>0){a=0;while(1){j=(f+32|0)+(a<<3)|0;d=Q(e-N[j>>2]);o=Q(d*d);d=Q(g-N[j+4>>2]);if(Q(o+Q(d*d))<Q(624999984211172e-20)){break f}a=a+1|0;if((i|0)!=(a|0)){continue}break}}a=(f+32|0)+(i<<3)|0;J[a+4>>2]=m;J[a>>2]=l;i=i+1|0;}if((c|0)==4){break c}g=N[b+36>>2];m=J[b+36>>2];e=N[b+32>>2];l=J[b+32>>2];g:{if((i|0)>0){a=0;while(1){j=(f+32|0)+(a<<3)|0;d=Q(e-N[j>>2]);o=Q(d*d);d=Q(g-N[j+4>>2]);if(Q(o+Q(d*d))<Q(624999984211172e-20)){break g}a=a+1|0;if((i|0)!=(a|0)){continue}break}}a=(f+32|0)+(i<<3)|0;J[a+4>>2]=m;J[a>>2]=l;i=i+1|0;}if((c|0)==5){break c}g=N[b+44>>2];m=J[b+44>>2];e=N[b+40>>2];l=J[b+40>>2];h:{if((i|0)>0){a=0;while(1){j=(f+32|0)+(a<<3)|0;d=Q(e-N[j>>2]);o=Q(d*d);d=Q(g-N[j+4>>2]);if(Q(o+Q(d*d))<Q(624999984211172e-20)){break h}a=a+1|0;if((i|0)!=(a|0)){continue}break}}a=(f+32|0)+(i<<3)|0;J[a+4>>2]=m;J[a>>2]=l;i=i+1|0;}if((c|0)==6){break c}g=N[b+52>>2];m=J[b+52>>2];e=N[b+48>>2];l=J[b+48>>2];i:{if((i|0)>0){a=0;while(1){j=(f+32|0)+(a<<3)|0;d=Q(e-N[j>>2]);o=Q(d*d);d=Q(g-N[j+4>>2]);if(Q(o+Q(d*d))<Q(624999984211172e-20)){break i}a=a+1|0;if((i|0)!=(a|0)){continue}break}}a=(f+32|0)+(i<<3)|0;J[a+4>>2]=m;J[a>>2]=l;i=i+1|0;}if((c|0)==7){break c}g=N[b+60>>2];c=J[b+60>>2];e=N[b+56>>2];b=J[b+56>>2];if((i|0)>0){a=0;while(1){m=(f+32|0)+(a<<3)|0;d=Q(e-N[m>>2]);o=Q(d*d);d=Q(g-N[m+4>>2]);if(Q(o+Q(d*d))<Q(624999984211172e-20)){break c}a=a+1|0;if((i|0)!=(a|0)){continue}break}}a=(f+32|0)+(i<<3)|0;J[a+4>>2]=c;J[a>>2]=b;i=i+1|0;}if((i|0)<3){break b}g=N[f+32>>2];e=N[f+40>>2];j:{if(!(g<e)){c=0;if(!(N[f+44>>2]<N[f+36>>2])|e!=g){break j}}g=e;c=1;}k:{if((i|0)==2){break k}e=N[f+48>>2];if(!(!(e>g)&(!(N[f+52>>2]<N[(f+32|c<<3)+4>>2])|e!=g))){c=2;g=e;}if((i|0)==3){break k}e=N[f+56>>2];if(!(!(e>g)&(!(N[f+60>>2]<N[((f+32|0)+(c<<3)|0)+4>>2])|e!=g))){c=3;g=e;}if((i|0)==4){break k}e=N[f+64>>2];if(!(!(e>g)&(!(N[f+68>>2]<N[((f+32|0)+(c<<3)|0)+4>>2])|e!=g))){c=4;g=e;}if((i|0)==5){break k}e=N[f+72>>2];if(!(!(e>g)&(!(N[f+76>>2]<N[((f+32|0)+(c<<3)|0)+4>>2])|e!=g))){c=5;g=e;}if((i|0)==6){break k}e=N[f+80>>2];if(!(!(e>g)&(!(N[f+84>>2]<N[((f+32|0)+(c<<3)|0)+4>>2])|e!=g))){c=6;g=e;}if((i|0)==7){break k}e=N[f+88>>2];if(!(e>g)&(!(N[f+92>>2]<N[((f+32|0)+(c<<3)|0)+4>>2])|e!=g)){break k}c=7;}l:{m:{n:{if((i|0)>=2){s=N[f+92>>2];u=N[f+84>>2];v=N[f+76>>2];w=N[f+68>>2];y=N[f+60>>2];z=N[f+52>>2];b=0;A=N[f+88>>2];B=N[f+80>>2];C=N[f+72>>2];D=N[f+64>>2];E=N[f+56>>2];F=N[f+48>>2];G=N[f+44>>2];H=N[f+40>>2];I=N[f+36>>2];o=N[f+32>>2];r=(i|0)==5;p=(i|0)==6;t=(i|0)==7;a=c;while(1){m=b;l=a;J[f+(b<<2)>>2]=a;q=(f+32|0)+(a<<3)|0;j=q|4;a=1;o:{if(!l){break o}d=N[q>>2];g=Q(o-d);h=N[j>>2];e=Q(G-h);d=Q(H-d);h=Q(I-h);n=Q(Q(g*e)-Q(d*h));a=n<Q(0);if(!(Q(Q(d*d)+Q(e*e))>Q(Q(g*g)+Q(h*h)))|n!=Q(0)){break o}a=1;}p:{if((i|0)==2){break p}b=2;q:{if((a|0)==(l|0)){break q}b=a;a=f+32|a<<3;d=N[q>>2];g=Q(N[a>>2]-d);h=N[j>>2];e=Q(z-h);d=Q(F-d);h=Q(N[a+4>>2]-h);n=Q(Q(g*e)-Q(d*h));b=n<Q(0)?2:b;if(!(Q(Q(d*d)+Q(e*e))>Q(Q(g*g)+Q(h*h)))|n!=Q(0)){break q}b=2;}if((i|0)==3){a=b;break p}a=3;r:{if((b|0)==(l|0)){break r}a=(f+32|0)+(b<<3)|0;d=N[q>>2];g=Q(N[a>>2]-d);h=N[j>>2];e=Q(y-h);d=Q(E-d);h=Q(N[a+4>>2]-h);n=Q(Q(g*e)-Q(d*h));a=n<Q(0)?3:b;if(!(Q(Q(d*d)+Q(e*e))>Q(Q(g*g)+Q(h*h)))|n!=Q(0)){break r}a=3;}if((i|0)==4){break p}b=4;s:{if((a|0)==(l|0)){break s}b=a;a=(f+32|0)+(a<<3)|0;d=N[q>>2];g=Q(N[a>>2]-d);h=N[j>>2];e=Q(w-h);d=Q(D-d);h=Q(N[a+4>>2]-h);n=Q(Q(g*e)-Q(d*h));b=n<Q(0)?4:b;if(!(Q(Q(d*d)+Q(e*e))>Q(Q(g*g)+Q(h*h)))|n!=Q(0)){break s}b=4;}if(r){a=b;break p}a=5;t:{if((b|0)==(l|0)){break t}a=(f+32|0)+(b<<3)|0;d=N[q>>2];g=Q(N[a>>2]-d);h=N[j>>2];e=Q(v-h);d=Q(C-d);h=Q(N[a+4>>2]-h);n=Q(Q(g*e)-Q(d*h));a=n<Q(0)?5:b;if(!(Q(Q(d*d)+Q(e*e))>Q(Q(g*g)+Q(h*h)))|n!=Q(0)){break t}a=5;}if(p){break p}b=6;u:{if((a|0)==(l|0)){break u}b=a;a=(f+32|0)+(a<<3)|0;d=N[q>>2];g=Q(N[a>>2]-d);h=N[j>>2];e=Q(u-h);d=Q(B-d);h=Q(N[a+4>>2]-h);n=Q(Q(g*e)-Q(d*h));b=n<Q(0)?6:b;if(!(Q(Q(d*d)+Q(e*e))>Q(Q(g*g)+Q(h*h)))|n!=Q(0)){break u}b=6;}if(t){a=b;break p}a=7;if((b|0)==(l|0)){break p}a=(f+32|0)+(b<<3)|0;d=N[q>>2];g=Q(N[a>>2]-d);h=N[j>>2];e=Q(s-h);d=Q(A-d);h=Q(N[a+4>>2]-h);n=Q(Q(g*e)-Q(d*h));a=n<Q(0)?7:b;if(!(Q(Q(d*d)+Q(e*e))>Q(Q(g*g)+Q(h*h)))|n!=Q(0)){break p}a=7;}b=m+1|0;if((a|0)!=(c|0)){continue}break}if(m>>>0>1){break n}break m}if(!c){break m}a=1;while(1){J[f+(a<<2)>>2]=0;a=a+1|0;continue}}J[k+148>>2]=b;b=m+1|0;c=b&3;i=0;a=0;if((m|0)!=2){q=b&-4;b=k+20|0;l=0;while(1){x=f+32|0;r=x;p=r+(J[f+(a<<2)>>2]<<3)|0;t=J[p+4>>2];j=b+(a<<3)|0;J[j>>2]=J[p>>2];J[j+4>>2]=t;j=a|1;p=b+(j<<3)|0;j=r+(J[f+(j<<2)>>2]<<3)|0;t=J[j+4>>2];J[p>>2]=J[j>>2];J[p+4>>2]=t;j=a|2;p=b+(j<<3)|0;j=r+(J[f+(j<<2)>>2]<<3)|0;r=J[j+4>>2];J[p>>2]=J[j>>2];J[p+4>>2]=r;j=a|3;r=b+(j<<3)|0;j=(J[f+(j<<2)>>2]<<3)+x|0;p=J[j+4>>2];J[r>>2]=J[j>>2];J[r+4>>2]=p;a=a+4|0;l=l+4|0;if((q|0)!=(l|0)){continue}break}}if(c){while(1){l=(f+32|0)+(J[f+(a<<2)>>2]<<3)|0;q=J[l+4>>2];b=(a<<3)+k|0;J[b+20>>2]=J[l>>2];J[b+24>>2]=q;a=a+1|0;i=i+1|0;if((c|0)!=(i|0)){continue}break}}l=k+20|0;a=0;while(1){c=a<<3;f=c+l|0;e=N[f>>2];b=a+1|0;i=l+((a>>>0<m>>>0?b:0)<<3)|0;d=N[i>>2];c=c+k|0;g=Q(N[i+4>>2]-N[f+4>>2]);N[c+84>>2]=g;e=Q(d-e);d=Q(-e);N[c+88>>2]=d;e=Q(Y(Q(Q(g*g)+Q(e*e))));if(!(e<Q(1.1920928955078125e-7))){o=g;g=Q(Q(1)/e);N[c+84>>2]=o*g;N[c+88>>2]=g*d;}c=(a|0)==(m|0);a=b;if(!c){continue}break}c=k+20|0;g=Q(0);e=Q(0);a=0;d=Q(0);while(1){l=c+(a<<3)|0;h=N[l>>2];b=a+1|0;f=c+((a>>>0<m>>>0?b:0)<<3)|0;s=N[f+4>>2];u=N[f>>2];v=N[l+4>>2];w=Q(Q(Q(h*s)-Q(u*v))*Q(.5));e=Q(e+w);o=Q(Q(v+Q(0))+s);s=Q(w*Q(.3333333432674408));d=Q(d+Q(o*s));g=Q(g+Q(Q(Q(h+Q(0))+u)*s));l=(a|0)!=(m|0);a=b;if(l){continue}break}h=Q(Q(1)/e);e=Q(h*d);g=Q(h*g);break l}J[k+84>>2]=0;J[k+88>>2]=-1082130432;J[k+20>>2]=-1082130432;J[k+24>>2]=-1082130432;J[k+148>>2]=4;J[k+108>>2]=-1082130432;J[k+112>>2]=0;J[k+100>>2]=0;J[k+104>>2]=1065353216;J[k+92>>2]=1065353216;J[k+96>>2]=0;J[k+44>>2]=-1082130432;J[k+48>>2]=1065353216;J[k+36>>2]=1065353216;J[k+40>>2]=1065353216;J[k+28>>2]=1065353216;J[k+32>>2]=-1082130432;e=Q(0);g=Q(0);}N[k+12>>2]=g;N[k+16>>2]=e;break a}J[k+84>>2]=0;J[k+88>>2]=-1082130432;J[k+20>>2]=-1082130432;J[k+24>>2]=-1082130432;J[k+148>>2]=4;J[k+12>>2]=0;J[k+16>>2]=0;J[k+108>>2]=-1082130432;J[k+112>>2]=0;J[k+100>>2]=0;J[k+104>>2]=1065353216;J[k+92>>2]=1065353216;J[k+96>>2]=0;J[k+44>>2]=-1082130432;J[k+48>>2]=1065353216;J[k+36>>2]=1065353216;J[k+40>>2]=1065353216;J[k+28>>2]=1065353216;J[k+32>>2]=-1082130432;}}function sf(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=Q(0),h=0,i=Q(0),j=0,k=Q(0),l=Q(0),m=Q(0),n=0,o=0,p=0,q=Q(0),r=0,s=Q(0);d=Fa+-64|0;Fa=d;b=J[a+102980>>2];a:{if(!b){break a}p=J[b+4>>2];b:{if(!(p&1)){break b}e=J[a+102948>>2];if(!e){break b}while(1){f=J[e+100>>2];if(f){while(1){c:{d:{e:{c=J[e>>2];if((c|0)==2){if(N[e+116>>2]==Q(0)){J[d+8>>2]=0;J[d+12>>2]=1065353216;J[d>>2]=1065353216;J[d+4>>2]=0;break c}b=L[e+4>>1];if(b&32){break e}break d}b=L[e+4>>1];if(!(b&32)){break d}f:{switch(c|0){case 0:J[d+8>>2]=1056964608;J[d+12>>2]=1065353216;J[d>>2]=1056964608;J[d+4>>2]=1063675494;break c;case 1:break f;default:break e}}J[d+8>>2]=1063675494;J[d+12>>2]=1065353216;J[d>>2]=1056964608;J[d+4>>2]=1056964608;break c}if(!(b&2)){J[d+8>>2]=1058642330;J[d+12>>2]=1065353216;J[d>>2]=1058642330;J[d+4>>2]=1058642330;break c}J[d+8>>2]=1060320051;J[d+12>>2]=1065353216;J[d>>2]=1063675494;J[d+4>>2]=1060320051;break c}J[d+8>>2]=1050253722;J[d+12>>2]=1065353216;J[d>>2]=1056964608;J[d+4>>2]=1056964608;}b=Fa-80|0;Fa=b;g:{h:{i:{j:{k:{c=J[f+12>>2];switch(J[c+4>>2]){case 0:break h;case 2:break i;case 3:break j;case 1:break k;default:break g}}l=N[e+12>>2];i=N[e+20>>2];k=N[c+12>>2];g=N[e+24>>2];m=N[c+16>>2];q=N[e+16>>2];N[b+4>>2]=Q(Q(i*k)+Q(g*m))+q;N[b>>2]=l+Q(Q(g*k)-Q(m*i));k=N[c+20>>2];m=N[c+24>>2];N[b+76>>2]=q+Q(Q(i*k)+Q(g*m));N[b+72>>2]=l+Q(Q(g*k)-Q(m*i));c=J[a+102980>>2];Ha[J[J[c>>2]+24>>2]](c,b,b+72|0,d);break g}o=J[c+16>>2];j=J[c+12>>2];i=N[d>>2];g=N[d+4>>2];l=N[d+8>>2];N[b+12>>2]=N[d+12>>2];N[b+8>>2]=l*Q(.75);N[b+4>>2]=g*Q(.75);N[b>>2]=i*Q(.75);i=N[e+12>>2];g=N[e+20>>2];l=N[j>>2];k=N[e+24>>2];m=N[j+4>>2];N[b+76>>2]=Q(Q(g*l)+Q(k*m))+N[e+16>>2];N[b+72>>2]=i+Q(Q(k*l)-Q(m*g));h=J[a+102980>>2];Ha[J[J[h>>2]+32>>2]](h,b+72|0,Q(4),d);if(K[c+36|0]){i=N[e+12>>2];g=N[e+20>>2];l=N[c+20>>2];k=N[e+24>>2];m=N[c+24>>2];N[b+68>>2]=Q(Q(g*l)+Q(k*m))+N[e+16>>2];N[b+64>>2]=i+Q(Q(k*l)-Q(m*g));h=J[a+102980>>2];n=b- -64|0;Ha[J[J[h>>2]+24>>2]](h,n,b+72|0,b);h=J[a+102980>>2];Ha[J[J[h>>2]+16>>2]](h,n,Q(.10000000149011612),b);}if((o|0)>=2){h=1;while(1){i=N[e+12>>2];g=N[e+20>>2];n=j+(h<<3)|0;l=N[n>>2];k=N[e+24>>2];m=N[n+4>>2];N[b+68>>2]=Q(Q(g*l)+Q(k*m))+N[e+16>>2];N[b+64>>2]=i+Q(Q(k*l)-Q(m*g));n=J[a+102980>>2];r=b- -64|0;Ha[J[J[n>>2]+24>>2]](n,b+72|0,r,d);n=J[a+102980>>2];Ha[J[J[n>>2]+32>>2]](n,r,Q(4),d);n=J[b+68>>2];J[b+72>>2]=J[b+64>>2];J[b+76>>2]=n;h=h+1|0;if((o|0)!=(h|0)){continue}break}}if(!K[c+37|0]){break g}i=N[e+12>>2];g=N[e+20>>2];l=N[c+28>>2];k=N[e+24>>2];m=N[c+32>>2];N[b+68>>2]=Q(Q(g*l)+Q(k*m))+N[e+16>>2];N[b+64>>2]=i+Q(Q(k*l)-Q(m*g));c=J[a+102980>>2];j=b- -64|0;Ha[J[J[c>>2]+24>>2]](c,b+72|0,j,b);c=J[a+102980>>2];Ha[J[J[c>>2]+16>>2]](c,j,Q(.10000000149011612),b);break g}h=J[c+148>>2];if((h|0)>0){i=N[e+24>>2];g=N[e+16>>2];l=N[e+12>>2];k=N[e+20>>2];m=Q(-k);j=0;while(1){o=j<<3;n=o+b|0;o=c+o|0;q=N[o+20>>2];s=N[o+24>>2];N[n+4>>2]=Q(Q(k*q)+Q(i*s))+g;N[n>>2]=l+Q(Q(i*q)+Q(s*m));j=j+1|0;if((h|0)!=(j|0)){continue}break}}c=J[a+102980>>2];Ha[J[J[c>>2]+12>>2]](c,b,h,d);break g}l=N[e+12>>2];i=N[e+20>>2];k=N[c+12>>2];g=N[e+24>>2];m=N[c+16>>2];N[b+4>>2]=Q(Q(i*k)+Q(g*m))+N[e+16>>2];N[b>>2]=l+Q(Q(g*k)-Q(m*i));l=N[c+8>>2];N[b+76>>2]=i+Q(g*Q(0));N[b+72>>2]=g-Q(i*Q(0));c=J[a+102980>>2];Ha[J[J[c>>2]+20>>2]](c,b,l,b+72|0,d);}Fa=b+80|0;f=J[f+4>>2];if(f){continue}break}}e=J[e+96>>2];if(e){continue}break}}l:{if(!(p&2)){break l}f=J[a+102952>>2];if(!f){break l}while(1){b=Fa-80|0;Fa=b;e=J[f+52>>2];c=J[f+48>>2];j=J[c+16>>2];J[b+72>>2]=J[c+12>>2];J[b+76>>2]=j;c=J[e+16>>2];J[b+64>>2]=J[e+12>>2];J[b+68>>2]=c;Ha[J[J[f>>2]>>2]](b+56|0,f);Ha[J[J[f>>2]+4>>2]](b+48|0,f);J[b+40>>2]=1061997773;J[b+44>>2]=1065353216;J[b+32>>2]=1056964608;J[b+36>>2]=1061997773;m:{n:{switch(J[f+4>>2]-3|0){case 0:e=J[a+102980>>2];Ha[J[J[e>>2]+24>>2]](e,b+56|0,b+48|0,b+32|0);break m;case 1:c=J[f+72>>2];e=b+8|0;J[e>>2]=J[f+68>>2];J[e+4>>2]=c;j=J[f+80>>2];c=b+24|0;J[c>>2]=J[f+76>>2];J[c+4>>2]=j;h=J[a+102980>>2];j=b+32|0;Ha[J[J[h>>2]+24>>2]](h,e,b+56|0,j);h=J[a+102980>>2];Ha[J[J[h>>2]+24>>2]](h,c,b+48|0,j);h=J[a+102980>>2];Ha[J[J[h>>2]+24>>2]](h,e,c,j);break m;case 2:J[b+16>>2]=0;J[b+20>>2]=1065353216;J[b+8>>2]=0;J[b+12>>2]=1065353216;c=J[a+102980>>2];j=b+56|0;e=b+8|0;Ha[J[J[c>>2]+32>>2]](c,j,Q(4),e);c=J[a+102980>>2];h=b+48|0;Ha[J[J[c>>2]+32>>2]](c,h,Q(4),e);J[b+16>>2]=1061997773;J[b+20>>2]=1065353216;J[b+8>>2]=1061997773;J[b+12>>2]=1061997773;c=J[a+102980>>2];Ha[J[J[c>>2]+24>>2]](c,j,h,e);break m;default:break n}}c=J[a+102980>>2];j=b+56|0;e=b+32|0;Ha[J[J[c>>2]+24>>2]](c,b+72|0,j,e);c=J[a+102980>>2];h=j;j=b+48|0;Ha[J[J[c>>2]+24>>2]](c,h,j,e);c=J[a+102980>>2];Ha[J[J[c>>2]+24>>2]](c,b- -64|0,j,e);}Fa=b+80|0;f=J[f+12>>2];if(f){continue}break}}o:{if(!(p&8)){break o}J[d+8>>2]=1063675494;J[d+12>>2]=1065353216;J[d>>2]=1050253722;J[d+4>>2]=1063675494;f=J[a+102928>>2];if(!f){break o}while(1){e=J[f+52>>2];c=J[f+60>>2];b=J[J[f+48>>2]+24>>2]+P(J[f+56>>2],28)|0;i=N[b+4>>2];g=N[b+12>>2];N[d+40>>2]=Q(N[b>>2]+N[b+8>>2])*Q(.5);N[d+44>>2]=Q(i+g)*Q(.5);b=J[e+24>>2]+P(c,28)|0;i=N[b+4>>2];g=N[b+12>>2];N[d+56>>2]=Q(N[b>>2]+N[b+8>>2])*Q(.5);N[d+60>>2]=Q(i+g)*Q(.5);b=J[a+102980>>2];Ha[J[J[b>>2]+24>>2]](b,d+40|0,d+56|0,d);f=J[f+12>>2];if(f){continue}break}}p:{if(!(p&4)){break p}J[d+48>>2]=1063675494;J[d+52>>2]=1065353216;J[d+40>>2]=1063675494;J[d+44>>2]=1050253722;b=J[a+102948>>2];if(!b){break p}while(1){q:{if(!(K[b+4|0]&32)){break q}f=J[b+100>>2];if(!f){break q}while(1){if(J[f+28>>2]>0){e=0;while(1){c=J[a+102872>>2]+P(J[(J[f+24>>2]+P(e,28)|0)+24>>2],40)|0;i=N[c+4>>2];g=N[c+8>>2];l=N[c>>2];k=N[c+12>>2];N[d+28>>2]=k;N[d+24>>2]=l;N[d+20>>2]=k;N[d+16>>2]=g;N[d+12>>2]=i;N[d+8>>2]=g;N[d+4>>2]=i;N[d>>2]=l;c=J[a+102980>>2];Ha[J[J[c>>2]+8>>2]](c,d,4,d+40|0);e=e+1|0;if((e|0)<J[f+28>>2]){continue}break}}f=J[f+4>>2];if(f){continue}break}}b=J[b+96>>2];if(b){continue}break}}if(!(p&16)){break a}f=J[a+102948>>2];if(!f){break a}while(1){b=J[f+24>>2];J[d+8>>2]=J[f+20>>2];J[d+12>>2]=b;b=J[f+16>>2];J[d>>2]=J[f+12>>2];J[d+4>>2]=b;b=J[f+48>>2];J[d>>2]=J[f+44>>2];J[d+4>>2]=b;b=J[a+102980>>2];Ha[J[J[b>>2]+28>>2]](b,d);f=J[f+96>>2];if(f){continue}break}}Fa=d- -64|0;}function yf(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=0,l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=0,q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),w=Q(0),x=0,y=Q(0);a:{if(K[a+102989|0]){break a}b:{c:{switch(J[b>>2]-1|0){case 2:c=pb(Ya(a,176),b);J[c>>2]=13068;d=J[b+24>>2];J[c+80>>2]=J[b+20>>2];J[c+84>>2]=d;d=J[b+32>>2];J[c+88>>2]=J[b+28>>2];J[c+92>>2]=d;N[c+104>>2]=N[b+36>>2];N[c+68>>2]=N[b+40>>2];N[c+72>>2]=N[b+44>>2];J[c+96>>2]=0;J[c+100>>2]=0;J[c+76>>2]=0;break b;case 4:c=pb(Ya(a,168),b);J[c>>2]=13612;j=N[b+24>>2];f=J[b+24>>2];g=N[b+20>>2];J[c+76>>2]=J[b+20>>2];J[c+80>>2]=f;d=J[c+52>>2];h=N[d+24>>2];i=Q(j-N[d+16>>2]);l=N[d+20>>2];g=Q(g-N[d+12>>2]);N[c+72>>2]=Q(h*i)-Q(l*g);N[c+68>>2]=Q(h*g)+Q(i*l);N[c+104>>2]=N[b+28>>2];J[c+96>>2]=0;J[c+100>>2]=0;N[c+84>>2]=N[b+32>>2];N[c+88>>2]=N[b+36>>2];J[c+108>>2]=0;J[c+92>>2]=0;break b;case 1:c=pb(Ya(a,256),b);J[c>>2]=13804;d=J[b+24>>2];J[c+68>>2]=J[b+20>>2];J[c+72>>2]=d;d=J[b+32>>2];J[c+76>>2]=J[b+28>>2];J[c+80>>2]=d;h=N[b+40>>2];f=J[b+40>>2];g=N[b+36>>2];d=J[b+36>>2];J[c+84>>2]=d;J[c+88>>2]=f;i=Q(Y(Q(Q(g*g)+Q(h*h))));if(!(i<Q(1.1920928955078125e-7))){i=Q(Q(1)/i);h=Q(i*h);N[c+88>>2]=h;g=Q(i*g);N[c+84>>2]=g;d=(C(g),v(2));}J[c+96>>2]=d;N[c+92>>2]=-h;g=N[b+44>>2];J[c+252>>2]=0;N[c+100>>2]=g;J[c+104>>2]=0;J[c+108>>2]=0;J[c+112>>2]=0;J[c+116>>2]=0;N[c+120>>2]=N[b+52>>2];N[c+124>>2]=N[b+56>>2];N[c+128>>2]=N[b+64>>2];N[c+132>>2]=N[b+68>>2];H[c+136|0]=K[b+48|0];d=K[b+60|0];J[c+184>>2]=0;J[c+188>>2]=0;J[c+140>>2]=0;H[c+137|0]=d;J[c+192>>2]=0;J[c+196>>2]=0;break b;case 0:c=pb(Ya(a,228),b);J[c>>2]=13968;d=J[b+24>>2];J[c+68>>2]=J[b+20>>2];J[c+72>>2]=d;d=J[b+32>>2];J[c+76>>2]=J[b+28>>2];J[c+80>>2]=d;g=N[b+36>>2];J[c+84>>2]=0;J[c+88>>2]=0;N[c+116>>2]=g;J[c+92>>2]=0;J[c+96>>2]=0;N[c+120>>2]=N[b+44>>2];N[c+124>>2]=N[b+48>>2];N[c+104>>2]=N[b+60>>2];N[c+108>>2]=N[b+56>>2];H[c+112|0]=K[b+40|0];d=K[b+52|0];J[c+224>>2]=0;H[c+100|0]=d;break b;case 3:c=pb(Ya(a,196),b);J[c>>2]=13888;d=J[b+24>>2];J[c+68>>2]=J[b+20>>2];J[c+72>>2]=d;d=J[b+32>>2];J[c+76>>2]=J[b+28>>2];J[c+80>>2]=d;d=J[b+40>>2];J[c+92>>2]=J[b+36>>2];J[c+96>>2]=d;d=J[b+48>>2];J[c+100>>2]=J[b+44>>2];J[c+104>>2]=d;h=N[b+52>>2];N[c+84>>2]=h;i=N[b+56>>2];N[c+88>>2]=i;g=N[b+60>>2];J[c+116>>2]=0;N[c+112>>2]=g;N[c+108>>2]=h+Q(g*i);break b;case 5:c=pb(Ya(a,276),b);J[c>>2]=13272;e=J[b+20>>2];J[c+68>>2]=e;p=J[b+24>>2];J[c+72>>2]=p;k=J[e+4>>2];J[c+76>>2]=k;x=J[p+4>>2];J[c+80>>2]=x;d=J[e+48>>2];J[c+84>>2]=d;f=J[e+52>>2];J[c+48>>2]=f;d:{if((k|0)==1){h=N[d+56>>2];i=N[f+56>>2];d=J[e+72>>2];J[c+108>>2]=J[e+68>>2];J[c+112>>2]=d;d=J[e+80>>2];J[c+92>>2]=J[e+76>>2];J[c+96>>2]=d;g=N[e+116>>2];J[c+124>>2]=0;J[c+128>>2]=0;N[c+140>>2]=g;g=Q(Q(i-h)-g);break d}h=N[d+20>>2];i=N[d+24>>2];l=N[f+20>>2];o=N[f+24>>2];q=N[f+16>>2];r=N[d+16>>2];n=N[f+12>>2];j=N[d+12>>2];s=N[e+72>>2];f=J[e+72>>2];m=N[e+68>>2];J[c+108>>2]=J[e+68>>2];J[c+112>>2]=f;t=N[e+80>>2];d=J[e+80>>2];g=N[e+76>>2];k=J[e+76>>2];J[c+92>>2]=k;J[c+96>>2]=d;N[c+140>>2]=N[e+100>>2];u=N[e+84>>2];k=J[e+84>>2];w=N[e+88>>2];e=J[e+88>>2];J[c+124>>2]=k;J[c+128>>2]=e;j=Q(n-j);n=t;j=Q(j+Q(Q(o*g)-Q(l*n)));g=Q(Q(q-r)+Q(Q(l*g)+Q(o*n)));g=Q(Q(Q(Q(Q(i*j)+Q(h*g))-m)*u)+Q(Q(Q(Q(i*g)-Q(h*j))-s)*w));}d=J[p+48>>2];J[c+88>>2]=d;f=J[p+52>>2];J[c+52>>2]=f;e:{if((x|0)==1){i=N[d+56>>2];l=N[f+56>>2];d=J[b+24>>2];f=J[d+72>>2];J[c+116>>2]=J[d+68>>2];J[c+120>>2]=f;f=J[d+80>>2];J[c+100>>2]=J[d+76>>2];J[c+104>>2]=f;h=N[d+116>>2];J[c+132>>2]=0;J[c+136>>2]=0;N[c+144>>2]=h;h=Q(Q(l-i)-h);break e}i=N[d+20>>2];l=N[d+24>>2];o=N[f+20>>2];q=N[f+24>>2];r=N[f+16>>2];n=N[d+16>>2];j=N[f+12>>2];m=N[d+12>>2];e=J[b+24>>2];d=e;u=N[d+68>>2];d=J[d+68>>2];s=N[e+72>>2];f=J[e+72>>2];J[c+116>>2]=d;J[c+120>>2]=f;t=N[e+80>>2];d=J[e+80>>2];h=N[e+76>>2];k=J[e+76>>2];J[c+100>>2]=k;J[c+104>>2]=d;N[c+144>>2]=N[e+100>>2];w=N[e+84>>2];k=J[e+84>>2];y=N[e+88>>2];e=J[e+88>>2];J[c+132>>2]=k;J[c+136>>2]=e;m=Q(j-m);j=t;m=Q(m+Q(Q(q*h)-Q(o*j)));h=Q(Q(r-n)+Q(Q(o*h)+Q(q*j)));h=Q(Q(Q(Q(Q(l*m)+Q(i*h))-u)*w)+Q(Q(Q(Q(l*h)-Q(i*m))-s)*y));}i=N[b+28>>2];J[c+156>>2]=0;N[c+152>>2]=i;N[c+148>>2]=Q(i*h)+g;break b;case 6:c=pb(Ya(a,224),b);J[c>>2]=14212;d=J[b+24>>2];J[c+76>>2]=J[b+20>>2];J[c+80>>2]=d;d=J[b+32>>2];J[c+84>>2]=J[b+28>>2];J[c+88>>2]=d;d=J[b+40>>2];f=J[b+36>>2];J[c+204>>2]=0;J[c+104>>2]=f;J[c+92>>2]=f;J[c+96>>2]=d;J[c+108>>2]=0;J[c+208>>2]=0;J[c+212>>2]=0;J[c+112>>2]=0;J[c+116>>2]=0;J[c+100>>2]=d^-2147483648;N[c+120>>2]=N[b+48>>2];N[c+124>>2]=N[b+52>>2];H[c+128|0]=K[b+44|0];N[c+68>>2]=N[b+56>>2];g=N[b+60>>2];J[c+216>>2]=0;J[c+220>>2]=0;N[c+72>>2]=g;J[c+172>>2]=0;J[c+176>>2]=0;J[c+180>>2]=0;J[c+184>>2]=0;break b;case 7:c=pb(Ya(a,208),b);J[c>>2]=14132;d=J[b+24>>2];J[c+80>>2]=J[b+20>>2];J[c+84>>2]=d;d=J[b+32>>2];J[c+88>>2]=J[b+28>>2];J[c+92>>2]=d;N[c+96>>2]=N[b+36>>2];N[c+68>>2]=N[b+40>>2];g=N[b+44>>2];J[c+112>>2]=0;J[c+104>>2]=0;J[c+108>>2]=0;N[c+72>>2]=g;break b;case 8:c=pb(Ya(a,180),b);J[c>>2]=13352;d=J[b+24>>2];J[c+68>>2]=J[b+20>>2];J[c+72>>2]=d;d=J[b+28>>2];f=J[b+32>>2];J[c+92>>2]=0;J[c+84>>2]=0;J[c+88>>2]=0;J[c+76>>2]=d;J[c+80>>2]=f;N[c+96>>2]=N[b+36>>2];N[c+100>>2]=N[b+40>>2];break b;case 9:c=pb(Ya(a,168),b);J[c>>2]=14052;d=J[b+24>>2];J[c+68>>2]=J[b+20>>2];J[c+72>>2]=d;d=J[b+32>>2];J[c+76>>2]=J[b+28>>2];J[c+80>>2]=d;N[c+84>>2]=N[b+36>>2];J[c+160>>2]=0;J[c+164>>2]=0;J[c+88>>2]=0;J[c+92>>2]=0;break b;case 10:break c;default:break b}}c=pb(Ya(a,192),b);J[c>>2]=13532;d=J[b+24>>2];J[c+68>>2]=J[b+20>>2];J[c+72>>2]=d;g=N[b+28>>2];J[c+88>>2]=0;J[c+80>>2]=0;J[c+84>>2]=0;N[c+76>>2]=g;N[c+92>>2]=N[b+32>>2];N[c+96>>2]=N[b+36>>2];N[c+100>>2]=N[b+40>>2];}J[c+8>>2]=0;d=J[a+102952>>2];J[c+12>>2]=d;if(d){J[d+8>>2]=c;}J[a+102952>>2]=c;J[a+102960>>2]=J[a+102960>>2]+1;J[c+24>>2]=0;J[c+20>>2]=c;a=J[c+52>>2];J[c+16>>2]=a;d=J[c+48>>2];f=J[d+108>>2];J[c+28>>2]=f;k=c+16|0;if(f){J[f+8>>2]=k;}J[d+108>>2]=k;J[c+40>>2]=0;J[c+32>>2]=d;J[c+36>>2]=c;d=J[a+108>>2];J[c+44>>2]=d;f=c+32|0;if(d){J[d+8>>2]=f;}J[a+108>>2]=f;if(K[b+16|0]){break a}a=J[J[b+12>>2]+112>>2];if(!a){break a}b=J[b+8>>2];while(1){if((b|0)==J[a>>2]){d=J[a+4>>2];J[d+4>>2]=J[d+4>>2]|8;}a=J[a+12>>2];if(a){continue}break}}return c|0}function Oc(a,b,c,d,e,f){a=a|0;b=+b;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,x=0,y=0,z=0,B=0,C=0;m=Fa-560|0;Fa=m;J[m+44>>2]=0;A(+b);g=v(1)|0;v(0)|0;a:{if((g|0)<0){s=1;y=1393;b=-b;A(+b);g=v(1)|0;v(0)|0;break a}if(e&2048){s=1;y=1396;break a}s=e&1;y=s?1399:1394;z=!s;}b:{if((g&2146435072)==2146435072){g=s+3|0;cb(a,32,c,g,e&-65537);bb(a,y,s);d=f&32;bb(a,b!=b?d?3570:6100:d?4060:6106,3);cb(a,32,c,g,e^8192);n=(c|0)<(g|0)?g:c;break b}u=m+16|0;c:{d:{e:{b=Sc(b,m+44|0);b=b+b;if(b!=0){g=J[m+44>>2];J[m+44>>2]=g-1;w=f|32;if((w|0)!=97){break e}break c}w=f|32;if((w|0)==97){break c}k=J[m+44>>2];l=(d|0)<0?6:d;break d}k=g-29|0;J[m+44>>2]=k;b=b*268435456;l=(d|0)<0?6:d;}q=(m+48|0)+((k|0)>=0?288:0)|0;h=q;while(1){if(b<4294967296&b>=0){d=~~b>>>0;}else {d=0;}J[h>>2]=d;h=h+4|0;b=(b-+(d>>>0))*1e9;if(b!=0){continue}break}f:{if((k|0)<=0){d=k;g=h;i=q;break f}i=q;d=k;while(1){o=(d|0)>=29?29:d;g=h-4|0;g:{if(i>>>0>g>>>0){break g}d=0;while(1){j=J[g>>2];x=d;d=o&31;if((o&63)>>>0>=32){n=j<<d;d=0;}else {n=(1<<d)-1&j>>>32-d;d=j<<d;}x=x+d|0;j=n+p|0;d=kl(x,d>>>0>x>>>0?j+1|0:j,1e9);B=g,C=x-jl(d,Ga,1e9,0)|0,J[B>>2]=C;g=g-4|0;if(i>>>0<=g>>>0){continue}break}if(!d){break g}i=i-4|0;J[i>>2]=d;}while(1){g=h;if(i>>>0<g>>>0){h=g-4|0;if(!J[h>>2]){continue}}break}d=J[m+44>>2]-o|0;J[m+44>>2]=d;h=g;if((d|0)>0){continue}break}}if((d|0)<0){t=((l+25>>>0)/9|0)+1|0;p=(w|0)==102;while(1){d=0-d|0;n=(d|0)>=9?9:d;h:{if(g>>>0<=i>>>0){h=J[i>>2];break h}o=1e9>>>n|0;j=-1<<n^-1;d=0;h=i;while(1){x=d;d=J[h>>2];J[h>>2]=x+(d>>>n|0);d=P(o,d&j);h=h+4|0;if(h>>>0<g>>>0){continue}break}h=J[i>>2];if(!d){break h}J[g>>2]=d;g=g+4|0;}d=n+J[m+44>>2]|0;J[m+44>>2]=d;i=(!h<<2)+i|0;h=p?q:i;g=g-h>>2>(t|0)?h+(t<<2)|0:g;if((d|0)<0){continue}break}}d=0;i:{if(g>>>0<=i>>>0){break i}d=P(q-i>>2,9);h=10;j=J[i>>2];if(j>>>0<10){break i}while(1){d=d+1|0;h=P(h,10);if(j>>>0>=h>>>0){continue}break}}h=(l-((w|0)!=102?d:0)|0)-((w|0)==103&(l|0)!=0)|0;if((h|0)<(P(g-q>>2,9)-9|0)){o=h+9216|0;j=(o|0)/9|0;k=((((k|0)<0?4:292)+m|0)+(j<<2)|0)-4048|0;h=10;n=o-P(j,9)|0;if((n|0)<=7){while(1){h=P(h,10);n=n+1|0;if((n|0)!=8){continue}break}}o=J[k>>2];t=(o>>>0)/(h>>>0)|0;p=o-P(h,t)|0;j=k+4|0;j:{if(!p&(j|0)==(g|0)){break j}k:{if(!(t&1)){b=9007199254740992;if(!(H[k-4|0]&1)|((h|0)!=1e9|i>>>0>=k>>>0)){break k}}b=9007199254740994;}r=(g|0)==(j|0)?1:1.5;j=h>>>1|0;r=j>>>0>p>>>0?.5:(j|0)==(p|0)?r:1.5;if(!(K[y|0]!=45|z)){r=-r;b=-b;}j=o-p|0;J[k>>2]=j;if(b+r==b){break j}d=h+j|0;J[k>>2]=d;if(d>>>0>=1e9){while(1){J[k>>2]=0;k=k-4|0;if(k>>>0<i>>>0){i=i-4|0;J[i>>2]=0;}d=J[k>>2]+1|0;J[k>>2]=d;if(d>>>0>999999999){continue}break}}d=P(q-i>>2,9);h=10;j=J[i>>2];if(j>>>0<10){break j}while(1){d=d+1|0;h=P(h,10);if(j>>>0>=h>>>0){continue}break}}h=k+4|0;g=g>>>0>h>>>0?h:g;}while(1){j=g;o=g>>>0<=i>>>0;if(!o){g=j-4|0;if(!J[g>>2]){continue}}break}l:{if((w|0)!=103){k=e&8;break l}h=l?l:1;g=(h|0)>(d|0)&(d|0)>-5;l=(g?d^-1:-1)+h|0;f=(g?-1:-2)+f|0;k=e&8;if(k){break l}g=-9;m:{if(o){break m}k=J[j-4>>2];if(!k){break m}n=10;g=0;if((k>>>0)%10|0){break m}while(1){h=g;g=g+1|0;n=P(n,10);if(!((k>>>0)%(n>>>0)|0)){continue}break}g=h^-1;}h=P(j-q>>2,9);if((f&-33)==70){k=0;g=(g+h|0)-9|0;g=(g|0)>0?g:0;l=(g|0)>(l|0)?l:g;break l}k=0;g=((d+h|0)+g|0)-9|0;g=(g|0)>0?g:0;l=(g|0)>(l|0)?l:g;}n=-1;o=k|l;if(((o?2147483645:2147483646)|0)<(l|0)){break b}p=(((o|0)!=0)+l|0)+1|0;h=f&-33;n:{if((h|0)==70){if((p^2147483647)<(d|0)){break b}g=(d|0)>0?d:0;break n}g=d>>31;g=Db((g^d)-g|0,0,u);if((u-g|0)<=1){while(1){g=g-1|0;H[g|0]=48;if((u-g|0)<2){continue}break}}t=g-2|0;H[t|0]=f;H[g-1|0]=(d|0)<0?45:43;g=u-t|0;if((g|0)>(p^2147483647)){break b}}d=g+p|0;if((d|0)>(s^2147483647)){break b}p=d+s|0;cb(a,32,c,p,e);bb(a,y,s);cb(a,48,c,p,e^65536);o:{p:{q:{if((h|0)==70){f=m+16|0;d=f|8;k=f|9;h=i>>>0>q>>>0?q:i;i=h;while(1){g=Db(J[i>>2],0,k);r:{if((h|0)!=(i|0)){if(m+16>>>0>=g>>>0){break r}while(1){g=g-1|0;H[g|0]=48;if(m+16>>>0<g>>>0){continue}break}break r}if((g|0)!=(k|0)){break r}H[m+24|0]=48;g=d;}bb(a,g,k-g|0);i=i+4|0;if(q>>>0>=i>>>0){continue}break}if(o){bb(a,7028,1);}if((l|0)<=0|i>>>0>=j>>>0){break q}while(1){g=Db(J[i>>2],0,k);if(g>>>0>m+16>>>0){while(1){g=g-1|0;H[g|0]=48;if(m+16>>>0<g>>>0){continue}break}}bb(a,g,(l|0)>=9?9:l);g=l-9|0;i=i+4|0;if(j>>>0<=i>>>0){break p}d=(l|0)>9;l=g;if(d){continue}break}break p}s:{if((l|0)<0){break s}q=i>>>0<j>>>0?j:i+4|0;f=m+16|0;d=f|8;j=f|9;h=i;while(1){g=Db(J[h>>2],0,j);if((j|0)==(g|0)){H[m+24|0]=48;g=d;}t:{if((h|0)!=(i|0)){if(m+16>>>0>=g>>>0){break t}while(1){g=g-1|0;H[g|0]=48;if(m+16>>>0<g>>>0){continue}break}break t}bb(a,g,1);g=g+1|0;if(!(k|l)){break t}bb(a,7028,1);}f=j-g|0;bb(a,g,(f|0)>(l|0)?l:f);l=l-f|0;h=h+4|0;if(q>>>0<=h>>>0){break s}if((l|0)>=0){continue}break}}cb(a,48,l+18|0,18,0);bb(a,t,u-t|0);break o}g=l;}cb(a,48,g+9|0,9,0);}cb(a,32,c,p,e^8192);n=(c|0)<(p|0)?p:c;break b}k=(f<<26>>31&9)+y|0;u:{if(d>>>0>11){break u}g=12-d|0;r=16;while(1){r=r*16;g=g-1|0;if(g){continue}break}if(K[k|0]==45){b=-(r+(-b-r));break u}b=b+r-r;}g=J[m+44>>2];h=g>>31;g=Db((g^h)-h|0,0,u);if((u|0)==(g|0)){H[m+15|0]=48;g=m+15|0;}q=s|2;i=f&32;h=J[m+44>>2];l=g-2|0;H[l|0]=f+15;H[g-1|0]=(h|0)<0?45:43;g=e&8;h=m+16|0;while(1){f=h;if(R(b)<2147483648){j=~~b;}else {j=-2147483648;}H[h|0]=i|K[j+18464|0];b=(b-+(j|0))*16;h=f+1|0;if(!(!((d|0)>0|g)&b==0|(h-(m+16|0)|0)!=1)){H[f+1|0]=46;h=f+2|0;}if(b!=0){continue}break}n=-1;g=u-l|0;f=g+q|0;if((2147483645-f|0)<(d|0)){break b}j=f;f=m+16|0;i=h-f|0;d=d?(i-2|0)<(d|0)?d+2|0:i:i;h=j+d|0;cb(a,32,c,h,e);bb(a,k,q);cb(a,48,c,h,e^65536);bb(a,f,i);cb(a,48,d-i|0,0,0);bb(a,l,g);cb(a,32,c,h,e^8192);n=(c|0)<(h|0)?h:c;}Fa=m+560|0;return n|0}function Rc(a,b,c,d,e){var f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,v=0,w=0,x=0,y=0;h=Fa-80|0;Fa=h;J[h+76>>2]=b;w=h+55|0;r=h+56|0;a:{b:{c:{d:{e:while(1){i=b;if((o^2147483647)<(f|0)){break d}o=f+o|0;f:{g:{h:{f=i;g=K[f|0];if(g){while(1){i:{b=g&255;j:{if(!b){b=f;break j}if((b|0)!=37){break i}g=f;while(1){if(K[g+1|0]!=37){b=g;break j}f=f+1|0;k=K[g+2|0];b=g+2|0;g=b;if((k|0)==37){continue}break}}f=f-i|0;v=o^2147483647;if((f|0)>(v|0)){break d}if(a){bb(a,i,f);}if(f){continue e}J[h+76>>2]=b;f=b+1|0;p=-1;if(!(K[b+2|0]!=36|H[b+1|0]-48>>>0>=10)){p=H[b+1|0]-48|0;s=1;f=b+3|0;}J[h+76>>2]=f;m=0;g=H[f|0];b=g-32|0;k:{if(b>>>0>31){l=f;break k}l=f;b=1<<b;if(!(b&75913)){break k}while(1){l=f+1|0;J[h+76>>2]=l;m=b|m;g=H[f+1|0];b=g-32|0;if(b>>>0>=32){break k}f=l;b=1<<b;if(b&75913){continue}break}}l:{if((g|0)==42){m:{if(!(K[l+2|0]!=36|H[l+1|0]-48>>>0>=10)){J[((H[l+1|0]<<2)+e|0)-192>>2]=10;g=l+3|0;s=1;b=J[((H[l+1|0]<<3)+d|0)-384>>2];break m}if(s){break h}g=l+1|0;if(!a){J[h+76>>2]=g;s=0;q=0;break l}b=J[c>>2];J[c>>2]=b+4;s=0;b=J[b>>2];}J[h+76>>2]=g;q=b;if((b|0)>=0){break l}q=0-q|0;m=m|8192;break l}q=Qc(h+76|0);if((q|0)<0){break d}g=J[h+76>>2];}f=0;j=-1;n:{if(K[g|0]!=46){b=g;u=0;break n}if(K[g+1|0]==42){o:{if(!(K[g+3|0]!=36|H[g+2|0]-48>>>0>=10)){J[((H[g+2|0]<<2)+e|0)-192>>2]=10;b=g+4|0;j=J[((H[g+2|0]<<3)+d|0)-384>>2];break o}if(s){break h}b=g+2|0;j=0;if(!a){break o}g=J[c>>2];J[c>>2]=g+4;j=J[g>>2];}J[h+76>>2]=b;u=(j^-1)>>>31|0;break n}J[h+76>>2]=g+1;j=Qc(h+76|0);b=J[h+76>>2];u=1;}while(1){n=f;l=28;k=b;f=H[b|0];if(f-123>>>0<4294967238){break c}b=k+1|0;f=K[(f+P(n,58)|0)+17935|0];if(f-1>>>0<8){continue}break}J[h+76>>2]=b;p:{q:{if((f|0)!=27){if(!f){break c}if((p|0)>=0){J[(p<<2)+e>>2]=f;g=(p<<3)+d|0;f=J[g+4>>2];J[h+64>>2]=J[g>>2];J[h+68>>2]=f;break q}if(!a){break f}Pc(h- -64|0,f,c);break p}if((p|0)>=0){break c}}f=0;if(!a){continue e}}g=m&-65537;m=m&8192?g:m;p=0;t=1383;l=r;r:{s:{t:{u:{v:{w:{x:{y:{z:{A:{B:{C:{D:{E:{F:{G:{f=H[k|0];f=n?(f&15)==3?f&-33:f:f;switch(f-88|0){case 11:break r;case 9:case 13:case 14:case 15:break s;case 27:break x;case 12:case 17:break A;case 23:break B;case 0:case 32:break C;case 24:break D;case 22:break E;case 29:break F;case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 10:case 16:case 18:case 19:case 20:case 21:case 25:case 26:case 28:case 30:case 31:break g;default:break G}}H:{switch(f-65|0){case 0:case 4:case 5:case 6:break s;case 2:break v;case 1:case 3:break g;default:break H}}if((f|0)==83){break w}break g}g=J[h+64>>2];k=J[h+68>>2];t=1383;break z}f=0;I:{switch(n&255){case 0:J[J[h+64>>2]>>2]=o;continue e;case 1:J[J[h+64>>2]>>2]=o;continue e;case 2:i=J[h+64>>2];J[i>>2]=o;J[i+4>>2]=o>>31;continue e;case 3:I[J[h+64>>2]>>1]=o;continue e;case 4:H[J[h+64>>2]]=o;continue e;case 6:J[J[h+64>>2]>>2]=o;continue e;case 7:break I;default:continue e}}i=J[h+64>>2];J[i>>2]=o;J[i+4>>2]=o>>31;continue e}j=j>>>0<=8?8:j;m=m|8;f=120;}i=r;g=J[h+64>>2];k=J[h+68>>2];if(g|k){x=f&32;while(1){i=i-1|0;H[i|0]=x|K[(g&15)+18464|0];y=!k&g>>>0>15|(k|0)!=0;n=k;k=k>>>4|0;g=(n&15)<<28|g>>>4;if(y){continue}break}}if(!(J[h+64>>2]|J[h+68>>2])|!(m&8)){break y}t=(f>>>4|0)+1383|0;p=2;break y}f=r;i=J[h+68>>2];k=i;g=J[h+64>>2];if(i|g){while(1){f=f-1|0;H[f|0]=g&7|48;n=!k&g>>>0>7|(k|0)!=0;i=k;k=i>>>3|0;g=(i&7)<<29|g>>>3;if(n){continue}break}}i=f;if(!(m&8)){break y}f=r-i|0;j=(f|0)<(j|0)?j:f+1|0;break y}g=J[h+64>>2];f=J[h+68>>2];k=f;if((f|0)<0){i=0-(k+((g|0)!=0)|0)|0;k=i;g=0-g|0;J[h+64>>2]=g;J[h+68>>2]=i;p=1;t=1383;break z}if(m&2048){p=1;t=1384;break z}p=m&1;t=p?1385:1383;}i=Db(g,k,r);}if((j|0)<0?u:0){break d}m=u?m&-65537:m;f=J[h+64>>2];g=J[h+68>>2];if(!(j|(f|g)!=0)){i=r;j=0;break g}f=!(f|g)+(r-i|0)|0;j=(f|0)<(j|0)?j:f;break g}l=j>>>0>=2147483647?2147483647:j;n=l;m=(l|0)!=0;f=J[h+64>>2];i=f?f:7030;f=i;J:{K:{L:{M:{if(!(f&3)|!l){break M}while(1){if(!K[f|0]){break L}n=n-1|0;m=(n|0)!=0;f=f+1|0;if(!(f&3)){break M}if(n){continue}break}}if(!m){break K}if(!(!K[f|0]|n>>>0<4)){while(1){k=J[f>>2];if((k^-1)&k-16843009&-2139062144){break L}f=f+4|0;n=n-4|0;if(n>>>0>3){continue}break}}if(!n){break K}}while(1){if(!K[f|0]){break J}f=f+1|0;n=n-1|0;if(n){continue}break}}f=0;}f=f?f-i|0:l;l=f+i|0;if((j|0)>=0){m=g;j=f;break g}m=g;j=f;if(K[l|0]){break d}break g}if(j){g=J[h+64>>2];break u}f=0;cb(a,32,q,0,m);break t}J[h+12>>2]=0;J[h+8>>2]=J[h+64>>2];g=h+8|0;J[h+64>>2]=g;j=-1;}f=0;N:{while(1){i=J[g>>2];if(!i){break N}k=Mc(h+4|0,i);i=(k|0)<0;if(!(i|k>>>0>j-f>>>0)){g=g+4|0;f=f+k|0;if(j>>>0>f>>>0){continue}break N}break}if(i){break b}}l=61;if((f|0)<0){break c}cb(a,32,q,f,m);if(!f){f=0;break t}l=0;g=J[h+64>>2];while(1){i=J[g>>2];if(!i){break t}i=Mc(h+4|0,i);l=i+l|0;if(l>>>0>f>>>0){break t}bb(a,h+4|0,i);g=g+4|0;if(f>>>0>l>>>0){continue}break}}cb(a,32,q,f,m^8192);f=(f|0)<(q|0)?q:f;continue e}if((j|0)<0?u:0){break d}l=61;f=Oc(a,O[h+64>>3],q,j,m,f);if((f|0)>=0){continue e}break c}H[h+55|0]=J[h+64>>2];j=1;i=w;m=g;break g}g=K[f+1|0];f=f+1|0;continue}}if(a){break a}if(!s){break f}f=1;while(1){a=J[(f<<2)+e>>2];if(a){Pc((f<<3)+d|0,a,c);o=1;f=f+1|0;if((f|0)!=10){continue}break a}break}o=1;if(f>>>0>=10){break a}while(1){if(J[(f<<2)+e>>2]){break h}f=f+1|0;if((f|0)!=10){continue}break}break a}l=28;break c}k=l-i|0;g=(j|0)>(k|0)?j:k;if((g|0)>(p^2147483647)){break d}l=61;j=g+p|0;f=(j|0)<(q|0)?q:j;if((v|0)<(f|0)){break c}cb(a,32,f,j,m);bb(a,t,p);cb(a,48,f,j,m^65536);cb(a,48,g,k,0);bb(a,i,k);cb(a,32,f,j,m^8192);continue}break}o=0;break a}l=61;}J[6386]=l;}o=-1;}Fa=h+80|0;return o}function Ub(a){var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0;J[a+52>>2]=0;b=J[a+40>>2];a:{if((b|0)<=0){break a}while(1){e=J[J[a+32>>2]+(i<<2)>>2];J[a+56>>2]=e;if((e|0)!=-1){h=J[a+4>>2]+P(e,40)|0;d=Fa-1040|0;Fa=d;J[d+1036>>2]=256;e=d+8|0;J[d+4>>2]=e;J[d+8>>2]=J[a>>2];b=e;c=1;while(1){b:{c=c-1|0;J[d+1032>>2]=c;g=J[(c<<2)+b>>2];c:{if((g|0)==-1){break c}f=J[a+4>>2]+P(g,40)|0;if(Q(N[h>>2]-N[f+8>>2])>Q(0)|Q(N[h+4>>2]-N[f+12>>2])>Q(0)|(Q(N[f>>2]-N[h+8>>2])>Q(0)|Q(N[f+4>>2]-N[h+12>>2])>Q(0))){break c}if(J[f+24>>2]==-1){c=J[a+56>>2];if(!((g|0)==(c|0)|(K[(J[a+4>>2]+P(g,40)|0)+36|0]?(c|0)<(g|0):0))){b=J[a+52>>2];if((b|0)==J[a+48>>2]){c=(b>>1)+b|0;J[a+48>>2]=c;b=J[a+44>>2];c=_a(c<<3);J[a+44>>2]=c;eb(c,b,J[a+52>>2]<<3);Wa(b);c=J[a+56>>2];b=J[a+52>>2];}f=J[a+44>>2]+(b<<3)|0;J[f+4>>2]=(c|0)<(g|0)?g:c;J[f>>2]=(c|0)>(g|0)?g:c;J[a+52>>2]=b+1;}b=J[d+4>>2];c=J[d+1032>>2];if((c|0)>0){continue}break b}d:{if(J[d+1036>>2]!=(c|0)){break d}J[d+1036>>2]=c<<1;c=_a(c<<3);J[d+4>>2]=c;eb(c,b,J[d+1032>>2]<<2);if((b|0)==(e|0)){break d}Wa(b);}b=J[d+4>>2];J[b+(J[d+1032>>2]<<2)>>2]=J[f+24>>2];c=J[d+1032>>2]+1|0;J[d+1032>>2]=c;e:{if((c|0)!=J[d+1036>>2]){break e}J[d+1036>>2]=c<<1;c=_a(c<<3);J[d+4>>2]=c;eb(c,b,J[d+1032>>2]<<2);if((b|0)==(e|0)){break e}Wa(b);}b=J[d+4>>2];J[b+(J[d+1032>>2]<<2)>>2]=J[f+28>>2];c=J[d+1032>>2]+1|0;J[d+1032>>2]=c;}if((c|0)>0){continue}}break}if((b|0)!=(e|0)){Wa(b);}Fa=d+1040|0;b=J[a+40>>2];}i=i+1|0;if((i|0)<(b|0)){continue}break}if(J[a+52>>2]>0){i=0;while(1){d=J[a+4>>2];e=J[a+44>>2]+(i<<3)|0;c=J[(d+P(J[e>>2],40)|0)+16>>2];b=J[c+16>>2];h=J[b+8>>2];d=J[(d+P(J[e+4>>2],40)|0)+16>>2];e=J[d+16>>2];j=J[e+8>>2];f:{if((h|0)==(j|0)){break f}d=J[d+20>>2];c=J[c+20>>2];g=J[j+112>>2];if(g){while(1){g:{if((h|0)!=J[g>>2]){break g}f=J[g+4>>2];k=J[f+60>>2];l=J[f+56>>2];m=J[f+52>>2];f=J[f+48>>2];if(!((f|0)!=(b|0)|(e|0)!=(m|0)|(c|0)!=(l|0))){if((d|0)==(k|0)){break f}}if((e|0)!=(f|0)|(b|0)!=(m|0)|(d|0)!=(l|0)){break g}if((c|0)==(k|0)){break f}}g=J[g+12>>2];if(g){continue}break}}if(!vd(j,h)){break f}g=J[a+68>>2];if(g){if(!(Ha[J[J[g>>2]+8>>2]](g,b,e)|0)){break f}}g=J[a+76>>2];if(!K[24464]){J[6092]=733;H[24280]=1;J[6069]=734;J[6068]=735;J[6074]=733;J[6080]=736;J[6104]=737;J[6071]=736;H[24376]=1;J[6093]=738;H[24400]=1;J[6099]=739;J[6098]=740;H[24304]=0;J[6075]=738;H[24328]=1;J[6081]=741;H[24424]=1;J[6105]=742;H[24388]=0;J[6096]=743;J[6095]=744;H[24352]=1;J[6087]=743;J[6086]=744;H[24292]=0;J[6072]=741;H[24448]=1;J[6111]=745;J[6110]=746;H[24316]=0;J[6078]=742;J[6077]=737;J[6101]=746;J[6102]=745;H[24412]=0;H[24464]=1;}h=(P(J[J[b+12>>2]+4>>2],48)+24272|0)+P(J[J[e+12>>2]+4>>2],12)|0;f=J[h>>2];h:{if(f){if(K[h+8|0]){b=Ha[f|0](b,c,e,d,g)|0;break h}b=Ha[f|0](e,d,b,c,g)|0;}else {b=0;}}if(!b){break f}f=J[b+52>>2];e=J[f+8>>2];h=J[b+48>>2];d=J[h+8>>2];J[b+8>>2]=0;c=J[a+60>>2];J[b+12>>2]=c;if(c){J[c+8>>2]=b;}J[a+60>>2]=b;J[b+24>>2]=0;J[b+16>>2]=e;J[b+20>>2]=b;c=J[d+112>>2];J[b+28>>2]=c;g=b+16|0;if(c){J[c+8>>2]=g;}J[d+112>>2]=g;J[b+40>>2]=0;J[b+32>>2]=d;J[b+36>>2]=b;c=J[e+112>>2];J[b+44>>2]=c;b=b+32|0;if(c){J[c+8>>2]=b;}J[e+112>>2]=b;if(!(K[h+38|0]|K[f+38|0])){J[d+144>>2]=0;I[d+4>>1]=L[d+4>>1]|2;J[e+144>>2]=0;I[e+4>>1]=L[e+4>>1]|2;}J[a+64>>2]=J[a+64>>2]+1;}i=i+1|0;if((i|0)<J[a+52>>2]){continue}break}b=J[a+40>>2];}if((b|0)<=0){break a}c=b&1;e=J[a+4>>2];d=J[a+32>>2];i=0;if((b|0)!=1){g=b&-2;b=0;while(1){f=i<<2;h=J[f+d>>2];if((h|0)!=-1){H[(e+P(h,40)|0)+36|0]=0;}f=J[d+(f|4)>>2];if((f|0)!=-1){H[(e+P(f,40)|0)+36|0]=0;}i=i+2|0;b=b+2|0;if((g|0)!=(b|0)){continue}break}}if(!c){break a}b=J[d+(i<<2)>>2];if((b|0)==-1){break a}H[(e+P(b,40)|0)+36|0]=0;}J[a+40>>2]=0;}function Uc(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0,s=0,t=0,u=0,w=0,y=0,z=0,A=0;o=Fa-16|0;Fa=o;s=(C(a),v(2));e=s&2147483647;a:{if(e>>>0<=1305022426){j=+a;d=j*.6366197723675814+6755399441055744+-6755399441055744;r=j+d*-1.5707963109016418+d*-1.5893254773528196e-8;O[b>>3]=r;c=r<-.7853981852531433;if(R(d)<2147483648){e=~~d;}else {e=-2147483648;}if(c){d=d+-1;O[b>>3]=j+d*-1.5707963109016418+d*-1.5893254773528196e-8;e=e-1|0;break a}if(!(r>.7853981852531433)){break a}d=d+1;O[b>>3]=j+d*-1.5707963109016418+d*-1.5893254773528196e-8;e=e+1|0;break a}if(e>>>0>=2139095040){O[b>>3]=Q(a-a);e=0;break a}c=e;e=(e>>>23|0)-150|0;O[o+8>>3]=(x(2,c-(e<<23)|0),D());t=o+8|0;g=Fa-560|0;Fa=g;c=(e-3|0)/24|0;p=(c|0)>0?c:0;i=e+P(p,-24)|0;l=J[3788];if((l|0)>=0){e=l+1|0;c=p;while(1){O[(g+320|0)+(f<<3)>>3]=(c|0)<0?0:+J[(c<<2)+15168>>2];c=c+1|0;f=f+1|0;if((e|0)!=(f|0)){continue}break}}k=i-24|0;e=0;f=(l|0)>0?l:0;while(1){c=0;d=0;while(1){d=O[(c<<3)+t>>3]*O[(g+320|0)+(e-c<<3)>>3]+d;c=c+1|0;if((c|0)!=1){continue}break}O[(e<<3)+g>>3]=d;c=(e|0)==(f|0);e=e+1|0;if(!c){continue}break}z=47-i|0;u=48-i|0;A=i-25|0;e=l;b:{while(1){d=O[(e<<3)+g>>3];c=0;f=e;n=(e|0)<=0;if(!n){while(1){m=(g+480|0)+(c<<2)|0;j=d*5.960464477539063e-8;c:{if(R(j)<2147483648){h=~~j;break c}h=-2147483648;}j=+(h|0);d=j*-16777216+d;d:{if(R(d)<2147483648){h=~~d;break d}h=-2147483648;}J[m>>2]=h;f=f-1|0;d=O[(f<<3)+g>>3]+j;c=c+1|0;if((e|0)!=(c|0)){continue}break}}d=Rb(d,k);d=d+V(d*.125)*-8;e:{if(R(d)<2147483648){m=~~d;break e}m=-2147483648;}d=d-+(m|0);f:{g:{h:{w=(k|0)<=0;i:{if(!w){f=(e<<2)+g|0;h=J[f+476>>2];c=h>>u;q=f;f=h-(c<<u)|0;J[q+476>>2]=f;m=c+m|0;h=f>>z;break i}if(k){break h}h=J[((e<<2)+g|0)+476>>2]>>23;}if((h|0)<=0){break f}break g}h=2;if(d>=.5){break g}h=0;break f}c=0;f=0;if(!n){while(1){q=(g+480|0)+(c<<2)|0;n=J[q>>2];y=16777215;j:{k:{if(f){break k}y=16777216;if(n){break k}f=0;break j}J[q>>2]=y-n;f=1;}c=c+1|0;if((e|0)!=(c|0)){continue}break}}l:{if(w){break l}c=8388607;m:{switch(A|0){case 1:c=4194303;break;case 0:break m;default:break l}}n=(e<<2)+g|0;J[n+476>>2]=J[n+476>>2]&c;}m=m+1|0;if((h|0)!=2){break f}d=1-d;h=2;if(!f){break f}d=d-Rb(1,k);}if(d==0){f=0;n:{c=e;if((l|0)>=(e|0)){break n}while(1){c=c-1|0;f=J[(g+480|0)+(c<<2)>>2]|f;if((c|0)>(l|0)){continue}break}if(!f){break n}i=k;while(1){i=i-24|0;e=e-1|0;if(!J[(g+480|0)+(e<<2)>>2]){continue}break}break b}c=1;while(1){f=c;c=c+1|0;if(!J[(g+480|0)+(l-f<<2)>>2]){continue}break}f=e+f|0;while(1){e=e+1|0;O[(g+320|0)+(e<<3)>>3]=J[(e+p<<2)+15168>>2];c=0;d=0;while(1){d=O[(c<<3)+t>>3]*O[(g+320|0)+(e-c<<3)>>3]+d;c=c+1|0;if((c|0)!=1){continue}break}O[(e<<3)+g>>3]=d;if((e|0)<(f|0)){continue}break}e=f;continue}break}d=Rb(d,24-i|0);o:{if(d>=16777216){k=(g+480|0)+(e<<2)|0;j=d*5.960464477539063e-8;p:{if(R(j)<2147483648){c=~~j;break p}c=-2147483648;}d=+(c|0)*-16777216+d;q:{if(R(d)<2147483648){f=~~d;break q}f=-2147483648;}J[k>>2]=f;e=e+1|0;break o}if(R(d)<2147483648){c=~~d;}else {c=-2147483648;}i=k;}J[(g+480|0)+(e<<2)>>2]=c;}d=Rb(1,i);r:{if((e|0)<0){break r}c=e;while(1){f=c;O[(c<<3)+g>>3]=d*+J[(g+480|0)+(c<<2)>>2];c=c-1|0;d=d*5.960464477539063e-8;if(f){continue}break}if((e|0)<0){break r}f=e;while(1){d=0;c=0;i=e-f|0;k=(i|0)>(l|0)?l:i;if((k|0)>=0){while(1){d=O[(c<<3)+17936>>3]*O[(c+f<<3)+g>>3]+d;p=(c|0)!=(k|0);c=c+1|0;if(p){continue}break}}O[(g+160|0)+(i<<3)>>3]=d;c=(f|0)>0;f=f-1|0;if(c){continue}break}}d=0;if((e|0)>=0){while(1){c=e;e=e-1|0;d=d+O[(g+160|0)+(c<<3)>>3];if(c){continue}break}}O[o>>3]=h?-d:d;Fa=g+560|0;e=m&7;d=O[o>>3];if((s|0)<0){O[b>>3]=-d;e=0-e|0;break a}O[b>>3]=d;}Fa=o+16|0;return e}function Yc(a,b,c,d){var e=0,f=0,g=Q(0),h=0,i=Q(0),j=Q(0),k=Q(0),l=0,m=Q(0),n=Q(0),o=0,p=Q(0),q=Q(0),r=0,s=Q(0),t=0,u=Q(0),v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0),B=0,C=0,D=0;g=N[a+48>>2];s=Q(Q(1)-d);n=Q(Q(s*N[a+68>>2])+Q(N[a+72>>2]*d));j=Ua(n);m=N[a+44>>2];n=Ta(n);x=Q(Q(Q(s*N[a+56>>2])+Q(N[a- -64>>2]*d))-Q(Q(n*m)+Q(g*j)));u=Q(Q(Q(s*N[a+52>>2])+Q(N[a+60>>2]*d))-Q(Q(j*m)-Q(g*n)));q=N[a+12>>2];m=Q(Q(s*N[a+32>>2])+Q(N[a+36>>2]*d));g=Ua(m);k=N[a+8>>2];m=Ta(m);y=Q(Q(Q(s*N[a+20>>2])+Q(N[a+28>>2]*d))-Q(Q(m*k)+Q(q*g)));v=Q(Q(Q(s*N[a+16>>2])+Q(N[a+24>>2]*d))-Q(Q(g*k)-Q(q*m)));w=Q(-n);s=Q(-m);a:{switch(J[a+80>>2]){case 0:q=N[a+92>>2];k=N[a+96>>2];e=J[a>>2];o=J[e+16>>2];h=J[e+20>>2];b:{if((h|0)<2){break b}i=Q(Q(g*q)+Q(m*k));p=Q(Q(s*q)+Q(g*k));d=Q(Q(N[o>>2]*i)+Q(p*N[o+4>>2]));e=1;f=h-1|0;C=f&1;c:{if((h|0)==2){f=0;break c}B=f&-2;f=0;h=0;while(1){r=o+(e<<3)|0;z=Q(Q(N[r>>2]*i)+Q(p*N[r+4>>2]));r=z>d;D=e+1|0;t=o+(D<<3)|0;A=Q(Q(N[t>>2]*i)+Q(p*N[t+4>>2]));d=r?z:d;t=A>d;d=t?A:d;f=t?D:r?e:f;e=e+2|0;h=h+2|0;if((B|0)!=(h|0)){continue}break}}if(!C){break b}h=e;e=o+(e<<3)|0;f=Q(Q(N[e>>2]*i)+Q(p*N[e+4>>2]))>d?h:f;}J[b>>2]=f;a=J[a+4>>2];e=J[a+16>>2];f=J[a+20>>2];d:{if((f|0)<2){break d}i=Q(Q(j*Q(-q))-Q(n*k));p=Q(Q(n*q)-Q(j*k));d=Q(Q(N[e>>2]*i)+Q(p*N[e+4>>2]));a=1;l=f-1|0;t=l&1;e:{if((f|0)==2){l=0;break e}C=l&-2;l=0;f=0;while(1){h=e+(a<<3)|0;z=Q(Q(N[h>>2]*i)+Q(p*N[h+4>>2]));h=z>d;B=a+1|0;r=e+(B<<3)|0;A=Q(Q(N[r>>2]*i)+Q(p*N[r+4>>2]));d=h?z:d;r=A>d;d=r?A:d;l=r?B:h?a:l;a=a+2|0;f=f+2|0;if((C|0)!=(f|0)){continue}break}}if(!t){break d}h=a;a=e+(a<<3)|0;l=Q(Q(N[a>>2]*i)+Q(p*N[a+4>>2]))>d?h:l;}J[c>>2]=l;i=u;a=e+(l<<3)|0;d=N[a>>2];u=N[a+4>>2];p=Q(i+Q(Q(j*d)+Q(u*w)));i=v;a=o+(J[b>>2]<<3)|0;w=N[a>>2];v=N[a+4>>2];return Q(Q(Q(p-Q(i+Q(Q(g*w)+Q(v*s))))*q)+Q(k*Q(Q(x+Q(Q(n*d)+Q(j*u)))-Q(y+Q(Q(m*w)+Q(g*v))))));case 1:d=N[a+88>>2];k=N[a+96>>2];i=N[a+84>>2];p=N[a+92>>2];J[b>>2]=-1;q=Q(Q(m*p)+Q(g*k));k=Q(Q(g*p)+Q(k*s));y=Q(y+Q(Q(m*i)+Q(g*d)));s=Q(v+Q(Q(g*i)+Q(d*s)));a=J[a+4>>2];b=J[a+16>>2];f=J[a+20>>2];f:{if((f|0)<2){break f}g=Q(Q(j*Q(-k))-Q(n*q));m=Q(Q(n*k)-Q(j*q));d=Q(Q(N[b>>2]*g)+Q(m*N[b+4>>2]));a=1;e=f-1|0;h=e&1;g:{if((f|0)==2){e=0;break g}r=e&-2;e=0;f=0;while(1){l=b+(a<<3)|0;v=Q(Q(N[l>>2]*g)+Q(m*N[l+4>>2]));l=v>d;t=a+1|0;o=b+(t<<3)|0;i=Q(Q(N[o>>2]*g)+Q(m*N[o+4>>2]));d=l?v:d;o=i>d;d=o?i:d;e=o?t:l?a:e;a=a+2|0;f=f+2|0;if((r|0)!=(f|0)){continue}break}}if(!h){break f}h=a;a=b+(a<<3)|0;e=Q(Q(N[a>>2]*g)+Q(m*N[a+4>>2]))>d?h:e;}J[c>>2]=e;a=b+(e<<3)|0;d=N[a>>2];g=N[a+4>>2];return Q(Q(Q(Q(u+Q(Q(j*d)+Q(g*w)))-s)*k)+Q(q*Q(Q(x+Q(Q(n*d)+Q(j*g)))-y)));case 2:d=N[a+88>>2];k=N[a+96>>2];i=N[a+84>>2];p=N[a+92>>2];J[c>>2]=-1;q=Q(Q(n*p)+Q(j*k));k=Q(Q(j*p)+Q(k*w));x=Q(x+Q(Q(n*i)+Q(j*d)));w=Q(u+Q(Q(j*i)+Q(d*w)));a=J[a>>2];c=J[a+16>>2];f=J[a+20>>2];h:{if((f|0)<2){break h}j=Q(Q(g*Q(-k))-Q(m*q));n=Q(Q(m*k)-Q(g*q));d=Q(Q(N[c>>2]*j)+Q(n*N[c+4>>2]));a=1;e=f-1|0;h=e&1;i:{if((f|0)==2){e=0;break i}r=e&-2;e=0;f=0;while(1){l=c+(a<<3)|0;u=Q(Q(N[l>>2]*j)+Q(n*N[l+4>>2]));l=u>d;t=a+1|0;o=c+(t<<3)|0;i=Q(Q(N[o>>2]*j)+Q(n*N[o+4>>2]));d=l?u:d;o=i>d;d=o?i:d;e=o?t:l?a:e;a=a+2|0;f=f+2|0;if((r|0)!=(f|0)){continue}break}}if(!h){break h}h=a;a=c+(a<<3)|0;e=Q(Q(N[a>>2]*j)+Q(n*N[a+4>>2]))>d?h:e;}J[b>>2]=e;a=c+(e<<3)|0;d=N[a>>2];j=N[a+4>>2];return Q(Q(Q(Q(v+Q(Q(g*d)+Q(j*s)))-w)*k)+Q(q*Q(Q(y+Q(Q(m*d)+Q(g*j)))-x)));default:break a}}J[b>>2]=-1;J[c>>2]=-1;return Q(0)}function ad(a){var b=Q(0),c=0,d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=0,k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=0,r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0),B=Q(0),C=0,D=Q(0),E=0,F=0,G=Q(0),H=0,I=0,L=0,M=0,O=Q(0),R=Q(0);if(J[a+48>>2]>0){q=J[a+28>>2];I=K[23352];while(1){c=J[a+40>>2]+P(F,156)|0;G=N[c+132>>2];A=N[c+124>>2];g=N[c+128>>2];B=N[c+120>>2];l=N[c+72>>2];i=N[c+76>>2];H=P(J[c+116>>2],12);j=H+q|0;o=N[j+8>>2];r=N[j+4>>2];s=N[j>>2];L=P(J[c+112>>2],12);C=L+q|0;b=N[C+8>>2];t=N[C+4>>2];u=N[C>>2];E=J[c+148>>2];M=(E|0)<=0;a:{b:{if(!M){v=N[c+136>>2];d=Q(-l);k=N[c+144>>2];h=Q(-g);q=0;while(1){j=c+P(q,36)|0;e=Q(v*N[j+16>>2]);f=Q(-e);w=N[j+20>>2];n=N[j+12>>2];m=N[j+4>>2];x=N[j+8>>2];y=N[j>>2];z=Q(w-Q(N[j+28>>2]*Q(Q(Q(Q(Q(Q(s-Q(o*n))-u)+Q(b*m))*i)+Q(Q(Q(Q(r+Q(o*x))-t)-Q(b*y))*d))-k)));e=e>z?z:e;e=e<f?f:e;N[j+20>>2]=e;f=Q(e-w);e=Q(f*d);f=Q(i*f);o=Q(Q(G*Q(Q(x*e)-Q(n*f)))+o);b=Q(Q(h*Q(Q(y*e)-Q(f*m)))+b);r=Q(r+Q(A*e));s=Q(s+Q(A*f));t=Q(t-Q(B*e));u=Q(u-Q(B*f));q=q+1|0;if((E|0)!=(q|0)){continue}break}if(I?(E|0)!=1:0){break b}if(M){break a}q=0;while(1){j=c+P(q,36)|0;e=N[j+16>>2];f=N[j+12>>2];g=N[j+4>>2];d=N[j+8>>2];v=N[j>>2];k=Q(e-Q(N[j+24>>2]*Q(Q(Q(Q(Q(Q(s-Q(o*f))-u)+Q(b*g))*l)+Q(i*Q(Q(Q(r+Q(o*d))-t)-Q(b*v))))-N[j+32>>2])));k=k>Q(0)?k:Q(0);N[j+16>>2]=k;m=d;d=Q(k-e);e=Q(i*d);p=f;f=Q(l*d);o=Q(Q(G*Q(Q(m*e)-Q(p*f)))+o);b=Q(Q(h*Q(Q(v*e)-Q(f*g)))+b);r=Q(r+Q(A*e));s=Q(s+Q(A*f));t=Q(t-Q(B*e));u=Q(u-Q(B*f));q=q+1|0;if((E|0)!=(q|0)){continue}break}break a}if(!I){break a}}c:{d:{v=N[c+12>>2];k=N[c+4>>2];w=N[c+8>>2];n=N[c>>2];d=N[c+16>>2];h=N[c+52>>2];O=N[c+104>>2];m=Q(Q(Q(Q(Q(Q(Q(s-Q(o*v))-u)+Q(b*k))*l)+Q(i*Q(Q(Q(r+Q(o*w))-t)-Q(b*n))))-N[c+32>>2])-Q(Q(N[c+96>>2]*d)+Q(h*O)));x=N[c+48>>2];y=N[c+40>>2];z=N[c+44>>2];D=N[c+36>>2];f=N[c+100>>2];p=Q(Q(Q(Q(Q(Q(Q(s-Q(o*x))-u)+Q(b*y))*l)+Q(i*Q(Q(Q(r+Q(o*z))-t)-Q(b*D))))-N[c+68>>2])-Q(Q(f*d)+Q(h*N[c+108>>2])));e=Q(Q(N[c+80>>2]*m)+Q(N[c+88>>2]*p));if(!(e<=Q(0))){break d}R=Q(Q(N[c+84>>2]*m)+Q(p*N[c+92>>2]));if(!(R<=Q(0))){break d}m=b;p=g;e=Q(-e);b=Q(e-d);g=Q(i*b);d=Q(l*b);n=Q(Q(n*g)-Q(d*k));f=Q(-R);b=Q(f-h);h=Q(i*b);k=Q(l*b);b=Q(m-Q(p*Q(n+Q(Q(D*h)-Q(k*y)))));i=Q(g+h);l=Q(d+k);g=Q(Q(w*g)-Q(d*v));d=Q(Q(z*h)-Q(k*x));break c}e=Q(m*Q(-N[c+24>>2]));if(!(!(e>=Q(0))|!(Q(Q(f*e)+p)>=Q(0)))){f=Q(0);m=b;p=g;b=Q(Q(0)-h);g=Q(i*b);h=Q(l*b);b=Q(e-d);d=Q(i*b);i=Q(n*d);n=Q(l*b);b=Q(m-Q(p*Q(Q(Q(D*g)-Q(h*y))+Q(i-Q(n*k)))));i=Q(g+d);l=Q(h+n);g=Q(Q(z*g)-Q(h*x));d=Q(Q(w*d)-Q(n*v));break c}f=Q(p*Q(-N[c+60>>2]));if(!(!(f>=Q(0))|!(Q(Q(O*f)+m)>=Q(0)))){e=Q(0);m=b;p=g;b=Q(Q(0)-d);g=Q(i*b);d=Q(l*b);n=Q(Q(n*g)-Q(d*k));b=Q(f-h);h=Q(i*b);k=Q(l*b);b=Q(m-Q(p*Q(n+Q(Q(D*h)-Q(k*y)))));i=Q(g+h);l=Q(d+k);g=Q(Q(w*g)-Q(d*v));d=Q(Q(z*h)-Q(k*x));break c}if(!(m>=Q(0))|!(p>=Q(0))){break a}e=Q(0);m=b;p=g;b=Q(Q(0)-d);f=Q(i*b);g=Q(l*b);b=Q(Q(0)-h);d=Q(i*b);h=Q(l*b);b=Q(m-Q(p*Q(Q(Q(n*f)-Q(g*k))+Q(Q(D*d)-Q(h*y)))));i=Q(f+d);l=Q(g+h);g=Q(Q(w*f)-Q(g*v));f=Q(0);d=Q(Q(z*d)-Q(h*x));}N[c+52>>2]=f;N[c+16>>2]=e;s=Q(s+Q(A*l));r=Q(r+Q(A*i));u=Q(u-Q(B*l));t=Q(t-Q(B*i));o=Q(Q(G*Q(g+d))+o);}N[C+4>>2]=t;N[C>>2]=u;c=J[a+28>>2];N[(c+L|0)+8>>2]=b;c=c+H|0;N[c+4>>2]=r;N[c>>2]=s;q=J[a+28>>2];N[(H+q|0)+8>>2]=o;F=F+1|0;if((F|0)<J[a+48>>2]){continue}break}}}function yh(a,b){a=a|0;b=b|0;var c=0,d=Q(0),e=0,f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=0,l=0,m=0,n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=0,s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0),B=Q(0),C=0,D=Q(0),E=Q(0),F=Q(0),G=Q(0),H=Q(0),I=Q(0),L=Q(0),M=Q(0),O=Q(0),R=Q(0),S=Q(0),T=Q(0),U=Q(0),V=Q(0),W=Q(0),X=Q(0),Y=Q(0),Z=0,_=0,$=Q(0),aa=Q(0);l=J[a+48>>2];C=J[l+8>>2];J[a+160>>2]=C;m=J[a+52>>2];r=J[m+8>>2];J[a+164>>2]=r;k=J[a+84>>2];Z=J[k+8>>2];J[a+168>>2]=Z;c=J[a+88>>2];_=J[c+8>>2];J[a+172>>2]=_;D=N[l+32>>2];e=J[l+32>>2];i=N[l+28>>2];J[a+176>>2]=J[l+28>>2];J[a+180>>2]=e;e=J[m+32>>2];J[a+184>>2]=J[m+28>>2];J[a+188>>2]=e;w=N[k+32>>2];e=J[k+32>>2];n=N[k+28>>2];J[a+192>>2]=J[k+28>>2];J[a+196>>2]=e;$=N[c+32>>2];e=J[c+32>>2];aa=N[c+28>>2];J[a+200>>2]=J[c+28>>2];J[a+204>>2]=e;E=N[l+120>>2];N[a+208>>2]=E;F=N[m+120>>2];N[a+212>>2]=F;G=N[k+120>>2];N[a+216>>2]=G;H=N[c+120>>2];N[a+220>>2]=H;x=N[l+128>>2];N[a+224>>2]=x;y=N[m+128>>2];N[a+228>>2]=y;z=N[k+128>>2];N[a+232>>2]=z;A=N[c+128>>2];N[a+236>>2]=A;l=J[a+76>>2];c=J[b+28>>2];k=P(_,12);e=c+k|0;I=N[e+8>>2];L=N[e+4>>2];M=N[e>>2];m=P(Z,12);e=m+c|0;O=N[e+8>>2];R=N[e+4>>2];S=N[e>>2];r=P(r,12);e=r+c|0;T=N[e+8>>2];U=N[e+4>>2];V=N[e>>2];e=P(C,12);c=e+c|0;W=N[c+8>>2];X=N[c+4>>2];Y=N[c>>2];c=J[b+24>>2];d=N[(c+r|0)+8>>2];f=N[(c+k|0)+8>>2];h=Ua(f);g=Ta(f);s=Ua(d);t=Ta(d);a:{if((l|0)==1){o=Q(1);j=Q(1);d=Q(x+z);break a}p=N[a+96>>2];d=N[(c+e|0)+8>>2];o=Ua(d);B=N[a+92>>2];j=Ta(d);q=N[a+128>>2];i=Q(B-i);p=Q(p-D);f=N[(c+m|0)+8>>2];d=Ta(f);u=N[a+124>>2];f=Ua(f);v=Q(Q(d*u)+Q(q*f));q=Q(Q(f*u)-Q(q*d));o=Q(Q(Q(Q(o*i)-Q(j*p))*v)-Q(q*Q(Q(j*i)+Q(o*p))));j=Q(N[a+108>>2]-n);i=Q(N[a+112>>2]-w);j=Q(Q(Q(Q(f*j)-Q(d*i))*v)-Q(q*Q(Q(d*j)+Q(f*i))));d=Q(Q(Q(x*o)*o)+Q(Q(Q(z*j)*j)+Q(E+G)));}N[a+264>>2]=j;N[a+256>>2]=o;N[a+240>>2]=q;N[a+244>>2]=v;i=Q(0);n=Q(d+Q(0));b:{if(J[a+80>>2]==1){J[a+248>>2]=0;d=N[a+152>>2];g=Q(Q(Q(d*d)*Q(y+A))+n);h=d;f=Q(0);break b}d=N[a+132>>2];B=N[a+136>>2];p=Q(Q(h*d)-Q(B*g));f=N[a+152>>2];i=Q(p*f);N[a+248>>2]=i;D=n;u=Q(N[a+100>>2]-N[a+184>>2]);w=Q(N[a+104>>2]-N[a+188>>2]);n=Q(Q(g*d)+Q(h*B));d=Q(f*Q(Q(Q(Q(s*u)-Q(w*t))*n)-Q(p*Q(Q(t*u)+Q(s*w)))));s=Q(N[a+116>>2]-aa);t=Q(N[a+120>>2]-$);h=Q(f*Q(Q(Q(Q(h*s)-Q(t*g))*n)-Q(p*Q(Q(g*s)+Q(h*t)))));g=Q(D+Q(Q(Q(y*d)*d)+Q(Q(Q(f*f)*Q(F+H))+Q(h*Q(A*h)))));f=Q(n*f);}N[a+268>>2]=h;N[a+260>>2]=d;N[a+252>>2]=f;N[a+272>>2]=g>Q(0)?Q(Q(1)/g):Q(0);c:{if(K[b+20|0]){g=N[a+156>>2];I=Q(I-Q(Q(g*A)*h));h=Q(H*g);L=Q(L-Q(h*f));M=Q(M-Q(h*i));O=Q(O-Q(Q(g*z)*j));h=Q(G*g);R=Q(R-Q(h*v));S=Q(S-Q(q*h));T=Q(Q(Q(y*g)*d)+T);d=Q(F*g);U=Q(U+Q(d*f));V=Q(V+Q(d*i));W=Q(Q(Q(x*g)*o)+W);d=Q(E*g);X=Q(X+Q(d*v));Y=Q(Y+Q(q*d));break c}J[a+156>>2]=0;}c=J[b+28>>2]+P(C,12)|0;N[c+4>>2]=X;N[c>>2]=Y;c=J[b+28>>2];N[(c+P(J[a+160>>2],12)|0)+8>>2]=W;c=c+P(J[a+164>>2],12)|0;N[c+4>>2]=U;N[c>>2]=V;c=J[b+28>>2];N[(c+P(J[a+164>>2],12)|0)+8>>2]=T;c=c+P(J[a+168>>2],12)|0;N[c+4>>2]=R;N[c>>2]=S;c=J[b+28>>2];N[(c+P(J[a+168>>2],12)|0)+8>>2]=O;c=c+P(J[a+172>>2],12)|0;N[c+4>>2]=L;N[c>>2]=M;N[(J[b+28>>2]+P(J[a+172>>2],12)|0)+8>>2]=I;}function Wa(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0;a:{if(!a){break a}d=a-8|0;b=J[a-4>>2];a=b&-8;f=d+a|0;b:{if(b&1){break b}if(!(b&3)){break a}b=J[d>>2];d=d-b|0;if(d>>>0<M[6438]){break a}a=a+b|0;c:{d:{if(J[6439]!=(d|0)){if(b>>>0<=255){e=b>>>3|0;b=J[d+12>>2];c=J[d+8>>2];if((b|0)==(c|0)){i=25736,j=J[6434]&ll(e),J[i>>2]=j;break b}J[c+12>>2]=b;J[b+8>>2]=c;break b}g=J[d+24>>2];b=J[d+12>>2];if((d|0)!=(b|0)){c=J[d+8>>2];J[c+12>>2]=b;J[b+8>>2]=c;break c}e=d+20|0;c=J[e>>2];if(!c){c=J[d+16>>2];if(!c){break d}e=d+16|0;}while(1){h=e;b=c;e=b+20|0;c=J[e>>2];if(c){continue}e=b+16|0;c=J[b+16>>2];if(c){continue}break}J[h>>2]=0;break c}b=J[f+4>>2];if((b&3)!=3){break b}J[6436]=a;J[f+4>>2]=b&-2;J[d+4>>2]=a|1;J[f>>2]=a;return}b=0;}if(!g){break b}c=J[d+28>>2];e=(c<<2)+26040|0;e:{if(J[e>>2]==(d|0)){J[e>>2]=b;if(b){break e}i=25740,j=J[6435]&ll(c),J[i>>2]=j;break b}J[g+(J[g+16>>2]==(d|0)?16:20)>>2]=b;if(!b){break b}}J[b+24>>2]=g;c=J[d+16>>2];if(c){J[b+16>>2]=c;J[c+24>>2]=b;}c=J[d+20>>2];if(!c){break b}J[b+20>>2]=c;J[c+24>>2]=b;}if(d>>>0>=f>>>0){break a}b=J[f+4>>2];if(!(b&1)){break a}f:{g:{h:{i:{if(!(b&2)){if(J[6440]==(f|0)){J[6440]=d;a=J[6437]+a|0;J[6437]=a;J[d+4>>2]=a|1;if(J[6439]!=(d|0)){break a}J[6436]=0;J[6439]=0;return}if(J[6439]==(f|0)){J[6439]=d;a=J[6436]+a|0;J[6436]=a;J[d+4>>2]=a|1;J[a+d>>2]=a;return}a=(b&-8)+a|0;if(b>>>0<=255){e=b>>>3|0;b=J[f+12>>2];c=J[f+8>>2];if((b|0)==(c|0)){i=25736,j=J[6434]&ll(e),J[i>>2]=j;break g}J[c+12>>2]=b;J[b+8>>2]=c;break g}g=J[f+24>>2];b=J[f+12>>2];if((f|0)!=(b|0)){c=J[f+8>>2];J[c+12>>2]=b;J[b+8>>2]=c;break h}e=f+20|0;c=J[e>>2];if(!c){c=J[f+16>>2];if(!c){break i}e=f+16|0;}while(1){h=e;b=c;e=b+20|0;c=J[e>>2];if(c){continue}e=b+16|0;c=J[b+16>>2];if(c){continue}break}J[h>>2]=0;break h}J[f+4>>2]=b&-2;J[d+4>>2]=a|1;J[a+d>>2]=a;break f}b=0;}if(!g){break g}c=J[f+28>>2];e=(c<<2)+26040|0;j:{if(J[e>>2]==(f|0)){J[e>>2]=b;if(b){break j}i=25740,j=J[6435]&ll(c),J[i>>2]=j;break g}J[g+(J[g+16>>2]==(f|0)?16:20)>>2]=b;if(!b){break g}}J[b+24>>2]=g;c=J[f+16>>2];if(c){J[b+16>>2]=c;J[c+24>>2]=b;}c=J[f+20>>2];if(!c){break g}J[b+20>>2]=c;J[c+24>>2]=b;}J[d+4>>2]=a|1;J[a+d>>2]=a;if(J[6439]!=(d|0)){break f}J[6436]=a;return}if(a>>>0<=255){b=(a&-8)+25776|0;c=J[6434];a=1<<(a>>>3);k:{if(!(c&a)){J[6434]=a|c;a=b;break k}a=J[b+8>>2];}J[b+8>>2]=d;J[a+12>>2]=d;J[d+12>>2]=b;J[d+8>>2]=a;return}c=31;if(a>>>0<=16777215){b=S(a>>>8|0);c=((a>>>38-b&1)-(b<<1)|0)+62|0;}J[d+28>>2]=c;J[d+16>>2]=0;J[d+20>>2]=0;b=(c<<2)+26040|0;l:{m:{e=J[6435];h=1<<c;n:{if(!(e&h)){J[6435]=e|h;J[b>>2]=d;J[d+24>>2]=b;break n}c=a<<((c|0)!=31?25-(c>>>1|0)|0:0);b=J[b>>2];while(1){e=b;if((J[b+4>>2]&-8)==(a|0)){break m}b=c>>>29|0;c=c<<1;h=e+(b&4)|0;b=J[h+16>>2];if(b){continue}break}J[h+16>>2]=d;J[d+24>>2]=e;}J[d+12>>2]=d;J[d+8>>2]=d;break l}a=J[e+8>>2];J[a+12>>2]=d;J[e+8>>2]=d;J[d+24>>2]=0;J[d+12>>2]=e;J[d+8>>2]=a;}a=J[6442]-1|0;J[6442]=a?a:-1;}}function Kg(a,b){a=a|0;b=b|0;var c=Q(0),d=0,e=Q(0),f=Q(0),g=0,h=Q(0),i=Q(0),j=0,k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0),B=Q(0),C=Q(0),D=Q(0),E=Q(0),F=0,G=0,H=Q(0),I=Q(0),L=0,M=Q(0),O=Q(0);g=J[a+48>>2];F=J[g+8>>2];J[a+144>>2]=F;j=J[a+52>>2];G=J[j+8>>2];J[a+148>>2]=G;H=N[g+32>>2];d=J[g+32>>2];p=N[g+28>>2];J[a+152>>2]=J[g+28>>2];J[a+156>>2]=d;I=N[j+32>>2];L=J[j+32>>2];n=N[j+28>>2];d=J[j+28>>2];J[a+160>>2]=d;J[a+164>>2]=L;x=N[g+120>>2];N[a+168>>2]=x;y=N[j+120>>2];N[a+172>>2]=y;v=N[g+128>>2];N[a+176>>2]=v;m=N[j+128>>2];N[a+180>>2]=m;g=J[b+28>>2];j=P(F,12);d=g+j|0;z=N[d>>2];A=N[d+4>>2];B=N[d+8>>2];d=g;g=P(G,12);d=d+g|0;C=N[d>>2];D=N[d+4>>2];E=N[d+8>>2];d=j;j=J[b+24>>2];d=d+j|0;h=N[d>>2];g=g+j|0;k=N[g>>2];r=N[d+4>>2];o=N[g+4>>2];M=N[a+72>>2];l=N[g+8>>2];i=N[a+80>>2];q=N[a+88>>2];c=N[d+8>>2];O=N[a+68>>2];f=N[a+76>>2];s=N[a+84>>2];e=Q(v+m);N[a+232>>2]=e==Q(0)?Q(1):e;e=Ua(c);c=Ta(c);w=Q(Q(s*c)+Q(e*q));N[a+188>>2]=w;q=Q(Q(e*s)-Q(q*c));N[a+184>>2]=q;t=N[a+92>>2];u=N[a+96>>2];s=Q(Q(c*t)+Q(e*u));N[a+196>>2]=s;t=Q(Q(e*t)-Q(u*c));N[a+192>>2]=t;u=Ua(l);n=Q(f-n);f=Q(i-I);l=Ta(l);i=Q(Q(u*n)-Q(f*l));f=Q(Q(l*n)+Q(u*f));l=Q(Q(i*w)-Q(q*f));N[a+212>>2]=l;n=Q(Q(i*s)-Q(t*f));N[a+204>>2]=n;u=Q(Q(k-h)+i);i=Q(O-p);h=Q(M-H);k=Q(Q(e*i)-Q(c*h));p=Q(u-k);e=Q(Q(c*i)+Q(e*h));f=Q(Q(Q(o-r)+f)-e);c=Q(k+p);i=Q(e+f);e=Q(Q(c*w)-Q(q*i));N[a+208>>2]=e;i=Q(Q(c*s)-Q(t*i));N[a+200>>2]=i;c=Q(v*e);r=Q(m*l);h=Q(c+r);N[a+244>>2]=h;N[a+236>>2]=h;h=Q(v*i);k=Q(m*n);o=Q(h+k);N[a+228>>2]=o;N[a+220>>2]=o;o=Q(Q(h*e)+Q(l*k));N[a+240>>2]=o;N[a+224>>2]=o;o=Q(r*l);r=Q(x+y);c=Q(o+Q(Q(c*e)+r));N[a+248>>2]=c;N[a+216>>2]=Q(k*n)+Q(Q(h*i)+r);N[a+252>>2]=c>Q(0)?Q(Q(1)/c):c;a:{if(K[a+136|0]){h=N[a+124>>2];k=N[a+120>>2];c=Q(h-k);if((c>Q(0)?c:Q(-c))<Q(.009999999776482582)){J[a+140>>2]=3;break a}c=Q(Q(q*p)+Q(w*f));if(c<=k){if(J[a+140>>2]==1){break a}J[a+140>>2]=1;J[a+112>>2]=0;break a}if(c>=h){if(J[a+140>>2]==2){break a}J[a+140>>2]=2;J[a+112>>2]=0;break a}J[a+140>>2]=0;J[a+112>>2]=0;break a}J[a+140>>2]=0;J[a+112>>2]=0;}if(!K[a+137|0]){J[a+116>>2]=0;}b:{if(K[b+20|0]){f=N[b+8>>2];c=Q(f*N[a+104>>2]);N[a+104>>2]=c;h=Q(f*N[a+116>>2]);N[a+116>>2]=h;p=Q(f*N[a+108>>2]);N[a+108>>2]=p;f=Q(f*N[a+112>>2]);N[a+112>>2]=f;k=m;m=Q(h+f);E=Q(Q(k*Q(Q(m*l)+Q(Q(c*n)+p)))+E);B=Q(B-Q(v*Q(Q(m*e)+Q(Q(c*i)+p))));e=Q(Q(s*c)+Q(m*w));D=Q(D+Q(y*e));c=Q(Q(t*c)+Q(q*m));C=Q(C+Q(y*c));A=Q(A-Q(x*e));z=Q(z-Q(x*c));break b}J[a+104>>2]=0;J[a+108>>2]=0;J[a+112>>2]=0;J[a+116>>2]=0;}d=J[b+28>>2]+P(J[a+144>>2],12)|0;N[d+4>>2]=A;N[d>>2]=z;d=J[b+28>>2];N[(d+P(J[a+144>>2],12)|0)+8>>2]=B;d=d+P(J[a+148>>2],12)|0;N[d+4>>2]=D;N[d>>2]=C;N[(J[b+28>>2]+P(J[a+148>>2],12)|0)+8>>2]=E;}function Lg(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=Q(0),f=0,g=0,h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=0,m=Q(0),n=0,o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=0,t=0,u=0,v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0),B=0,C=0,D=Q(0),E=Q(0),F=Q(0),G=Q(0),K=0,L=0,M=Q(0),O=Q(0),R=0,S=0;g=J[J[a+48>>2]+12>>2];a=J[J[a+52>>2]+12>>2];f=Fa-96|0;Fa=f;l=b;J[b+60>>2]=0;e=N[a+8>>2];k=N[g+8>>2];J[f+92>>2]=0;r=Q(k+e);e=ed(f+92|0,g,c,a,d);a:{if(r<e){break a}J[f+88>>2]=0;k=ed(f+88|0,a,d,g,c);if(k>r){break a}B=k>Q(e+Q(.0005000000237487257));b:{if(B){n=2;b=c;c=g;g=f+88|0;break b}n=1;b=d;d=c;c=a;a=g;g=f+92|0;}v=N[b>>2];w=N[b+4>>2];F=N[d>>2];G=N[d+4>>2];k=N[b+12>>2];p=N[b+8>>2];h=N[d+12>>2];m=N[d+8>>2];s=J[g>>2];J[l+56>>2]=n;K=s<<3;x=Q(-p);d=0;n=J[c+148>>2];c:{if((n|0)<=0){break c}b=a+K|0;e=N[b+84>>2];i=N[b+88>>2];j=Q(Q(m*e)+Q(h*i));e=Q(Q(h*e)-Q(i*m));i=Q(Q(k*j)-Q(p*e));j=Q(Q(k*e)+Q(p*j));C=c+84|0;R=n&1;d:{if((n|0)==1){e=Q(34028234663852886e22);g=0;break d}S=n&-2;e=Q(34028234663852886e22);g=0;b=0;while(1){t=(g<<3)+C|0;o=Q(Q(j*N[t>>2])+Q(i*N[t+4>>2]));t=o<e;L=g|1;u=(L<<3)+C|0;q=Q(Q(j*N[u>>2])+Q(i*N[u+4>>2]));e=t?o:e;u=q<e;e=u?q:e;d=u?L:t?g:d;g=g+2|0;b=b+2|0;if((S|0)!=(b|0)){continue}break}}if(!R){break c}b=(g<<3)+C|0;d=Q(Q(j*N[b>>2])+Q(i*N[b+4>>2]))<e?g:d;}b=c+20|0;c=b+(d<<3)|0;e=N[c>>2];i=N[c+4>>2];I[f+74>>1]=1;H[f+73|0]=d;H[f+72|0]=s;N[f+68>>2]=w+Q(Q(p*e)+Q(k*i));N[f+64>>2]=v+Q(Q(k*e)+Q(i*x));c=d+1|0;c=(c|0)<(n|0)?c:0;b=b+(c<<3)|0;e=N[b>>2];i=N[b+4>>2];H[f+84|0]=s;N[f+80>>2]=w+Q(Q(p*e)+Q(k*i));I[f+86>>1]=1;H[f+85|0]=c;N[f+76>>2]=v+Q(Q(k*e)+Q(i*x));b=s+1|0;b=(b|0)<J[a+148>>2]?b:0;a=a+20|0;c=(b<<3)+a|0;q=N[c>>2];a=a+K|0;y=N[a>>2];e=Q(q-y);z=N[c+4>>2];A=N[a+4>>2];i=Q(z-A);j=Q(Y(Q(Q(e*e)+Q(i*i))));if(!(j<Q(1.1920928955078125e-7))){j=Q(Q(1)/j);i=Q(i*j);e=Q(e*j);}j=Q(Q(m*e)+Q(h*i));N[f+60>>2]=j;D=Q(-m);o=Q(Q(h*e)+Q(i*D));N[f+56>>2]=o;N[f+4>>2]=-j;E=Q(-o);N[f>>2]=E;M=Q(F+Q(Q(h*y)+Q(A*D)));O=Q(G+Q(Q(m*y)+Q(h*A)));if((Xb(f+32|0,f- -64|0,f,Q(r-Q(Q(o*M)+Q(O*j))),s)|0)<2){break a}if((Xb(f,f+32|0,f+56|0,Q(r+Q(Q(o*Q(F+Q(Q(h*q)+Q(z*D))))+Q(Q(G+Q(Q(m*q)+Q(h*z)))*j))),b)|0)<2){break a}N[l+48>>2]=Q(y+q)*Q(.5);N[l+40>>2]=i;N[l+52>>2]=Q(A+z)*Q(.5);N[l+44>>2]=-e;g=0;e=N[f>>2];h=N[f+4>>2];m=Q(Q(j*M)-Q(O*o));e:{if(!(Q(Q(Q(j*e)+Q(h*E))-m)<=r)){break e}e=Q(e-v);h=Q(h-w);N[l+4>>2]=Q(x*e)+Q(k*h);N[l>>2]=Q(k*e)+Q(p*h);a=J[f+8>>2];J[l+16>>2]=a;g=1;if(!B){break e}H[l+17|0]=a;H[l+19|0]=a>>>16;H[l+18|0]=a>>>24;H[l+16|0]=a>>>8;}e=N[f+12>>2];h=N[f+16>>2];if(Q(Q(Q(j*e)+Q(h*E))-m)<=r){a=P(g,20)+l|0;e=Q(e-v);h=Q(h-w);N[a+4>>2]=Q(x*e)+Q(k*h);N[a>>2]=Q(k*e)+Q(p*h);b=J[f+20>>2];J[a+16>>2]=b;if(B){H[a+17|0]=b;H[a+19|0]=b>>>16;H[a+18|0]=b>>>24;H[a+16|0]=b>>>8;}g=g+1|0;}J[l+60>>2]=g;}Fa=f+96|0;}function Sk(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0;a:{g=a+16|0;e=J[g>>2];if(!e){break a}c=g;d=e;while(1){f=M[d+16>>2]<b>>>0;c=f?c:d;d=J[(f?d+4|0:d)>>2];if(d){continue}break}if((c|0)==(g|0)|M[c+16>>2]>b>>>0){break a}b=J[c+4>>2];b:{if(!b){b=c;while(1){d=J[b+8>>2];g=J[d>>2]!=(b|0);b=d;if(g){continue}break}break b}while(1){d=b;b=J[b>>2];if(b){continue}break}}if(J[a+12>>2]==(c|0)){J[a+12>>2]=d;}J[a+20>>2]=J[a+20>>2]-1;d=e;g=c;c:{d:{b=c;c=J[b>>2];if(c){a=J[g+4>>2];if(!a){break d}while(1){b=a;a=J[a>>2];if(a){continue}break}}c=J[b+4>>2];if(c){break d}c=0;e=1;break c}J[c+8>>2]=J[b+8>>2];e=0;}f=J[b+8>>2];a=J[f>>2];e:{if((b|0)==(a|0)){J[f>>2]=c;if((b|0)==(d|0)){a=0;d=c;break e}a=J[f+4>>2];break e}J[f+4>>2]=c;}h=!K[b+12|0];if((b|0)!=(g|0)){f=J[g+8>>2];J[b+8>>2]=f;J[f+(((g|0)!=J[J[g+8>>2]>>2])<<2)>>2]=b;f=J[g>>2];J[b>>2]=f;J[f+8>>2]=b;f=J[g+4>>2];J[b+4>>2]=f;if(f){J[f+8>>2]=b;}H[b+12|0]=K[g+12|0];d=(d|0)==(g|0)?b:d;}f:{if(h|!d){break f}if(e){while(1){b=K[a+12|0];g:{e=J[a+8>>2];if(J[e>>2]!=(a|0)){if(!b){H[a+12|0]=1;H[e+12|0]=0;c=J[e+4>>2];b=J[c>>2];J[e+4>>2]=b;if(b){J[b+8>>2]=e;}J[c+8>>2]=J[e+8>>2];b=J[e+8>>2];J[(((e|0)!=J[b>>2])<<2)+b>>2]=c;J[c>>2]=e;J[e+8>>2]=c;b=a;a=J[a>>2];d=(a|0)==(d|0)?b:d;a=J[a+4>>2];}h:{i:{b=J[a>>2];j:{if(!(K[b+12|0]?0:b)){c=J[a+4>>2];if(K[c+12|0]?0:c){break j}H[a+12|0]=0;a=J[a+8>>2];k:{if((d|0)==(a|0)){a=d;break k}if(K[a+12|0]){break g}}H[a+12|0]=1;break f}c=J[a+4>>2];if(!c){break i}}if(K[c+12|0]){break i}b=a;break h}H[b+12|0]=1;H[a+12|0]=0;c=J[b+4>>2];J[a>>2]=c;if(c){J[c+8>>2]=a;}J[b+8>>2]=J[a+8>>2];c=J[a+8>>2];J[((J[c>>2]!=(a|0))<<2)+c>>2]=b;J[b+4>>2]=a;J[a+8>>2]=b;c=a;}d=J[b+8>>2];H[b+12|0]=K[d+12|0];H[d+12|0]=1;H[c+12|0]=1;b=J[d+4>>2];a=J[b>>2];J[d+4>>2]=a;if(a){J[a+8>>2]=d;}J[b+8>>2]=J[d+8>>2];a=J[d+8>>2];J[(((d|0)!=J[a>>2])<<2)+a>>2]=b;J[b>>2]=d;J[d+8>>2]=b;break f}if(!b){H[a+12|0]=1;H[e+12|0]=0;b=J[a+4>>2];J[e>>2]=b;if(b){J[b+8>>2]=e;}J[a+8>>2]=J[e+8>>2];b=J[e+8>>2];J[(((e|0)!=J[b>>2])<<2)+b>>2]=a;J[a+4>>2]=e;J[e+8>>2]=a;d=(d|0)==(e|0)?a:d;a=J[e>>2];}c=J[a>>2];l:{if(!(!c|K[c+12|0])){b=a;break l}b=J[a+4>>2];if(!(K[b+12|0]?0:b)){H[a+12|0]=0;a=J[a+8>>2];if((a|0)!=(d|0)?K[a+12|0]:0){break g}H[a+12|0]=1;break f}if(c){if(!K[c+12|0]){b=a;break l}b=J[a+4>>2];}H[b+12|0]=1;H[a+12|0]=0;c=J[b>>2];J[a+4>>2]=c;if(c){J[c+8>>2]=a;}J[b+8>>2]=J[a+8>>2];c=J[a+8>>2];J[((J[c>>2]!=(a|0))<<2)+c>>2]=b;J[b>>2]=a;J[a+8>>2]=b;c=a;}d=J[b+8>>2];H[b+12|0]=K[d+12|0];H[d+12|0]=1;H[c+12|0]=1;b=J[d>>2];a=J[b+4>>2];J[d>>2]=a;if(a){J[a+8>>2]=d;}J[b+8>>2]=J[d+8>>2];a=J[d+8>>2];J[(((d|0)!=J[a>>2])<<2)+a>>2]=b;J[b+4>>2]=d;J[d+8>>2]=b;break f}b=a;a=J[a+8>>2];a=J[(((b|0)==J[a>>2])<<2)+a>>2];continue}}H[c+12|0]=1;}Wa(g);}}function Uf(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=Q(0),f=0,g=0,h=Q(0),i=Q(0),j=Q(0),k=0,l=0,m=0,n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0),B=0,C=0;g=Fa-48|0;Fa=g;m=J[a+48>>2];B=J[m+8>>2];J[a+116>>2]=B;k=J[a+52>>2];C=J[k+8>>2];J[a+120>>2]=C;n=N[m+32>>2];f=J[m+32>>2];c=N[m+28>>2];l=J[m+28>>2];J[a+140>>2]=l;J[a+144>>2]=f;r=N[k+32>>2];f=J[k+32>>2];h=N[k+28>>2];J[a+148>>2]=J[k+28>>2];J[a+152>>2]=f;t=N[m+120>>2];N[a+156>>2]=t;u=N[k+120>>2];N[a+160>>2]=u;o=N[m+128>>2];N[a+164>>2]=o;p=N[k+128>>2];N[a+168>>2]=p;i=N[a+84>>2];m=J[b+24>>2];l=P(B,12);s=N[(m+l|0)+8>>2];d=Ta(s);j=N[a+80>>2];e=Ua(s);k=J[b+28>>2];l=k+l|0;v=N[l>>2];w=N[l+4>>2];x=N[l+8>>2];f=k;k=P(C,12);l=f+k|0;y=N[l>>2];z=N[l+4>>2];A=N[l+8>>2];q=N[(k+m|0)+8>>2];j=Q(j-c);i=Q(i-n);c=Q(Q(e*j)-Q(d*i));N[a+124>>2]=c;i=Q(Q(d*j)+Q(e*i));N[a+128>>2]=i;d=N[a+92>>2];e=Ta(q);j=N[a+88>>2];n=Ua(q);h=Q(j-h);j=Q(d-r);d=Q(Q(n*h)-Q(e*j));N[a+132>>2]=d;e=Q(Q(e*h)+Q(n*j));N[a+136>>2]=e;j=Q(Q(c*o)+Q(p*d));N[g+40>>2]=j;h=Q(o+p);N[g+44>>2]=h;N[g+32>>2]=j;j=Q(t+u);N[g+28>>2]=Q(p*Q(d*d))+Q(Q(o*Q(c*c))+j);n=Q(-i);r=Q(Q(o*n)-Q(p*e));N[g+36>>2]=r;N[g+20>>2]=r;c=Q(Q(o*Q(c*n))-Q(p*Q(d*e)));N[g+24>>2]=c;N[g+12>>2]=Q(p*Q(e*e))+Q(Q(o*Q(i*i))+j);N[g+16>>2]=c;a:{if(N[a+68>>2]>Q(0)){Bd(g+12|0,a+172|0);c=N[b>>2];d=Q(N[a+68>>2]*Q(6.2831854820251465));e=h>Q(0)?Q(Q(1)/h):Q(0);i=Q(d*Q(d*e));d=Q(c*Q(Q(c*i)+Q(d*Q(Q(e+e)*N[a+72>>2]))));d=d!=Q(0)?Q(Q(1)/d):Q(0);N[a+100>>2]=d;N[a+76>>2]=Q(Q(c*Q(Q(q-s)-N[a+96>>2]))*i)*d;c=Q(h+d);N[a+204>>2]=c!=Q(0)?Q(Q(1)/c):Q(0);break a}f=a+172|0;if(h==Q(0)){Bd(g+12|0,f);J[a+76>>2]=0;J[a+100>>2]=0;break a}h=N[g+24>>2];d=N[g+40>>2];i=N[g+28>>2];e=N[g+36>>2];j=Q(Q(h*d)-Q(i*e));q=N[g+12>>2];s=N[g+44>>2];n=Q(i*s);c=N[g+32>>2];r=Q(s*Q(-h));c=Q(Q(N[g+20>>2]*j)+Q(Q(q*Q(n-Q(d*c)))+Q(N[g+16>>2]*Q(Q(c*e)+r))));c=c!=Q(0)?Q(Q(1)/c):c;N[f+32>>2]=Q(Q(q*i)-Q(h*h))*c;h=Q(c*Q(Q(e*h)-Q(d*q)));N[f+28>>2]=h;i=Q(j*c);N[f+24>>2]=i;N[f+20>>2]=h;N[f+16>>2]=c*Q(Q(q*s)-Q(e*e));e=Q(c*Q(Q(e*d)+r));N[f+12>>2]=e;N[f+8>>2]=i;N[f+4>>2]=e;N[f>>2]=c*Q(n-Q(d*d));J[a+76>>2]=0;J[a+100>>2]=0;}b:{if(K[b+20|0]){e=N[b+8>>2];c=Q(e*N[a+104>>2]);N[a+104>>2]=c;d=Q(e*N[a+108>>2]);N[a+108>>2]=d;e=Q(e*N[a+112>>2]);N[a+112>>2]=e;A=Q(Q(p*Q(e+Q(Q(N[a+132>>2]*d)-Q(c*N[a+136>>2]))))+A);x=Q(x-Q(o*Q(e+Q(Q(N[a+124>>2]*d)-Q(c*N[a+128>>2])))));y=Q(y+Q(u*c));v=Q(v-Q(t*c));z=Q(z+Q(u*d));w=Q(w-Q(t*d));break b}J[a+104>>2]=0;J[a+108>>2]=0;J[a+112>>2]=0;}f=J[b+28>>2]+P(J[a+116>>2],12)|0;N[f+4>>2]=w;N[f>>2]=v;f=J[b+28>>2];N[(f+P(J[a+116>>2],12)|0)+8>>2]=x;f=f+P(J[a+120>>2],12)|0;N[f+4>>2]=z;N[f>>2]=y;N[(J[b+28>>2]+P(J[a+120>>2],12)|0)+8>>2]=A;Fa=g+48|0;}function Of(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=Q(0),f=Q(0),g=0,h=0,i=Q(0),j=0,k=Q(0),l=0,m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0),B=0,C=Q(0),D=Q(0),E=Q(0),F=Q(0),G=Q(0),H=Q(0),I=0,L=Q(0);h=J[a+48>>2];B=J[h+8>>2];J[a+132>>2]=B;j=J[a+52>>2];I=J[j+8>>2];J[a+136>>2]=I;w=N[h+32>>2];g=J[h+32>>2];f=N[h+28>>2];J[a+140>>2]=J[h+28>>2];J[a+144>>2]=g;L=N[j+32>>2];l=J[j+32>>2];k=N[j+28>>2];g=J[j+28>>2];J[a+148>>2]=g;J[a+152>>2]=l;x=N[h+120>>2];N[a+156>>2]=x;o=N[j+120>>2];N[a+160>>2]=o;u=N[h+128>>2];N[a+164>>2]=u;v=N[j+128>>2];N[a+168>>2]=v;h=P(B,12);j=J[b+28>>2];g=h+j|0;C=N[g>>2];D=N[g+4>>2];E=N[g+8>>2];g=j;j=P(I,12);g=g+j|0;F=N[g>>2];G=N[g+4>>2];H=N[g+8>>2];g=h;h=J[b+24>>2];g=g+h|0;y=N[g>>2];h=h+j|0;z=N[h>>2];n=N[g+4>>2];p=N[h+4>>2];q=N[a+80>>2];m=N[h+8>>2];e=N[a+88>>2];r=N[a+104>>2];d=N[g+8>>2];A=N[a+76>>2];s=N[a+84>>2];i=N[a+100>>2];J[a+220>>2]=0;J[a+212>>2]=0;J[a+216>>2]=0;c=Ua(d);d=Ta(d);t=Q(Q(i*d)+Q(c*r));N[a+184>>2]=t;r=Q(Q(c*i)-Q(r*d));N[a+180>>2]=r;i=Ua(m);s=Q(s-k);e=Q(e-L);m=Ta(m);k=Q(Q(i*s)-Q(e*m));e=Q(Q(m*s)+Q(i*e));m=Q(Q(k*t)-Q(r*e));N[a+200>>2]=m;i=Q(A-f);f=Q(q-w);q=Q(Q(c*i)-Q(d*f));y=Q(Q(Q(z+k)-y)-q);i=Q(Q(d*i)+Q(c*f));z=Q(Q(Q(p+e)-n)-i);n=Q(q+y);p=Q(i+z);i=Q(Q(n*t)-Q(r*p));N[a+196>>2]=i;q=Q(x+o);f=Q(Q(Q(v*m)*m)+Q(Q(Q(u*i)*i)+q));N[a+204>>2]=f>Q(0)?Q(Q(1)/f):f;A=N[a+68>>2];a:{if(A>Q(0)){s=N[a+92>>2];w=N[a+96>>2];f=Q(Q(d*s)+Q(c*w));N[a+176>>2]=f;c=Q(Q(c*s)+Q(w*Q(-d)));N[a+172>>2]=c;d=Q(Q(k*f)+Q(c*Q(-e)));N[a+192>>2]=d;k=Q(Q(n*f)+Q(c*Q(-p)));N[a+188>>2]=k;d=Q(Q(Q(v*d)*d)+Q(Q(Q(u*k)*k)+q));if(!(d>Q(0))){break a}k=N[b>>2];e=Q(A*Q(6.2831854820251465));n=Q(Q(1)/d);p=Q(e*Q(e*n));e=Q(k*Q(Q(k*p)+Q(e*Q(Q(n+n)*N[a+72>>2]))));e=e>Q(0)?Q(Q(1)/e):e;N[a+220>>2]=e;N[a+216>>2]=Q(p*Q(k*Q(Q(y*c)+Q(z*f))))*e;c=Q(d+e);N[a+212>>2]=c>Q(0)?Q(Q(1)/c):c;break a}J[a+116>>2]=0;}b:{if(K[a+128|0]){c=Q(u+v);N[a+208>>2]=c;if(!(c>Q(0))){break b}N[a+208>>2]=Q(1)/c;break b}J[a+112>>2]=0;J[a+208>>2]=0;}c:{if(K[b+20|0]){f=N[b+8>>2];c=Q(f*N[a+108>>2]);N[a+108>>2]=c;d=Q(f*N[a+116>>2]);N[a+116>>2]=d;f=Q(f*N[a+112>>2]);N[a+112>>2]=f;t=Q(Q(t*c)+Q(d*N[a+176>>2]));G=Q(G+Q(o*t));e=o;o=Q(Q(r*c)+Q(d*N[a+172>>2]));F=Q(F+Q(e*o));D=Q(D-Q(x*t));C=Q(C-Q(x*o));H=Q(Q(v*Q(f+Q(Q(c*m)+Q(d*N[a+192>>2]))))+H);E=Q(E-Q(u*Q(f+Q(Q(c*i)+Q(d*N[a+188>>2])))));break c}J[a+116>>2]=0;J[a+108>>2]=0;J[a+112>>2]=0;}l=J[b+28>>2]+P(B,12)|0;N[l+4>>2]=D;N[l>>2]=C;l=J[b+28>>2];N[(l+P(J[a+132>>2],12)|0)+8>>2]=E;l=l+P(J[a+136>>2],12)|0;N[l+4>>2]=G;N[l>>2]=F;N[(J[b+28>>2]+P(J[a+136>>2],12)|0)+8>>2]=H;}function Ig(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=Q(0),f=0,g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=0,o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=0,v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0),B=Q(0),C=Q(0),D=Q(0),E=Q(0),F=Q(0),G=Q(0),H=Q(0);f=Fa+-64|0;Fa=f;o=N[a+156>>2];h=N[a+72>>2];u=J[b+24>>2];n=u+P(J[a+144>>2],12)|0;w=N[n+8>>2];c=Ua(w);q=N[a+152>>2];r=N[a+68>>2];d=Ta(w);p=N[a+164>>2];s=N[a+80>>2];u=u+P(J[a+148>>2],12)|0;x=N[u+8>>2];m=Ua(x);k=N[a+160>>2];t=N[a+76>>2];j=Ta(x);l=N[a+92>>2];v=N[a+96>>2];y=Q(Q(c*l)-Q(d*v));D=N[u>>2];k=Q(t-k);s=Q(s-p);p=Q(Q(m*k)-Q(j*s));E=N[n>>2];q=Q(r-q);o=Q(h-o);h=Q(Q(c*q)-Q(d*o));e=Q(Q(Q(D+p)-E)-h);t=Q(Q(d*l)+Q(c*v));F=N[u+4>>2];m=Q(Q(j*k)+Q(m*s));G=N[n+4>>2];j=Q(Q(d*q)+Q(c*o));i=Q(Q(Q(F+m)-G)-j);l=Q(Q(y*e)+Q(t*i));v=Q(-l);q=l>Q(0)?l:v;h=Q(h+e);j=Q(j+i);o=Q(Q(h*t)-Q(y*j));k=h;h=N[a+84>>2];r=N[a+88>>2];z=Q(Q(d*h)+Q(c*r));A=Q(Q(c*h)-Q(r*d));r=Q(Q(k*z)-Q(A*j));h=Q(Q(p*t)-Q(y*m));p=Q(Q(p*z)-Q(A*m));B=Q(Q(x-w)-N[a+100>>2]);C=Q(-B);m=N[a+180>>2];j=N[a+176>>2];s=N[a+172>>2];k=N[a+168>>2];a:{b:{if(!K[a+136|0]){break b}c=Q(Q(A*e)+Q(z*i));e=N[a+124>>2];d=N[a+120>>2];i=Q(e-d);c:{if((i>Q(0)?i:Q(-i))<Q(.009999999776482582)){g=c<Q(.20000000298023224)?c:Q(.20000000298023224);g=g<Q(-.20000000298023224)?Q(-.20000000298023224):g;d=c>Q(0)?c:Q(-c);break c}if(c<=d){g=Q(Q(c-d)+Q(.004999999888241291));g=g<Q(0)?g:Q(0);g=g<Q(-.20000000298023224)?Q(-.20000000298023224):g;d=Q(d-c);break c}if(!(c>=e)){break b}d=Q(c-e);c=Q(d+Q(-.004999999888241291));c=c<Q(.20000000298023224)?c:Q(.20000000298023224);g=c<Q(0)?Q(0):c;}c=Q(j*r);l=Q(m*p);e=Q(c+l);N[f+56>>2]=e;N[f+48>>2]=e;e=Q(j+m);N[f+44>>2]=e==Q(0)?Q(1):e;e=Q(k+s);N[f+60>>2]=Q(l*p)+Q(Q(c*r)+e);c=Q(j*o);l=Q(m*h);i=Q(c+l);N[f+40>>2]=i;N[f+32>>2]=i;i=Q(Q(c*r)+Q(p*l));N[f+52>>2]=i;N[f+36>>2]=i;N[f+28>>2]=Q(l*h)+Q(Q(c*o)+e);N[f+12>>2]=-g;N[f+8>>2]=C;N[f+4>>2]=v;q=d<q?q:d;Vb(f+16|0,f+28|0,f+4|0);g=N[f+24>>2];d=N[f+20>>2];c=N[f+16>>2];break a}c=Q(m*h);d=Q(j*o);i=Q(Q(c*h)+Q(Q(d*o)+Q(k+s)));e=Q(j+m);H=e==Q(0)?Q(1):e;c=Q(d+c);d=Q(Q(i*H)-Q(c*c));e=d!=Q(0)?Q(Q(1)/d):d;d=Q(Q(Q(i*C)+Q(l*c))*e);c=Q(Q(Q(H*v)+Q(B*c))*e);}n=J[b+24>>2]+P(J[a+144>>2],12)|0;t=Q(Q(t*c)+Q(z*g));N[n+4>>2]=G-Q(k*t);e=k;k=Q(Q(y*c)+Q(A*g));N[n>>2]=E-Q(e*k);n=J[b+24>>2];N[(n+P(J[a+144>>2],12)|0)+8>>2]=w-Q(j*Q(Q(g*r)+Q(Q(c*o)+d)));n=n+P(J[a+148>>2],12)|0;N[n+4>>2]=F+Q(s*t);N[n>>2]=D+Q(s*k);N[(J[b+24>>2]+P(J[a+148>>2],12)|0)+8>>2]=Q(m*Q(Q(g*p)+Q(Q(c*h)+d)))+x;Fa=f- -64|0;return (B>Q(0)?B:C)<=Q(.03490658849477768)&q<=Q(.004999999888241291)}function wh(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=Q(0),f=0,g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=0,l=Q(0),m=Q(0),n=Q(0),o=0,p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0),B=Q(0),C=Q(0),D=Q(0),E=Q(0),F=Q(0),G=0,H=Q(0),I=Q(0),K=Q(0),L=Q(0),M=Q(0),O=Q(0),R=Q(0),S=Q(0);f=J[a+76>>2];k=J[b+24>>2];G=k+P(J[a+172>>2],12)|0;H=N[G+4>>2];I=N[G>>2];o=k+P(J[a+168>>2],12)|0;u=N[o+8>>2];K=N[o+4>>2];L=N[o>>2];o=k+P(J[a+164>>2],12)|0;M=N[o+4>>2];O=N[o>>2];k=k+P(J[a+160>>2],12)|0;v=N[k+8>>2];R=N[k+4>>2];S=N[k>>2];w=N[o+8>>2];x=N[G+8>>2];r=Ua(x);s=Ta(x);g=Ua(w);n=Ta(w);a:{if((f|0)==1){y=N[a+224>>2];z=N[a+232>>2];p=Q(y+z);A=Q(1);B=Q(1);c=Q(Q(v-u)-N[a+140>>2]);break a}q=N[a+124>>2];l=Ua(u);j=N[a+128>>2];m=Ta(u);d=N[a+180>>2];c=N[a+96>>2];i=Ua(v);e=Q(N[a+92>>2]-N[a+176>>2]);h=Ta(v);c=Q(c-d);d=Q(Q(i*e)-Q(h*c));C=Q(Q(m*q)+Q(l*j));D=Q(Q(l*q)-Q(j*m));c=Q(Q(h*e)+Q(i*c));A=Q(Q(d*C)-Q(D*c));y=N[a+224>>2];e=Q(N[a+108>>2]-N[a+192>>2]);h=Q(N[a+112>>2]-N[a+196>>2]);B=Q(Q(Q(Q(l*e)-Q(m*h))*C)-Q(D*Q(Q(m*e)+Q(l*h))));z=N[a+232>>2];p=Q(Q(Q(y*A)*A)+Q(Q(Q(B*z)*B)+Q(N[a+216>>2]+N[a+208>>2])));d=Q(Q(S-L)+d);c=Q(Q(R-K)+c);c=Q(Q(q*Q(Q(Q(l*d)+Q(m*c))-e))+Q(j*Q(Q(Q(l*c)-Q(m*d))-h)));}j=Q(0);d=Q(p+Q(0));b:{if(J[a+80>>2]==1){i=Q(Q(w-x)-N[a+144>>2]);e=N[a+152>>2];E=N[a+228>>2];F=N[a+236>>2];t=Q(Q(Q(e*e)*Q(E+F))+d);h=e;p=e;d=Q(0);break b}t=d;E=N[a+228>>2];e=N[a+152>>2];h=Q(N[a+100>>2]-N[a+184>>2]);d=Q(N[a+104>>2]-N[a+188>>2]);i=Q(Q(g*h)-Q(d*n));l=N[a+132>>2];m=N[a+136>>2];q=Q(Q(s*l)+Q(r*m));j=Q(Q(r*l)-Q(m*s));d=Q(Q(n*h)+Q(g*d));h=Q(e*Q(Q(i*q)-Q(j*d)));g=Q(N[a+116>>2]-N[a+200>>2]);n=Q(N[a+120>>2]-N[a+204>>2]);p=Q(e*Q(Q(Q(Q(r*g)-Q(n*s))*q)-Q(j*Q(Q(s*g)+Q(r*n)))));F=N[a+236>>2];t=Q(t+Q(Q(Q(E*h)*h)+Q(Q(Q(e*e)*Q(N[a+220>>2]+N[a+212>>2]))+Q(p*Q(p*F)))));i=Q(Q(O-I)+i);d=Q(Q(M-H)+d);i=Q(Q(Q(Q(Q(r*i)+Q(s*d))-g)*l)+Q(m*Q(Q(Q(r*d)-Q(s*i))-n)));j=Q(j*e);d=Q(q*e);}g=Q(0);g=t>Q(0)?Q(Q(-Q(Q(Q(e*i)+c)-N[a+148>>2]))/t):g;n=N[a+220>>2];i=N[a+216>>2];e=N[a+212>>2];c=Q(g*N[a+208>>2]);N[k+4>>2]=R+Q(C*c);N[k>>2]=S+Q(D*c);f=J[b+24>>2];N[(f+P(J[a+160>>2],12)|0)+8>>2]=Q(Q(g*y)*A)+v;f=f+P(J[a+164>>2],12)|0;c=Q(g*e);N[f+4>>2]=M+Q(d*c);N[f>>2]=O+Q(j*c);f=J[b+24>>2];N[(f+P(J[a+164>>2],12)|0)+8>>2]=Q(Q(g*E)*h)+w;f=f+P(J[a+168>>2],12)|0;c=Q(g*i);N[f+4>>2]=K-Q(C*c);N[f>>2]=L-Q(D*c);f=J[b+24>>2];N[(f+P(J[a+168>>2],12)|0)+8>>2]=u-Q(Q(g*z)*B);f=f+P(J[a+172>>2],12)|0;c=Q(g*n);N[f+4>>2]=H-Q(d*c);N[f>>2]=I-Q(j*c);N[(J[b+24>>2]+P(J[a+172>>2],12)|0)+8>>2]=x-Q(Q(g*F)*p);return 1}function yd(a,b){var c=Q(0),d=Q(0),e=Q(0),f=Q(0),g=0,h=0,i=0,j=0,k=0,l=0,m=Q(0),n=Q(0),o=0,p=0,q=0,r=0,s=Q(0),t=0,u=0,v=0,w=0,x=0;k=J[a+4>>2];h=k+P(b,40)|0;p=J[h+24>>2];if((p|0)==-1){return b}if(J[h+32>>2]<2){return b}j=P(b,40)+k|0;q=J[j+28>>2];g=P(q,40)+k|0;t=J[g+32>>2];l=P(p,40)+k|0;u=J[l+32>>2];i=t-u|0;a:{if((i|0)>=2){o=J[g+24>>2];J[g+24>>2]=b;J[g+20>>2]=J[j+20>>2];r=J[g+28>>2];J[j+20>>2]=q;t=P(r,40);v=t+k|0;p=P(o,40);w=p+k|0;i=J[g+20>>2];if((i|0)!=-1){a=J[a+4>>2]+P(i,40)|0;a=J[a+24>>2]==(b|0)?a+24|0:a+28|0;}J[a>>2]=q;t=k+t|0;a=J[t+32>>2];p=k+p|0;i=J[p+32>>2];b:{if((a|0)<(i|0)){x=p+32|0;J[g+28>>2]=o;J[j+28>>2]=r;J[t+20>>2]=b;f=N[v>>2];e=N[l>>2];d=N[l+4>>2];c=N[v+4>>2];m=c>d?d:c;N[h+4>>2]=m;n=e<f?e:f;N[h>>2]=n;s=N[v+8>>2];e=N[l+8>>2];d=N[l+12>>2];c=N[v+12>>2];f=c<d?d:c;N[h+12>>2]=f;e=e>s?e:s;N[h+8>>2]=e;d=N[w>>2];c=N[w+4>>2];N[g+4>>2]=c>m?m:c;N[g>>2]=d>n?n:d;c=N[w+12>>2];d=c<f?f:c;c=N[w+8>>2];c=c<e?e:c;break b}x=t+32|0;J[g+28>>2]=r;J[j+28>>2]=o;J[p+20>>2]=b;f=N[w>>2];e=N[l>>2];d=N[l+4>>2];c=N[w+4>>2];m=c>d?d:c;N[h+4>>2]=m;n=e<f?e:f;N[h>>2]=n;s=N[w+8>>2];e=N[l+8>>2];d=N[l+12>>2];c=N[w+12>>2];f=c<d?d:c;N[h+12>>2]=f;e=e>s?e:s;N[h+8>>2]=e;d=N[v>>2];c=N[v+4>>2];N[g+4>>2]=c>m?m:c;N[g>>2]=d>n?n:d;c=N[v+12>>2];d=c<f?f:c;a=i;c=N[v+8>>2];c=c<e?e:c;}i=g+32|0;N[g+8>>2]=c;a=(a|0)<(u|0)?u:a;break a}if((i|0)>-2){return b}r=P(p,40)+k|0;u=J[r+24>>2];J[r+24>>2]=b;i=P(b,40)+k|0;J[r+20>>2]=J[i+20>>2];q=J[r+28>>2];J[i+20>>2]=p;i=J[r+20>>2];if((i|0)!=-1){a=J[a+4>>2]+P(i,40)|0;a=J[a+24>>2]==(b|0)?a+24|0:a+28|0;}J[a>>2]=p;j=P(q,40)+k|0;a=J[j+32>>2];o=P(u,40)+k|0;i=J[o+32>>2];c:{if((a|0)<(i|0)){x=o+32|0;J[r+28>>2]=u;J[h+24>>2]=q;J[(P(q,40)+k|0)+20>>2]=b;f=N[j>>2];e=N[g>>2];d=N[g+4>>2];c=N[j+4>>2];m=c>d?d:c;N[h+4>>2]=m;n=e<f?e:f;N[h>>2]=n;s=N[j+8>>2];e=N[g+8>>2];d=N[g+12>>2];c=N[j+12>>2];f=c<d?d:c;N[h+12>>2]=f;e=e>s?e:s;N[h+8>>2]=e;d=N[o>>2];c=N[o+4>>2];N[l+4>>2]=c>m?m:c;N[l>>2]=d>n?n:d;c=N[o+12>>2];d=c<f?f:c;c=N[o+8>>2];c=c<e?e:c;break c}x=j+32|0;J[r+28>>2]=q;J[h+24>>2]=u;J[(P(u,40)+k|0)+20>>2]=b;f=N[o>>2];e=N[g>>2];d=N[g+4>>2];c=N[o+4>>2];m=c>d?d:c;N[h+4>>2]=m;n=e<f?e:f;N[h>>2]=n;s=N[o+8>>2];e=N[g+8>>2];d=N[g+12>>2];c=N[o+12>>2];f=c<d?d:c;N[h+12>>2]=f;e=e>s?e:s;N[h+8>>2]=e;d=N[j>>2];c=N[j+4>>2];N[l+4>>2]=c>m?m:c;N[l>>2]=d>n?n:d;c=N[j+12>>2];d=c<f?f:c;a=i;c=N[j+8>>2];c=c<e?e:c;}i=l+32|0;N[l+8>>2]=c;q=p;a=(a|0)<(t|0)?t:a;}b=a+1|0;J[h+32>>2]=b;a=J[x>>2];N[(P(q,40)+k|0)+12>>2]=d;J[i>>2]=((a|0)<(b|0)?b:a)+1;return q}function Jg(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=0,f=0,g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=0,n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0);e=Fa-32|0;Fa=e;m=J[b+28>>2];f=m+P(J[a+148>>2],12)|0;h=N[f+8>>2];n=N[f+4>>2];o=N[f>>2];f=m+P(J[a+144>>2],12)|0;i=N[f+8>>2];p=N[f+4>>2];q=N[f>>2];l=N[a+180>>2];w=N[a+176>>2];t=N[a+172>>2];u=N[a+168>>2];f=J[a+140>>2];if(!(!K[a+137|0]|(f|0)==3)){c=Q(N[b>>2]*N[a+128>>2]);d=Q(-c);g=N[a+116>>2];r=N[a+212>>2];k=N[a+184>>2];j=N[a+188>>2];s=N[a+208>>2];v=Q(g+Q(N[a+252>>2]*Q(N[a+132>>2]-Q(Q(Q(r*h)+Q(Q(k*Q(o-q))+Q(Q(n-p)*j)))-Q(s*i)))));c=c>v?v:c;c=c<d?d:c;N[a+116>>2]=c;c=Q(c-g);h=Q(Q(l*Q(r*c))+h);d=Q(j*c);n=Q(n+Q(t*d));g=Q(k*c);o=Q(o+Q(t*g));p=Q(p-Q(u*d));q=Q(q-Q(u*g));i=Q(i-Q(w*Q(s*c)));}j=Q(o-q);s=Q(n-p);c=Q(Q(Q(N[a+204>>2]*h)+Q(Q(N[a+192>>2]*j)+Q(s*N[a+196>>2])))-Q(N[a+200>>2]*i));d=Q(h-i);a:{if(!(!K[a+136|0]|!f)){g=N[a+112>>2];r=N[a+108>>2];v=N[a+188>>2];k=N[a+104>>2];y=N[a+208>>2];z=N[a+212>>2];A=N[a+184>>2];d=Q(-d);N[e+12>>2]=d;x=Q(-c);N[e+8>>2]=x;N[e+16>>2]=-Q(Q(Q(z*h)+Q(Q(A*j)+Q(s*v)))-Q(y*i));m=a+216|0;Vb(e+20|0,m,e+8|0);N[a+104>>2]=N[e+20>>2]+N[a+104>>2];N[a+108>>2]=N[e+24>>2]+N[a+108>>2];c=Q(N[e+28>>2]+N[a+112>>2]);N[a+112>>2]=c;b:{c:{d:{switch(J[a+140>>2]-1|0){case 0:f=c>Q(0);break c;case 1:break d;default:break b}}f=c<Q(0);}c=f?c:Q(0);N[a+112>>2]=c;}j=N[a+240>>2];c=Q(c-g);N[e+12>>2]=d-Q(c*N[a+244>>2]);N[e+8>>2]=x-Q(j*c);Ab(e,m,e+8|0);c=N[e>>2];j=Q(r+N[e+4>>2]);N[a+108>>2]=j;c=Q(k+c);N[a+104>>2]=c;d=Q(c-k);g=Q(N[a+112>>2]-g);c=Q(Q(d*N[a+196>>2])+Q(g*N[a+188>>2]));k=l;l=Q(j-r);h=Q(Q(k*Q(Q(g*N[a+212>>2])+Q(Q(d*N[a+204>>2])+l)))+h);i=Q(i-Q(w*Q(Q(g*N[a+208>>2])+Q(Q(d*N[a+200>>2])+l))));d=Q(Q(d*N[a+192>>2])+Q(g*N[a+184>>2]));break a}N[e+12>>2]=-d;N[e+8>>2]=-c;Ab(e+20|0,a+216|0,e+8|0);d=N[e+20>>2];N[a+104>>2]=d+N[a+104>>2];g=N[e+24>>2];N[a+108>>2]=g+N[a+108>>2];c=Q(d*N[a+196>>2]);h=Q(Q(l*Q(g+Q(d*N[a+204>>2])))+h);i=Q(i-Q(w*Q(g+Q(d*N[a+200>>2]))));d=Q(d*N[a+192>>2]);}f=J[b+28>>2]+P(J[a+144>>2],12)|0;N[f+4>>2]=p-Q(u*c);N[f>>2]=q-Q(u*d);f=J[b+28>>2];N[(f+P(J[a+144>>2],12)|0)+8>>2]=i;f=f+P(J[a+148>>2],12)|0;N[f+4>>2]=n+Q(t*c);N[f>>2]=o+Q(t*d);N[(J[b+28>>2]+P(J[a+148>>2],12)|0)+8>>2]=h;Fa=e+32|0;}function tf(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=Q(0),f=0,g=Q(0),h=0,i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=0,q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=0,y=Q(0),z=Q(0);f=Fa-32|0;Fa=f;J[f+28>>2]=b;p=a+102868|0;J[f+24>>2]=p;J[f+16>>2]=1065353216;a=J[c+4>>2];J[f>>2]=J[c>>2];J[f+4>>2]=a;a=J[d+4>>2];J[f+8>>2]=J[d>>2];J[f+12>>2]=a;a=Fa-1088|0;Fa=a;i=N[f>>2];m=Q(N[f+8>>2]-i);n=m;j=N[f+4>>2];o=Q(N[f+12>>2]-j);e=o;g=Q(Y(Q(Q(m*m)+Q(e*e))));if(!(g<Q(1.1920928955078125e-7))){g=Q(Q(1)/g);e=Q(o*g);n=Q(m*g);}g=N[f+16>>2];J[a+1064>>2]=256;d=a+36|0;J[a+32>>2]=d;J[a+36>>2]=J[p>>2];k=Q(j+Q(o*g));t=j>k?j:k;l=Q(i+Q(m*g));u=i>l?i:l;v=j<k?j:k;k=i<l?i:l;y=n>Q(0)?n:Q(-n);w=Q(-e);z=e<Q(0)?w:e;c=1;while(1){a:{c=c-1|0;J[a+1060>>2]=c;h=J[a+32>>2];b=J[h+(c<<2)>>2];b:{if((b|0)==-1){break b}x=P(b,40);b=x+J[p+4>>2]|0;e=N[b+8>>2];if(Q(k-e)>Q(0)){break b}l=N[b+12>>2];if(Q(v-l)>Q(0)){break b}q=N[b>>2];if(Q(q-u)>Q(0)){break b}r=N[b+4>>2];if(Q(r-t)>Q(0)){break b}s=Q(Q(w*Q(i-Q(Q(e+q)*Q(.5))))+Q(n*Q(j-Q(Q(l+r)*Q(.5)))));if(Q((s>Q(0)?s:Q(-s))-Q(Q(z*Q(Q(e-q)*Q(.5)))+Q(y*Q(Q(l-r)*Q(.5)))))>Q(0)){break b}if(J[b+24>>2]==-1){b=J[f+4>>2];J[a+8>>2]=J[f>>2];J[a+12>>2]=b;b=J[f+12>>2];c=J[f+8>>2];N[a+24>>2]=g;J[a+16>>2]=c;J[a+20>>2]=b;c=J[(J[J[f+24>>2]+4>>2]+x|0)+16>>2];b=J[c+16>>2];h=J[b+12>>2];c:{if(Ha[J[J[h>>2]+20>>2]](h,a+1076|0,a+8|0,J[b+8>>2]+12|0,J[c+20>>2])|0){e=N[a+1084>>2];l=Q(Q(1)-e);N[a+1072>>2]=Q(l*N[a+12>>2])+Q(e*N[a+20>>2]);N[a+1068>>2]=Q(l*N[a+8>>2])+Q(e*N[a+16>>2]);c=J[f+28>>2];e=Q(Ha[J[J[c>>2]+8>>2]](c,b,a+1068|0,a+1076|0,e));break c}e=N[a+24>>2];}if(e>Q(0)){g=Q(j+Q(o*e));t=g<j?j:g;k=Q(i+Q(m*e));u=i>k?i:k;v=g>j?j:g;k=i<k?i:k;g=e;}if(e==Q(0)){break a}c=J[a+1060>>2];break b}d:{if(J[a+1064>>2]!=(c|0)){break d}J[a+1064>>2]=c<<1;c=_a(c<<3);J[a+32>>2]=c;eb(c,h,J[a+1060>>2]<<2);if((d|0)==(h|0)){break d}Wa(h);}c=J[a+32>>2];J[c+(J[a+1060>>2]<<2)>>2]=J[b+24>>2];h=J[a+1060>>2]+1|0;J[a+1060>>2]=h;e:{if((h|0)!=J[a+1064>>2]){break e}J[a+1064>>2]=h<<1;h=_a(h<<3);J[a+32>>2]=h;eb(h,c,J[a+1060>>2]<<2);if((c|0)==(d|0)){break e}Wa(c);}J[J[a+32>>2]+(J[a+1060>>2]<<2)>>2]=J[b+28>>2];c=J[a+1060>>2]+1|0;J[a+1060>>2]=c;}if((c|0)>0){continue}}break}b=J[a+32>>2];if((b|0)!=(d|0)){Wa(b);}Fa=a+1088|0;Fa=f+32|0;}function bd(a){var b=0,c=Q(0),d=0,e=Q(0),f=Q(0),g=Q(0),h=0,i=0,j=0,k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=0,s=0,t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0),B=Q(0),C=Q(0),D=Q(0),E=Q(0),F=Q(0),G=0;h=Fa+-64|0;Fa=h;if(J[a+48>>2]>0){while(1){b=J[a+40>>2]+P(s,156)|0;o=N[b+132>>2];p=N[b+128>>2];q=N[b+124>>2];z=N[b+120>>2];i=J[a+28>>2];j=P(J[b+116>>2],12);d=i+j|0;t=N[d+8>>2];A=N[d+4>>2];B=N[d>>2];r=P(J[b+112>>2],12);d=r+i|0;u=N[d+8>>2];C=N[d+4>>2];D=N[d>>2];d=J[a+36>>2]+P(s,88)|0;E=N[d+80>>2];F=N[d+76>>2];G=J[J[a+44>>2]+(J[b+152>>2]<<2)>>2];i=j;j=J[a+24>>2];i=i+j|0;v=N[i>>2];w=N[i+4>>2];c=N[d+56>>2];g=N[d+60>>2];j=j+r|0;x=N[j>>2];y=N[j+4>>2];f=N[d+48>>2];l=N[d+52>>2];k=N[i+8>>2];m=N[j+8>>2];n=Ua(m);N[h+60>>2]=n;m=Ta(m);N[h+56>>2]=m;e=Ua(k);N[h+44>>2]=e;k=Ta(k);N[h+40>>2]=k;N[h+52>>2]=y-Q(Q(m*f)+Q(l*n));N[h+48>>2]=x-Q(Q(n*f)-Q(l*m));N[h+36>>2]=w-Q(Q(k*c)+Q(g*e));N[h+32>>2]=v-Q(Q(e*c)-Q(g*k));Dd(h,G- -64|0,h+48|0,F,h+32|0,E);d=J[h+4>>2];J[b+72>>2]=J[h>>2];J[b+76>>2]=d;r=J[b+148>>2];a:{if((r|0)<=0){break a}g=Q(z+q);i=0;while(1){j=(i<<3)+h|0;c=N[j+8>>2];d=P(i,36)+b|0;l=Q(N[j+12>>2]-y);N[d+4>>2]=l;k=Q(c-x);N[d>>2]=k;c=N[j+8>>2];n=Q(N[j+12>>2]-w);N[d+12>>2]=n;m=Q(c-v);N[d+8>>2]=m;f=N[b+76>>2];c=N[b+72>>2];J[d+32>>2]=0;e=Q(Q(m*f)-Q(c*n));q=Q(Q(o*e)*e);e=Q(Q(k*f)-Q(c*l));e=Q(q+Q(Q(Q(p*e)*e)+g));N[d+24>>2]=e>Q(0)?Q(Q(1)/e):Q(0);e=Q(-c);q=Q(Q(m*e)-Q(f*n));e=Q(Q(k*e)-Q(f*l));e=Q(Q(Q(o*q)*q)+Q(Q(Q(p*e)*e)+g));N[d+28>>2]=e>Q(0)?Q(Q(1)/e):Q(0);f=Q(Q(c*Q(Q(u*l)+Q(Q(B-Q(t*n))-D)))+Q(f*Q(Q(Q(A+Q(t*m))-C)-Q(u*k))));if(f<Q(-1)){N[d+32>>2]=f*Q(-N[b+140>>2]);}i=i+1|0;if((r|0)!=(i|0)){continue}break}if(!K[23352]|J[b+148>>2]!=2){break a}l=N[b+76>>2];f=Q(Q(N[b+8>>2]*l)-Q(c*N[b+12>>2]));k=Q(o*f);e=Q(k*f);f=Q(Q(N[b>>2]*l)-Q(c*N[b+4>>2]));n=Q(p*f);f=Q(e+Q(Q(n*f)+g));e=o;o=Q(Q(N[b+44>>2]*l)-Q(c*N[b+48>>2]));c=Q(Q(N[b+36>>2]*l)-Q(c*N[b+40>>2]));p=Q(Q(Q(e*o)*o)+Q(Q(Q(p*c)*c)+g));c=Q(Q(k*o)+Q(Q(n*c)+g));g=Q(Q(f*p)-Q(c*c));if(Q(f*f)<Q(g*Q(1e3))){N[b+96>>2]=f;N[b+108>>2]=p;N[b+104>>2]=c;N[b+100>>2]=c;g=g!=Q(0)?Q(Q(1)/g):g;N[b+92>>2]=f*g;N[b+80>>2]=p*g;c=Q(c*Q(-g));N[b+88>>2]=c;N[b+84>>2]=c;break a}J[b+148>>2]=1;}s=s+1|0;if((s|0)<J[a+48>>2]){continue}break}}Fa=h- -64|0;}function mg(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=Q(0),f=0,g=Q(0),h=0,i=Q(0),j=0,k=0,l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=0,v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0),B=0,C=Q(0);j=J[a+48>>2];u=J[j+8>>2];J[a+128>>2]=u;h=J[a+52>>2];B=J[h+8>>2];J[a+132>>2]=B;e=N[j+32>>2];k=J[j+32>>2];m=N[j+28>>2];f=J[j+28>>2];J[a+152>>2]=f;J[a+156>>2]=k;C=N[h+32>>2];f=J[h+32>>2];n=N[h+28>>2];J[a+160>>2]=J[h+28>>2];J[a+164>>2]=f;s=N[j+120>>2];N[a+168>>2]=s;t=N[h+120>>2];N[a+172>>2]=t;o=N[j+128>>2];N[a+176>>2]=o;p=N[h+128>>2];N[a+180>>2]=p;g=N[a+72>>2];j=J[b+24>>2];f=P(u,12);q=N[(j+f|0)+8>>2];d=Ta(q);c=N[a+68>>2];i=Ua(q);h=J[b+28>>2];f=f+h|0;v=N[f>>2];w=N[f+4>>2];x=N[f+8>>2];f=h;h=P(B,12);f=f+h|0;y=N[f>>2];z=N[f+4>>2];A=N[f+8>>2];l=N[(h+j|0)+8>>2];c=Q(c-m);g=Q(g-e);m=Q(Q(i*c)-Q(d*g));N[a+136>>2]=m;c=Q(Q(d*c)+Q(i*g));N[a+140>>2]=c;d=Q(o+p);N[a+216>>2]=d;N[a+220>>2]=d>Q(0)?Q(Q(1)/d):d;i=N[a+80>>2];g=Ta(l);e=N[a+76>>2];r=Ua(l);n=Q(e-n);e=Q(i-C);i=Q(Q(r*n)-Q(g*e));N[a+144>>2]=i;g=Q(Q(g*n)+Q(r*e));N[a+148>>2]=g;n=Q(-c);e=Q(Q(o*n)-Q(p*g));N[a+208>>2]=e;r=Q(Q(m*o)+Q(p*i));N[a+212>>2]=r;N[a+192>>2]=e;N[a+204>>2]=r;e=Q(o*Q(c*c));c=Q(s+t);N[a+184>>2]=Q(p*Q(g*g))+Q(e+c);e=Q(Q(o*Q(m*n))-Q(p*Q(i*g)));N[a+196>>2]=e;N[a+188>>2]=e;N[a+200>>2]=Q(p*Q(i*i))+Q(Q(o*Q(m*m))+c);if(!(d!=Q(0)?K[a+100|0]:0)){J[a+96>>2]=0;}a:{if(!(!K[a+112|0]|d==Q(0))){c=N[a+124>>2];e=N[a+120>>2];d=Q(c-e);if((d>Q(0)?d:Q(-d))<Q(.06981317698955536)){J[a+224>>2]=3;break a}d=Q(Q(l-q)-N[a+116>>2]);if(d<=e){if(J[a+224>>2]!=1){J[a+92>>2]=0;}J[a+224>>2]=1;break a}if(d>=c){if(J[a+224>>2]!=2){J[a+92>>2]=0;}J[a+224>>2]=2;break a}J[a+224>>2]=0;J[a+92>>2]=0;break a}J[a+224>>2]=0;}b:{if(K[b+20|0]){c=N[b+8>>2];d=Q(c*N[a+84>>2]);N[a+84>>2]=d;q=Q(c*N[a+96>>2]);N[a+96>>2]=q;l=Q(c*N[a+88>>2]);N[a+88>>2]=l;c=Q(c*N[a+92>>2]);N[a+92>>2]=c;A=Q(Q(p*Q(c+Q(q+Q(Q(i*l)+Q(d*Q(-g))))))+A);x=Q(x-Q(o*Q(c+Q(q+Q(Q(m*l)+Q(d*n))))));y=Q(y+Q(t*d));v=Q(v-Q(s*d));z=Q(z+Q(t*l));w=Q(w-Q(s*l));break b}J[a+84>>2]=0;J[a+88>>2]=0;J[a+92>>2]=0;J[a+96>>2]=0;}k=J[b+28>>2]+P(u,12)|0;N[k+4>>2]=w;N[k>>2]=v;k=J[b+28>>2];N[(k+P(J[a+128>>2],12)|0)+8>>2]=x;k=k+P(J[a+132>>2],12)|0;N[k+4>>2]=z;N[k>>2]=y;N[(J[b+28>>2]+P(J[a+132>>2],12)|0)+8>>2]=A;}function lg(a,b){a=a|0;b=b|0;var c=0,d=Q(0),e=0,f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=0,s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=Q(0);c=Fa-32|0;Fa=c;n=N[a+176>>2];o=N[a+180>>2];f=Q(n+o);r=J[b+28>>2];e=r+P(J[a+132>>2],12)|0;g=N[e+8>>2];s=N[e+4>>2];t=N[e>>2];e=P(J[a+128>>2],12)+r|0;h=N[e+8>>2];u=N[e+4>>2];v=N[e>>2];e=J[a+224>>2];if(!(!K[a+100|0]|(e|0)==3|f==Q(0))){d=Q(N[b>>2]*N[a+104>>2]);i=Q(-d);k=N[a+96>>2];l=Q(k-Q(N[a+220>>2]*Q(Q(g-h)-N[a+108>>2])));d=d>l?l:d;d=d<i?i:d;N[a+96>>2]=d;d=Q(d-k);g=Q(Q(o*d)+g);h=Q(h-Q(n*d));}w=N[a+172>>2];x=N[a+168>>2];i=Q(Q(Q(s+Q(g*N[a+144>>2]))-u)-Q(h*N[a+136>>2]));k=Q(Q(Q(t-Q(g*N[a+148>>2]))-v)+Q(h*N[a+140>>2]));a:{if(!(!K[a+112|0]|!e|f==Q(0))){N[c+24>>2]=i;N[c+20>>2]=k;N[c+28>>2]=g-h;e=a+184|0;Vb(c+8|0,e,c+20|0);j=N[c+16>>2];l=Q(-j);p=N[c+12>>2];f=Q(-p);q=N[c+8>>2];d=Q(-q);b:{c:{switch(J[a+224>>2]-1|0){case 2:N[a+84>>2]=N[a+84>>2]-q;N[a+88>>2]=N[a+88>>2]-p;N[a+92>>2]=N[a+92>>2]-j;break b;case 0:m=N[a+92>>2];j=Q(m-j);if(j<Q(0)){f=N[a+208>>2];N[c+12>>2]=Q(m*N[a+212>>2])-i;N[c+8>>2]=Q(m*f)-k;Ab(c,e,c+8|0);i=N[a+92>>2];f=N[c+4>>2];d=N[c>>2];J[a+92>>2]=0;N[a+84>>2]=d+N[a+84>>2];N[a+88>>2]=f+N[a+88>>2];l=Q(-i);break b}N[a+92>>2]=j;N[a+84>>2]=N[a+84>>2]-q;N[a+88>>2]=N[a+88>>2]-p;break b;case 1:break c;default:break b}}m=N[a+92>>2];j=Q(m-j);if(j>Q(0)){f=N[a+208>>2];N[c+12>>2]=Q(m*N[a+212>>2])-i;N[c+8>>2]=Q(m*f)-k;Ab(c,e,c+8|0);i=N[a+92>>2];f=N[c+4>>2];d=N[c>>2];J[a+92>>2]=0;N[a+84>>2]=d+N[a+84>>2];N[a+88>>2]=f+N[a+88>>2];l=Q(-i);break b}N[a+92>>2]=j;N[a+84>>2]=N[a+84>>2]-q;N[a+88>>2]=N[a+88>>2]-p;}g=Q(Q(o*Q(l+Q(Q(N[a+144>>2]*f)-Q(d*N[a+148>>2]))))+g);h=Q(h-Q(n*Q(l+Q(Q(N[a+136>>2]*f)-Q(d*N[a+140>>2])))));break a}N[c+12>>2]=-i;N[c+8>>2]=-k;Ab(c+20|0,a+184|0,c+8|0);d=N[c+20>>2];N[a+84>>2]=d+N[a+84>>2];f=N[c+24>>2];N[a+88>>2]=f+N[a+88>>2];g=Q(Q(o*Q(Q(f*N[a+144>>2])-Q(d*N[a+148>>2])))+g);h=Q(h-Q(n*Q(Q(f*N[a+136>>2])-Q(d*N[a+140>>2]))));}e=J[b+28>>2]+P(J[a+128>>2],12)|0;N[e+4>>2]=u-Q(x*f);N[e>>2]=v-Q(x*d);e=J[b+28>>2];N[(e+P(J[a+128>>2],12)|0)+8>>2]=h;e=e+P(J[a+132>>2],12)|0;N[e+4>>2]=s+Q(w*f);N[e>>2]=t+Q(w*d);N[(J[b+28>>2]+P(J[a+132>>2],12)|0)+8>>2]=g;Fa=c+32|0;}function Dd(a,b,c,d,e,f){var g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=0,q=Q(0),r=Q(0),s=0,t=Q(0);a:{if(!J[b+60>>2]){break a}b:{switch(J[b+56>>2]){case 0:J[a>>2]=1065353216;J[a+4>>2]=0;h=N[c+12>>2];g=N[b+48>>2];j=N[b+52>>2];k=N[c+8>>2];i=Q(N[c>>2]+Q(Q(h*g)-Q(j*k)));o=N[e+12>>2];m=N[b>>2];n=N[b+4>>2];q=N[e+8>>2];l=Q(N[e>>2]+Q(Q(o*m)-Q(n*q)));r=Q(i-l);j=Q(Q(Q(k*g)+Q(h*j))+N[c+4>>2]);k=Q(Q(Q(q*m)+Q(o*n))+N[e+4>>2]);h=Q(j-k);c:{if(!(Q(Q(r*r)+Q(h*h))>Q(14210854715202004e-30))){h=Q(1);g=Q(0);break c}g=Q(k-j);N[a+4>>2]=g;h=Q(l-i);N[a>>2]=h;o=Q(Y(Q(Q(h*h)+Q(g*g))));if(o<Q(1.1920928955078125e-7)){break c}o=Q(Q(1)/o);g=Q(g*o);N[a+4>>2]=g;h=Q(h*o);N[a>>2]=h;}j=Q(j+Q(g*d));k=Q(k-Q(g*f));N[a+12>>2]=Q(j+k)*Q(.5);d=Q(i+Q(h*d));f=Q(l-Q(h*f));N[a+8>>2]=Q(d+f)*Q(.5);N[a+24>>2]=Q(Q(f-d)*h)+Q(g*Q(k-j));return;case 1:g=N[c+8>>2];i=N[b+40>>2];l=N[c+12>>2];j=N[b+44>>2];h=Q(Q(g*i)+Q(l*j));N[a+4>>2]=h;g=Q(Q(l*i)-Q(j*g));N[a>>2]=g;if(J[b+60>>2]<=0){break a}i=N[c+8>>2];l=N[b+48>>2];j=N[c+12>>2];k=N[b+52>>2];o=Q(Q(Q(i*l)+Q(j*k))+N[c+4>>2]);l=Q(N[c>>2]+Q(Q(j*l)-Q(k*i)));j=Q(h*f);k=Q(g*f);while(1){c=(p<<3)+a|0;i=N[e+8>>2];s=P(p,20)+b|0;m=N[s>>2];n=N[e+12>>2];q=N[s+4>>2];f=Q(Q(Q(i*m)+Q(n*q))+N[e+4>>2]);r=Q(f-j);t=f;i=Q(N[e>>2]+Q(Q(n*m)-Q(q*i)));f=Q(d-Q(Q(Q(i-l)*g)+Q(Q(f-o)*h)));m=Q(t+Q(h*f));N[c+12>>2]=Q(r+m)*Q(.5);n=Q(i-k);f=Q(i+Q(g*f));N[c+8>>2]=Q(n+f)*Q(.5);N[((p<<2)+a|0)+24>>2]=Q(Q(n-f)*g)+Q(h*Q(r-m));p=p+1|0;if((p|0)<J[b+60>>2]){continue}break}break a;case 2:break b;default:break a}}g=N[e+8>>2];i=N[b+40>>2];l=N[e+12>>2];j=N[b+44>>2];h=Q(Q(g*i)+Q(l*j));N[a+4>>2]=h;g=Q(Q(l*i)-Q(j*g));N[a>>2]=g;if(J[b+60>>2]>0){i=N[e+8>>2];l=N[b+48>>2];j=N[e+12>>2];k=N[b+52>>2];o=Q(Q(Q(i*l)+Q(j*k))+N[e+4>>2]);l=Q(N[e>>2]+Q(Q(j*l)-Q(k*i)));j=Q(h*d);k=Q(g*d);while(1){e=(p<<3)+a|0;i=N[c+8>>2];s=P(p,20)+b|0;m=N[s>>2];n=N[c+12>>2];q=N[s+4>>2];d=Q(Q(Q(i*m)+Q(n*q))+N[c+4>>2]);r=Q(d-j);t=d;i=Q(N[c>>2]+Q(Q(n*m)-Q(q*i)));d=Q(f-Q(Q(Q(i-l)*g)+Q(Q(d-o)*h)));m=Q(t+Q(h*d));N[e+12>>2]=Q(r+m)*Q(.5);n=Q(i-k);d=Q(i+Q(g*d));N[e+8>>2]=Q(n+d)*Q(.5);N[((p<<2)+a|0)+24>>2]=Q(Q(n-d)*g)+Q(h*Q(r-m));p=p+1|0;if((p|0)<J[b+60>>2]){continue}break}}N[a+4>>2]=-h;N[a>>2]=-g;}}function tc(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0;e=Fa+-64|0;Fa=e;c=J[a+124>>2];J[e+56>>2]=J[a+120>>2];J[e+60>>2]=c;c=J[a+116>>2];J[e+48>>2]=J[a+112>>2];J[e+52>>2]=c;c=J[a+108>>2];J[e+40>>2]=J[a+104>>2];J[e+44>>2]=c;c=J[a+100>>2];J[e+32>>2]=J[a+96>>2];J[e+36>>2]=c;c=J[a+92>>2];J[e+24>>2]=J[a+88>>2];J[e+28>>2]=c;c=J[a+84>>2];J[e+16>>2]=J[a+80>>2];J[e+20>>2]=c;c=J[a+76>>2];J[e+8>>2]=J[a+72>>2];J[e+12>>2]=c;c=J[a+68>>2];J[e>>2]=J[a+64>>2];J[e+4>>2]=c;c=J[a+4>>2];J[a+4>>2]=c|4;n=c&2;h=J[a+52>>2];l=J[h+8>>2];g=l+12|0;j=J[a+48>>2];m=J[j+8>>2];c=m+12|0;o=K[h+38|0]|K[j+38|0];a:{if(o){l=J[j+12>>2];m=J[a+56>>2];h=J[h+12>>2];i=J[a+60>>2];d=Fa-128|0;Fa=d;J[d+88>>2]=0;J[d+80>>2]=0;J[d+84>>2]=0;J[d+60>>2]=0;J[d+52>>2]=0;J[d+56>>2]=0;j=d+36|0;Wb(j,l,m);Wb(d- -64|0,h,i);i=J[c+12>>2];J[d+100>>2]=J[c+8>>2];J[d+104>>2]=i;i=J[c+4>>2];J[d+92>>2]=J[c>>2];J[d+96>>2]=i;c=J[g+12>>2];J[d+116>>2]=J[g+8>>2];J[d+120>>2]=c;c=J[g+4>>2];J[d+108>>2]=J[g>>2];J[d+112>>2]=c;H[d+124|0]=1;I[d+28>>1]=0;Cd(d,d+24|0,j);Fa=d+128|0;f=N[d+16>>2]<Q(11920928955078125e-22);J[a+124>>2]=0;break a}d=a- -64|0;Ha[J[J[a>>2]>>2]](a,d,c,g);h=J[a+124>>2];b:{if((h|0)<=0){break b}g=J[e+60>>2];if((g|0)>0){while(1){j=d+P(k,20)|0;c=j;J[c+8>>2]=0;J[c+12>>2]=0;c=J[c+16>>2];f=0;c:{while(1){i=P(f,20)+e|0;if(J[i+16>>2]!=(c|0)){f=f+1|0;if((g|0)!=(f|0)){continue}break c}break}N[j+8>>2]=N[i+8>>2];N[j+12>>2]=N[i+12>>2];}k=k+1|0;if((h|0)!=(k|0)){continue}break}break b}if(h>>>0>=4){g=h&-4;while(1){c=d+P(f,20)|0;J[c+8>>2]=0;J[c+12>>2]=0;c=d+P(f|1,20)|0;J[c+8>>2]=0;J[c+12>>2]=0;c=d+P(f|2,20)|0;J[c+8>>2]=0;J[c+12>>2]=0;c=d+P(f|3,20)|0;J[c+8>>2]=0;J[c+12>>2]=0;f=f+4|0;k=k+4|0;if((g|0)!=(k|0)){continue}break}}g=h&3;if(!g){break b}while(1){c=d+P(f,20)|0;J[c+8>>2]=0;J[c+12>>2]=0;f=f+1|0;i=i+1|0;if((g|0)!=(i|0)){continue}break}}f=(h|0)>0;if((f|0)==(n>>>1|0)){break a}J[m+144>>2]=0;I[m+4>>1]=L[m+4>>1]|2;J[l+144>>2]=0;I[l+4>>1]=L[l+4>>1]|2;}k=2;J[a+4>>2]=J[a+4>>2]&-3|(f?2:0);c=(b|0)!=0&f;d:{if(!(c&!n)){if(!n){break d}k=3;if(!b|f){break d}}Ha[J[J[b>>2]+(k<<2)>>2]](b,a);}if(!(!c|(o|0)!=0)){Ha[J[J[b>>2]+16>>2]](b,a,e);}Fa=e- -64|0;}function Nc(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0,m=0,n=0,o=0,p=0,q=0,r=0;c=b;b=J[b>>2]+7&-8;J[c>>2]=b+16;o=a;i=J[b>>2];d=J[b+4>>2];a=J[b+12>>2];m=a;g=Fa-32|0;Fa=g;a=a&2147483647;h=a;e=a-1006698496|0;a=a-1140785152|0;c=J[b+8>>2];b=c;a:{if((e|0)==(a|0)&b>>>0<b>>>0|a>>>0>e>>>0){a=c;c=m<<4|a>>>28;b=a<<4|d>>>28;a=c;d=d&268435455;if((d|0)==134217728&(i|0)!=0|d>>>0>134217728){a=a+1073741824|0;b=b+1|0;a=b?a:a+1|0;break a}a=a+1073741824|0;if(i|(d|0)!=134217728){break a}d=b&1;b=d+b|0;a=b>>>0<d>>>0?a+1|0:a;break a}if(!(!b&(h|0)==2147418112?!(d|i):h>>>0<2147418112)){a=c;c=m<<4|a>>>28;b=a<<4|d>>>28;a=c&524287|2146959360;break a}b=0;a=2146435072;if(h>>>0>1140785151){break a}a=0;n=h>>>16|0;if(n>>>0<15249){break a}b=i;a=d;e=m&65535|65536;h=e;l=c;f=c;j=n-15233|0;b:{if(j&64){c=b;e=j+-64|0;b=e&31;if((e&63)>>>0>=32){a=c<<b;f=0;}else {a=(1<<b)-1&c>>>32-b|a<<b;f=c<<b;}e=a;b=0;a=0;break b}if(!j){break b}k=f;f=j&31;if((j&63)>>>0>=32){c=k<<f;f=0;}else {c=(1<<f)-1&k>>>32-f|e<<f;f=k<<f;}e=c;p=f;k=b;c=64-j|0;f=c&31;if((c&63)>>>0>=32){c=0;b=a>>>f|0;}else {c=a>>>f|0;b=((1<<f)-1&a)<<32-f|k>>>f;}f=p|b;e=c|e;b=j&31;if((j&63)>>>0>=32){c=k<<b;b=0;}else {c=(1<<b)-1&k>>>32-b|a<<b;b=k<<b;}a=c;}J[g+16>>2]=b;J[g+20>>2]=a;J[g+24>>2]=f;J[g+28>>2]=e;b=15361-n|0;c:{if(b&64){d=l;b=b+-64|0;a=b&31;if((b&63)>>>0>=32){c=0;i=h>>>a|0;}else {c=h>>>a|0;i=((1<<a)-1&h)<<32-a|d>>>a;}d=c;l=0;h=0;break c}if(!b){break c}e=l;a=64-b|0;c=a&31;if((a&63)>>>0>=32){a=e<<c;f=0;}else {a=(1<<c)-1&e>>>32-c|h<<c;f=e<<c;}e=i;i=b&31;if((b&63)>>>0>=32){c=0;e=d>>>i|0;}else {c=d>>>i|0;e=((1<<i)-1&d)<<32-i|e>>>i;}i=f|e;d=a|c;e=l;c=b&31;if((b&63)>>>0>=32){a=0;l=h>>>c|0;}else {a=h>>>c|0;l=((1<<c)-1&h)<<32-c|e>>>c;}h=a;}J[g>>2]=i;J[g+4>>2]=d;J[g+8>>2]=l;J[g+12>>2]=h;b=J[g+8>>2];a=J[g+12>>2]<<4|b>>>28;b=b<<4;c=J[g>>2];h=J[g+4>>2];b=h>>>28|b;d=h&268435455;c=c|(J[g+16>>2]|J[g+24>>2]|(J[g+20>>2]|J[g+28>>2]))!=0;if((d|0)==134217728&(c|0)!=0|d>>>0>134217728){b=b+1|0;a=b?a:a+1|0;break a}if(c|(d|0)!=134217728){break a}c=b;b=b+(b&1)|0;a=c>>>0>b>>>0?a+1|0:a;}Fa=g+32|0;x(0,b|0);x(1,m&-2147483648|a);q=o,r=+z(),O[q>>3]=r;}function zd(a,b){var c=0,d=Q(0),e=Q(0),f=0,g=0,h=Q(0),i=0,j=Q(0),k=0,l=0,m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=0,y=Q(0);J[a+24>>2]=J[a+24>>2]+1;c=J[a>>2];if((c|0)==-1){J[a>>2]=b;J[(J[a+4>>2]+P(b,40)|0)+20>>2]=-1;return}i=J[a+4>>2];g=i+P(b,40)|0;n=N[g+12>>2];o=N[g+8>>2];p=N[g+4>>2];q=N[g>>2];while(1){k=c;c=i+P(c,40)|0;g=J[c+24>>2];if((g|0)!=-1){m=N[c+8>>2];j=N[c>>2];h=N[c+12>>2];d=N[c+4>>2];e=Q(Q((o<m?m:o)-(j<q?j:q))+Q((h>n?h:n)-(d<p?d:p)));r=Q(e+e);e=Q(Q(m-j)+Q(h-d));e=Q(r-Q(e+e));e=Q(e+e);f=i+P(g,40)|0;s=N[f+8>>2];t=o>s?o:s;u=N[f+4>>2];v=p<u?p:u;m=N[f>>2];j=q<m?q:m;h=N[f+12>>2];d=h<n?n:h;c=J[c+28>>2];a:{if(J[f+24>>2]==-1){d=Q(Q(t-j)+Q(d-v));h=Q(d+d);break a}d=Q(Q(t-j)+Q(d-v));w=Q(d+d);d=Q(Q(s-m)+Q(h-u));h=Q(w-Q(d+d));}y=Q(r+r);f=i+P(c,40)|0;r=N[f+8>>2];s=o>r?o:r;t=N[f+4>>2];u=p<t?p:t;v=N[f>>2];m=q<v?q:v;j=N[f+12>>2];d=j<n?n:j;h=Q(e+h);b:{if(J[f+24>>2]==-1){d=Q(Q(s-m)+Q(d-u));w=Q(d+d);break b}d=Q(Q(s-m)+Q(d-u));w=Q(d+d);d=Q(Q(r-v)+Q(j-t));w=Q(w-Q(d+d));}e=Q(e+w);c=h<e?g:c;if(!(h>y)|!(e>y)){continue}}break}f=P(k,40);i=J[(f+i|0)+20>>2];x=Ad(a);g=P(x,40);J[(g+J[a+4>>2]|0)+20>>2]=i;c=J[a+4>>2];l=c+g|0;J[l+16>>2]=0;c=c+f|0;d=N[c>>2];e=N[c+4>>2];N[l+4>>2]=e>p?p:e;N[l>>2]=d>q?q:d;d=N[c+8>>2];e=N[c+12>>2];N[l+12>>2]=e<n?n:e;N[l+8>>2]=d<o?o:d;c=J[a+4>>2];l=c+g|0;g=c+f|0;J[l+32>>2]=J[g+32>>2]+1;c:{if((i|0)!=-1){c=c+P(i,40)|0;J[((k|0)==J[c+24>>2]?c+24|0:c+28|0)>>2]=x;J[l+28>>2]=b;J[l+24>>2]=k;J[g+20>>2]=x;c=(J[a+4>>2]+P(b,40)|0)+20|0;break c}J[l+28>>2]=b;J[l+24>>2]=k;J[g+20>>2]=x;J[(J[a+4>>2]+P(b,40)|0)+20>>2]=x;c=a;}J[c>>2]=x;c=J[(J[a+4>>2]+P(b,40)|0)+20>>2];if((c|0)!=-1){while(1){b=yd(a,c);k=J[a+4>>2];c=P(b,40);i=k+c|0;f=P(J[i+24>>2],40)+k|0;g=J[f+32>>2];k=k+P(J[i+28>>2],40)|0;b=J[k+32>>2];J[i+32>>2]=((b|0)<(g|0)?g:b)+1;j=N[k>>2];h=N[f>>2];d=N[f+4>>2];e=N[k+4>>2];N[i+4>>2]=d<e?d:e;N[i>>2]=h<j?h:j;j=N[f+8>>2];h=N[k+8>>2];d=N[f+12>>2];e=N[k+12>>2];N[i+12>>2]=d>e?d:e;N[i+8>>2]=h<j?j:h;c=J[(c+J[a+4>>2]|0)+20>>2];if((c|0)!=-1){continue}break}}}function Hh(a,b){a=a|0;b=b|0;var c=0,d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0),i=0,j=0,k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=0,r=0,s=Q(0),t=Q(0),u=Q(0),v=0,w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0),B=Q(0),C=Q(0),D=Q(0),E=Q(0);c=J[a+48>>2];v=J[c+8>>2];J[a+108>>2]=v;j=J[a+52>>2];q=J[j+8>>2];J[a+112>>2]=q;o=N[c+32>>2];i=J[c+32>>2];p=N[c+28>>2];r=J[c+28>>2];J[a+140>>2]=r;J[a+144>>2]=i;E=N[j+32>>2];i=J[j+32>>2];k=N[j+28>>2];J[a+148>>2]=J[j+28>>2];J[a+152>>2]=i;t=N[c+120>>2];N[a+156>>2]=t;u=N[j+120>>2];N[a+160>>2]=u;w=N[c+128>>2];N[a+164>>2]=w;x=N[j+128>>2];N[a+168>>2]=x;l=N[a+84>>2];j=J[b+24>>2];c=P(v,12);r=j+c|0;e=N[r+8>>2];f=Ta(e);d=N[a+80>>2];e=Ua(e);i=J[b+28>>2];c=c+i|0;y=N[c>>2];z=N[c+4>>2];A=N[c+8>>2];q=P(q,12);c=q+i|0;B=N[c>>2];C=N[c+4>>2];D=N[c+8>>2];g=N[r>>2];c=j+q|0;h=N[c>>2];s=N[r+4>>2];m=N[c+4>>2];n=N[c+8>>2];d=Q(d-p);l=Q(l-o);p=Q(Q(e*d)-Q(f*l));N[a+124>>2]=p;l=Q(Q(f*d)+Q(e*l));N[a+128>>2]=l;e=N[a+92>>2];f=Ta(n);d=N[a+88>>2];o=Ua(n);k=Q(d-k);e=Q(e-E);n=Q(Q(o*k)-Q(f*e));N[a+132>>2]=n;k=Q(Q(f*k)+Q(o*e));N[a+136>>2]=k;d=Q(0);f=Q(0);e=Q(0);g=Q(Q(Q(h+n)-g)-p);h=Q(Q(Q(m+k)-s)-l);s=Q(Y(Q(Q(g*g)+Q(h*h))));if(s>Q(.004999999888241291)){f=Q(Q(1)/s);e=Q(h*f);f=Q(g*f);}N[a+120>>2]=e;N[a+116>>2]=f;g=Q(Q(n*e)-Q(f*k));h=Q(Q(x*g)*g);g=Q(Q(p*e)-Q(f*l));h=Q(h+Q(u+Q(Q(Q(w*g)*g)+t)));g=h!=Q(0)?Q(Q(1)/h):Q(0);N[a+172>>2]=g;m=N[a+68>>2];if(m>Q(0)){o=h;h=N[b>>2];d=Q(m*Q(6.2831854820251465));m=Q(d*Q(d*g));d=Q(h*Q(Q(h*m)+Q(d*Q(Q(g+g)*N[a+72>>2]))));d=d!=Q(0)?Q(Q(1)/d):Q(0);g=Q(o+d);N[a+172>>2]=g!=Q(0)?Q(Q(1)/g):Q(0);h=Q(Q(m*Q(h*Q(s-N[a+104>>2])))*d);}else {h=Q(0);}N[a+76>>2]=h;N[a+96>>2]=d;a:{if(K[b+20|0]){d=Q(N[b+8>>2]*N[a+100>>2]);N[a+100>>2]=d;e=Q(e*d);f=Q(f*d);D=Q(Q(x*Q(Q(n*e)+Q(f*Q(-k))))+D);A=Q(A-Q(w*Q(Q(p*e)+Q(f*Q(-l)))));C=Q(C+Q(u*e));B=Q(B+Q(u*f));z=Q(z-Q(t*e));y=Q(y-Q(t*f));break a}J[a+100>>2]=0;}i=J[b+28>>2]+P(v,12)|0;N[i+4>>2]=z;N[i>>2]=y;i=J[b+28>>2];N[(i+P(J[a+108>>2],12)|0)+8>>2]=A;i=i+P(J[a+112>>2],12)|0;N[i+4>>2]=C;N[i>>2]=B;N[(J[b+28>>2]+P(J[a+112>>2],12)|0)+8>>2]=D;}function sg(a,b){a=a|0;b=b|0;var c=Q(0),d=0,e=Q(0),f=Q(0),g=Q(0),h=0,i=Q(0),j=Q(0),k=0,l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=0,r=0,s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=0,x=Q(0),y=Q(0),z=Q(0),A=Q(0),B=Q(0),C=Q(0),D=Q(0),E=Q(0);d=J[a+48>>2];w=J[d+8>>2];J[a+120>>2]=w;k=J[a+52>>2];q=J[k+8>>2];J[a+124>>2]=q;o=N[d+32>>2];h=J[d+32>>2];c=N[d+28>>2];r=J[d+28>>2];J[a+160>>2]=r;J[a+164>>2]=h;s=N[k+32>>2];h=J[k+32>>2];j=N[k+28>>2];J[a+168>>2]=J[k+28>>2];J[a+172>>2]=h;t=N[d+120>>2];N[a+176>>2]=t;u=N[k+120>>2];N[a+180>>2]=u;x=N[d+128>>2];N[a+184>>2]=x;y=N[k+128>>2];N[a+188>>2]=y;p=N[a+96>>2];k=J[b+24>>2];d=P(w,12);r=k+d|0;e=N[r+8>>2];g=Ta(e);l=N[a+92>>2];e=Ua(e);h=J[b+28>>2];d=d+h|0;z=N[d>>2];A=N[d+4>>2];B=N[d+8>>2];q=P(q,12);d=q+h|0;C=N[d>>2];D=N[d+4>>2];E=N[d+8>>2];d=k+q|0;m=N[d>>2];f=N[d+4>>2];n=N[r>>2];v=N[r+4>>2];i=N[d+8>>2];c=Q(l-c);l=Q(p-o);p=Q(Q(e*c)-Q(g*l));N[a+144>>2]=p;l=Q(Q(g*c)+Q(e*l));N[a+148>>2]=l;e=N[a+104>>2];g=Ta(i);c=N[a+100>>2];i=Ua(i);c=Q(c-j);e=Q(e-s);j=Q(Q(i*c)-Q(g*e));N[a+152>>2]=j;o=Q(Q(g*c)+Q(i*e));N[a+156>>2]=o;m=Q(Q(m+j)-N[a+76>>2]);f=Q(Q(f+o)-N[a+80>>2]);s=Q(Y(Q(Q(m*m)+Q(f*f))));g=Q(0);e=Q(0);i=Q(0);c=Q(Q(n+p)-N[a+68>>2]);n=Q(Q(v+l)-N[a+72>>2]);v=Q(Y(Q(Q(c*c)+Q(n*n))));if(v>Q(.04999999701976776)){e=Q(Q(1)/v);i=Q(n*e);e=Q(c*e);}N[a+132>>2]=i;N[a+128>>2]=e;c=Q(0);if(s>Q(.04999999701976776)){g=Q(Q(1)/s);c=Q(f*g);g=Q(m*g);}N[a+140>>2]=c;N[a+136>>2]=g;m=N[a+112>>2];f=Q(Q(j*c)-Q(g*o));n=Q(Q(m*m)*Q(Q(Q(y*f)*f)+u));f=Q(Q(p*i)-Q(e*l));f=Q(n+Q(Q(Q(x*f)*f)+t));N[a+192>>2]=f>Q(0)?Q(Q(1)/f):f;a:{if(K[b+20|0]){f=Q(N[b+8>>2]*N[a+116>>2]);N[a+116>>2]=f;n=j;j=c;c=Q(f*Q(-m));j=Q(j*c);g=Q(g*c);E=Q(Q(y*Q(Q(n*j)+Q(g*Q(-o))))+E);c=i;i=Q(-f);c=Q(c*i);e=Q(e*i);B=Q(Q(x*Q(Q(p*c)+Q(e*Q(-l))))+B);D=Q(D+Q(u*j));C=Q(C+Q(u*g));A=Q(A+Q(t*c));z=Q(z+Q(t*e));break a}J[a+116>>2]=0;}h=J[b+28>>2]+P(w,12)|0;N[h+4>>2]=A;N[h>>2]=z;h=J[b+28>>2];N[(h+P(J[a+120>>2],12)|0)+8>>2]=B;h=h+P(J[a+124>>2],12)|0;N[h+4>>2]=D;N[h>>2]=C;N[(J[b+28>>2]+P(J[a+124>>2],12)|0)+8>>2]=E;}function Sf(a,b){a=a|0;b=b|0;var c=0,d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=0,i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=0,p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0),B=Q(0),C=Q(0);c=Fa-80|0;Fa=c;h=J[b+24>>2];o=h+P(J[a+116>>2],12)|0;w=N[o>>2];h=h+P(J[a+120>>2],12)|0;x=N[h>>2];y=N[o+4>>2];z=N[h+4>>2];q=N[o+8>>2];j=N[a+144>>2];l=N[a+84>>2];r=N[h+8>>2];i=N[a+152>>2];s=N[a+92>>2];u=N[a+160>>2];v=N[a+156>>2];m=N[a+140>>2];p=N[a+80>>2];n=N[a+148>>2];t=N[a+88>>2];k=N[a+164>>2];e=N[a+168>>2];A=Q(k+e);N[c+76>>2]=A;g=Ta(r);f=Ua(r);d=Ta(q);B=Ua(q);m=Q(p-m);p=Q(l-j);l=Q(Q(B*m)-Q(d*p));n=Q(t-n);t=Q(s-i);j=Q(Q(f*n)-Q(g*t));i=Q(Q(k*l)+Q(e*j));N[c+72>>2]=i;N[c- -64>>2]=i;C=Q(v+u);N[c+60>>2]=Q(e*Q(j*j))+Q(Q(k*Q(l*l))+C);i=Q(Q(d*m)+Q(B*p));s=Q(-i);d=Q(Q(g*n)+Q(f*t));g=Q(Q(k*s)-Q(e*d));N[c+68>>2]=g;N[c+52>>2]=g;g=Q(Q(k*Q(l*s))-Q(e*Q(j*d)));N[c+56>>2]=g;N[c+44>>2]=Q(e*Q(d*d))+Q(Q(k*Q(i*i))+C);N[c+48>>2]=g;g=Q(Q(Q(z+d)-y)-i);f=Q(Q(Q(x+j)-w)-l);a:{if(N[a+68>>2]>Q(0)){N[c+24>>2]=f;N[c+28>>2]=g;Ab(c+12|0,c+44|0,c+24|0);n=e;m=d;d=N[c+12>>2];e=N[c+16>>2];j=Q(Q(n*Q(Q(m*d)-Q(j*e)))+r);k=Q(q-Q(k*Q(Q(i*d)-Q(l*e))));i=Q(Y(Q(Q(f*f)+Q(g*g))));e=Q(-e);f=Q(-d);o=1;break a}m=Q(-d);N[c+40>>2]=g;N[c+36>>2]=f;d=N[a+96>>2];N[c+28>>2]=g;N[c+24>>2]=f;d=Q(Q(r-q)-d);N[c+32>>2]=d;p=d>Q(0)?d:Q(-d);i=Q(Y(Q(Q(f*f)+Q(g*g))));n=e;b:{if(A>Q(0)){Vb(c+12|0,c+44|0,c+24|0);d=Q(-N[c+20>>2]);f=Q(-N[c+12>>2]);e=Q(-N[c+16>>2]);break b}Ab(c+12|0,c+44|0,c+36|0);f=Q(-N[c+12>>2]);d=Q(0);e=Q(-N[c+16>>2]);}j=Q(Q(n*Q(Q(Q(j*e)+Q(f*m))+d))+r);k=Q(q-Q(k*Q(Q(Q(l*e)+Q(f*s))+d)));o=p<=Q(.03490658849477768);}h=J[b+24>>2]+P(J[a+116>>2],12)|0;N[h+4>>2]=y-Q(v*e);N[h>>2]=w-Q(v*f);h=J[b+24>>2];N[(h+P(J[a+116>>2],12)|0)+8>>2]=k;h=h+P(J[a+120>>2],12)|0;N[h+4>>2]=z+Q(u*e);N[h>>2]=x+Q(u*f);N[(J[b+24>>2]+P(J[a+120>>2],12)|0)+8>>2]=j;Fa=c+80|0;return o&i<=Q(.004999999888241291)}function kg(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=0,f=Q(0),g=0,h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0),B=Q(0);e=J[b+24>>2];g=e+P(J[a+132>>2],12)|0;m=N[g+8>>2];v=N[g+4>>2];w=N[g>>2];g=e+P(J[a+128>>2],12)|0;n=N[g+8>>2];l=N[g+4>>2];x=N[g>>2];p=N[a+180>>2];o=N[a+176>>2];a:{if(!K[a+112|0]){break a}e=J[a+224>>2];if(!e|Q(o+p)==Q(0)){break a}c=Q(Q(m-n)-N[a+116>>2]);b:{c:{switch(e-1|0){case 2:c=Q(c-N[a+120>>2]);c=c<Q(.13962635397911072)?c:Q(.13962635397911072);c=c<Q(-.13962635397911072)?Q(-.13962635397911072):c;s=c>Q(0)?c:Q(-c);d=Q(c*Q(-N[a+220>>2]));break b;case 0:f=Q(c-N[a+120>>2]);c=Q(f+Q(.03490658849477768));c=c<Q(0)?c:Q(0);d=Q((c<Q(-.13962635397911072)?Q(-.13962635397911072):c)*Q(-N[a+220>>2]));s=Q(-f);break b;case 1:break c;default:break b}}s=Q(c-N[a+124>>2]);c=Q(s+Q(-.03490658849477768));c=c<Q(.13962635397911072)?c:Q(.13962635397911072);d=Q((c<Q(0)?Q(0):c)*Q(-N[a+220>>2]));}m=Q(Q(p*d)+m);n=Q(n-Q(o*d));}h=N[a+164>>2];i=N[a+80>>2];y=Ta(m);j=N[a+160>>2];d=N[a+76>>2];z=Ua(m);f=N[a+156>>2];c=N[a+72>>2];k=Q(d-j);h=Q(i-h);q=Q(Q(y*k)+Q(z*h));i=Ta(n);j=Q(N[a+68>>2]-N[a+152>>2]);d=Ua(n);c=Q(c-f);r=Q(Q(i*j)+Q(d*c));t=N[a+168>>2];u=N[a+172>>2];f=Q(t+u);A=Q(Q(Q(p*q)*q)+Q(Q(Q(o*r)*r)+f));B=l;l=Q(Q(Q(v+q)-l)-r);h=Q(Q(z*k)-Q(h*y));k=Q(Q(d*j)-Q(c*i));i=Q(Q(Q(w+h)-x)-k);c=Q(p*h);j=Q(Q(Q(k*Q(-o))*r)-Q(q*c));f=Q(Q(c*h)+Q(Q(Q(o*k)*k)+f));c=Q(Q(A*f)-Q(j*j));c=Q(-(c!=Q(0)?Q(Q(1)/c):c));d=Q(Q(Q(A*l)-Q(i*j))*c);N[g+4>>2]=B-Q(t*d);c=Q(Q(Q(f*i)-Q(l*j))*c);N[g>>2]=x-Q(t*c);e=J[b+24>>2];N[(e+P(J[a+128>>2],12)|0)+8>>2]=n-Q(o*Q(Q(k*d)-Q(c*r)));e=e+P(J[a+132>>2],12)|0;N[e+4>>2]=v+Q(u*d);N[e>>2]=w+Q(u*c);N[(J[b+24>>2]+P(J[a+132>>2],12)|0)+8>>2]=Q(p*Q(Q(h*d)-Q(c*q)))+m;return Q(Y(Q(Q(i*i)+Q(l*l))))<=Q(.004999999888241291)&s<=Q(.03490658849477768)}function nh(a,b){a=a|0;b=b|0;var c=0,d=Q(0),e=0,f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=0,k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=0,q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=0,w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0),B=Q(0),C=Q(0),D=Q(0),E=0,F=Q(0);e=J[a+48>>2];v=J[e+8>>2];J[a+104>>2]=v;j=J[a+52>>2];p=J[j+8>>2];J[a+108>>2]=p;D=N[e+32>>2];c=J[e+32>>2];f=N[e+28>>2];J[a+128>>2]=J[e+28>>2];J[a+132>>2]=c;k=N[j+32>>2];E=J[j+32>>2];i=N[j+28>>2];c=J[j+28>>2];J[a+136>>2]=c;J[a+140>>2]=E;r=N[e+120>>2];N[a+156>>2]=r;s=N[j+120>>2];N[a+160>>2]=s;d=N[e+128>>2];N[a+164>>2]=d;q=N[j+128>>2];N[a+168>>2]=q;e=J[b+28>>2];j=P(p,12);c=e+j|0;w=N[c+8>>2];x=N[c+4>>2];y=N[c>>2];p=P(v,12);c=e+p|0;z=N[c+8>>2];A=N[c+4>>2];B=N[c>>2];e=J[b+24>>2];c=e+p|0;o=N[c>>2];e=e+j|0;t=N[e>>2];u=N[c+4>>2];F=N[e+4>>2];g=N[c+8>>2];n=N[e+8>>2];l=Q(d+q);N[a+188>>2]=l>Q(0)?Q(Q(1)/l):l;h=Ua(n);m=Ta(n);l=Q(Q(m*Q(-i))-Q(h*k));N[a+124>>2]=l;m=Q(Q(m*k)-Q(h*i));N[a+120>>2]=m;i=N[a+72>>2];h=Ua(g);k=N[a+68>>2];C=Ta(g);f=Q(k-f);k=Q(i-D);i=Q(Q(C*f)+Q(h*k));N[a+116>>2]=i;h=Q(Q(h*f)-Q(k*C));N[a+112>>2]=h;N[a+148>>2]=Q(Q(F+l)-u)-i;N[a+144>>2]=Q(Q(t+m)-o)-h;f=Q(r+s);o=Q(Q(Q(q*l)*l)+Q(Q(Q(d*i)*i)+f));k=o;o=Q(q*m);t=Q(Q(o*m)+Q(Q(Q(d*h)*h)+f));u=Q(-d);d=Q(Q(Q(h*u)*i)-Q(l*o));f=Q(Q(k*t)-Q(d*d));f=f!=Q(0)?Q(Q(1)/f):f;N[a+184>>2]=k*f;N[a+172>>2]=t*f;d=Q(d*Q(-f));N[a+180>>2]=d;N[a+176>>2]=d;N[a+152>>2]=Q(n-g)-N[a+76>>2];a:{if(K[b+20|0]){g=N[b+8>>2];d=Q(g*N[a+80>>2]);N[a+80>>2]=d;n=Q(g*N[a+88>>2]);N[a+88>>2]=n;g=Q(g*N[a+84>>2]);N[a+84>>2]=g;w=Q(Q(q*Q(Q(Q(m*g)-Q(d*l))+n))+w);z=Q(Q(u*Q(Q(Q(h*g)-Q(d*i))+n))+z);y=Q(y+Q(s*d));B=Q(B-Q(r*d));x=Q(x+Q(s*g));A=Q(A-Q(r*g));break a}J[a+88>>2]=0;J[a+80>>2]=0;J[a+84>>2]=0;}c=J[b+28>>2]+P(v,12)|0;N[c+4>>2]=A;N[c>>2]=B;c=J[b+28>>2];N[(c+P(J[a+104>>2],12)|0)+8>>2]=z;c=c+P(J[a+108>>2],12)|0;N[c+4>>2]=x;N[c>>2]=y;N[(J[b+28>>2]+P(J[a+108>>2],12)|0)+8>>2]=w;}function Zf(a,b){a=a|0;b=b|0;var c=Q(0),d=0,e=Q(0),f=0,g=Q(0),h=Q(0),i=0,j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=0,p=0,q=Q(0),r=Q(0),s=0,t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0),B=Q(0),C=Q(0),D=Q(0),E=Q(0);d=J[a+48>>2];s=J[d+8>>2];J[a+96>>2]=s;i=J[a+52>>2];o=J[i+8>>2];J[a+100>>2]=o;k=N[d+32>>2];f=J[d+32>>2];m=N[d+28>>2];p=J[d+28>>2];J[a+128>>2]=p;J[a+132>>2]=f;A=N[i+32>>2];f=J[i+32>>2];h=N[i+28>>2];J[a+136>>2]=J[i+28>>2];J[a+140>>2]=f;q=N[d+120>>2];N[a+144>>2]=q;r=N[i+120>>2];N[a+148>>2]=r;t=N[d+128>>2];N[a+152>>2]=t;u=N[i+128>>2];N[a+156>>2]=u;e=N[a+72>>2];i=J[b+24>>2];d=P(s,12);p=i+d|0;n=N[p+8>>2];l=Ta(n);c=N[a+68>>2];g=Ua(n);f=J[b+28>>2];d=d+f|0;n=N[d>>2];v=N[d+4>>2];w=N[d+8>>2];o=P(o,12);d=o+f|0;x=N[d>>2];y=N[d+4>>2];z=N[d+8>>2];B=N[p>>2];d=i+o|0;C=N[d>>2];D=N[p+4>>2];E=N[d+4>>2];j=N[d+8>>2];c=Q(c-m);e=Q(e-k);m=Q(Q(g*c)-Q(l*e));N[a+112>>2]=m;l=Q(Q(l*c)+Q(g*e));N[a+116>>2]=l;g=N[a+80>>2];e=Ta(j);c=N[a+76>>2];j=Ua(j);h=Q(c-h);c=Q(g-A);g=Q(Q(j*h)-Q(e*c));N[a+120>>2]=g;j=Q(Q(e*h)+Q(j*c));N[a+124>>2]=j;h=Q(Q(Q(C+g)-B)-m);c=Q(Q(Q(E+j)-D)-l);e=Q(Y(Q(Q(h*h)+Q(c*c))));N[a+88>>2]=e;J[a+164>>2]=(Q(e-N[a+84>>2])>Q(0))<<1;if(e>Q(.004999999888241291)){k=c;c=Q(Q(1)/e);e=Q(k*c);N[a+108>>2]=e;h=Q(h*c);N[a+104>>2]=h;c=Q(Q(g*e)-Q(h*j));k=Q(Q(u*c)*c);c=Q(Q(m*e)-Q(h*l));c=Q(k+Q(r+Q(Q(Q(t*c)*c)+q)));N[a+160>>2]=c!=Q(0)?Q(Q(1)/c):Q(0);a:{if(K[b+20|0]){c=Q(N[b+8>>2]*N[a+92>>2]);N[a+92>>2]=c;k=g;g=Q(e*c);e=Q(h*c);z=Q(Q(u*Q(Q(k*g)+Q(e*Q(-j))))+z);w=Q(w-Q(t*Q(Q(m*g)+Q(e*Q(-l)))));y=Q(y+Q(r*g));x=Q(x+Q(r*e));v=Q(v-Q(q*g));n=Q(n-Q(q*e));break a}J[a+92>>2]=0;}f=J[b+28>>2]+P(s,12)|0;N[f+4>>2]=v;N[f>>2]=n;f=J[b+28>>2];N[(f+P(J[a+96>>2],12)|0)+8>>2]=w;f=f+P(J[a+100>>2],12)|0;N[f+4>>2]=y;N[f>>2]=x;N[(J[b+28>>2]+P(J[a+100>>2],12)|0)+8>>2]=z;return}J[a+160>>2]=0;J[a+104>>2]=0;J[a+108>>2]=0;J[a+92>>2]=0;}function Ng(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=Q(0),f=Q(0),g=0,h=Q(0),i=Q(0),j=0,k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=0,p=0,q=Q(0),r=0,s=0,t=0;g=J[J[a+48>>2]+12>>2];p=J[J[a+52>>2]+12>>2];J[b+60>>2]=0;e=N[c+12>>2];f=N[d+8>>2];h=N[p+12>>2];l=N[d+12>>2];m=N[p+16>>2];n=Q(Q(Q(Q(f*h)+Q(l*m))+N[d+4>>2])-N[c+4>>2]);i=N[c+8>>2];f=Q(Q(N[d>>2]+Q(Q(l*h)-Q(m*f)))-N[c>>2]);q=Q(Q(e*n)-Q(i*f));n=Q(Q(e*f)+Q(n*i));d=g+84|0;o=g+20|0;e=Q(N[g+8>>2]+N[p+8>>2]);a=1;c=0;g=J[g+148>>2];a:{if((g|0)>0){f=Q(-34028234663852886e22);a=0;while(1){j=a<<3;r=j+d|0;j=j+o|0;h=Q(Q(N[r>>2]*Q(n-N[j>>2]))+Q(Q(q-N[j+4>>2])*N[r+4>>2]));if(h>e){break a}j=f<h;f=j?h:f;c=j?a:c;a=a+1|0;if((g|0)!=(a|0)){continue}break}a=f<Q(1.1920928955078125e-7);}j=c+1|0;g=o+(((g|0)>(j|0)?j:0)<<3)|0;j=J[g+4>>2];f=N[g+4>>2];r=J[g>>2];h=N[g>>2];g=o;o=c<<3;g=g+o|0;s=J[g+4>>2];l=N[g+4>>2];t=J[g>>2];m=N[g>>2];b:{if(a){J[b+56>>2]=1;J[b+60>>2]=1;a=d+o|0;c=J[a>>2];a=J[a+4>>2];N[b+52>>2]=Q(l+f)*Q(.5);N[b+48>>2]=Q(m+h)*Q(.5);J[b+40>>2]=c;J[b+44>>2]=a;break b}i=Q(n-m);k=Q(q-l);if(Q(Q(i*Q(h-m))+Q(k*Q(f-l)))<=Q(0)){f=Q(e*e);e=Q(Q(i*i)+Q(k*k));if(f<e){break a}J[b+56>>2]=1;J[b+60>>2]=1;N[b+40>>2]=i;N[b+44>>2]=k;e=Q(Y(e));if(!(e<Q(1.1920928955078125e-7))){e=Q(Q(1)/e);N[b+44>>2]=k*e;N[b+40>>2]=i*e;}J[b+48>>2]=t;J[b+52>>2]=s;break b}i=Q(n-h);k=Q(q-f);if(Q(Q(i*Q(m-h))+Q(k*Q(l-f)))<=Q(0)){f=Q(e*e);e=Q(Q(i*i)+Q(k*k));if(f<e){break a}J[b+56>>2]=1;J[b+60>>2]=1;N[b+40>>2]=i;N[b+44>>2]=k;e=Q(Y(e));if(!(e<Q(1.1920928955078125e-7))){e=Q(Q(1)/e);N[b+44>>2]=k*e;N[b+40>>2]=i*e;}J[b+48>>2]=r;J[b+52>>2]=j;break b}h=Q(Q(m+h)*Q(.5));a=d+(c<<3)|0;f=Q(Q(l+f)*Q(.5));if(e<Q(Q(Q(n-h)*N[a>>2])+Q(Q(q-f)*N[a+4>>2]))){break a}J[b+56>>2]=1;J[b+60>>2]=1;c=J[a+4>>2];a=J[a>>2];N[b+52>>2]=f;N[b+48>>2]=h;J[b+40>>2]=a;J[b+44>>2]=c;}a=J[p+16>>2];c=J[p+12>>2];J[b+16>>2]=0;J[b>>2]=c;J[b+4>>2]=a;}}function md(a,b){var c=0,d=Q(0),e=0,f=0;c=Fa-464|0;Fa=c;Sa(8296,0);O[c+448>>3]=N[a+16>>2];Sa(7384,c+448|0);O[c+432>>3]=N[a+20>>2];Sa(7353,c+432|0);O[c+416>>3]=N[a>>2];Sa(7144,c+416|0);J[c+400>>2]=K[a+38|0];Sa(9511,c+400|0);J[c+384>>2]=L[a+32>>1];Sa(9787,c+384|0);J[c+368>>2]=L[a+34>>1];Sa(9829,c+368|0);J[c+352>>2]=I[a+36>>1];Sa(9867,c+352|0);a:{b:{c:{d:{e:{f:{a=J[a+12>>2];switch(J[a+4>>2]){case 0:break c;case 3:break d;case 2:break e;case 1:break f;default:break a}}Sa(8031,0);O[c+144>>3]=N[a+8>>2];Sa(7235,c+144|0);d=N[a+28>>2];O[c+136>>3]=N[a+32>>2];O[c+128>>3]=d;Sa(9153,c+128|0);d=N[a+12>>2];O[c+120>>3]=N[a+16>>2];O[c+112>>3]=d;Sa(9109,c+112|0);d=N[a+20>>2];O[c+104>>3]=N[a+24>>2];O[c+96>>3]=d;Sa(9065,c+96|0);d=N[a+36>>2];O[c+88>>3]=N[a+40>>2];O[c+80>>3]=d;Sa(9021,c+80|0);J[c+64>>2]=K[a+44|0];Sa(9722,c- -64|0);J[c+48>>2]=K[a+45|0];Sa(9686,c+48|0);break b}Sa(7953,0);J[c+208>>2]=8;Sa(8335,c+208|0);e=J[a+148>>2];if((e|0)>0){while(1){e=a+(f<<3)|0;d=N[e+20>>2];O[c+192>>3]=N[e+24>>2];J[c+176>>2]=f;O[c+184>>3]=d;Sa(8776,c+176|0);f=f+1|0;e=J[a+148>>2];if((f|0)<(e|0)){continue}break}}J[c+160>>2]=e;Sa(9906,c+160|0);break b}Sa(7980,0);J[c+336>>2]=J[a+16>>2];Sa(8335,c+336|0);e=J[a+16>>2];if((e|0)>0){while(1){e=J[a+12>>2]+(f<<3)|0;d=N[e>>2];O[c+320>>3]=N[e+4>>2];J[c+304>>2]=f;O[c+312>>3]=d;Sa(8776,c+304|0);f=f+1|0;e=J[a+16>>2];if((f|0)<(e|0)){continue}break}}J[c+288>>2]=e;Sa(9930,c+288|0);d=N[a+20>>2];O[c+280>>3]=N[a+24>>2];O[c+272>>3]=d;Sa(8564,c+272|0);d=N[a+28>>2];O[c+264>>3]=N[a+32>>2];O[c+256>>3]=d;Sa(8611,c+256|0);J[c+240>>2]=K[a+36|0];Sa(9348,c+240|0);J[c+224>>2]=K[a+37|0];Sa(9387,c+224|0);break b}Sa(8005,0);O[c+32>>3]=N[a+8>>2];Sa(7235,c+32|0);d=N[a+12>>2];O[c+24>>3]=N[a+16>>2];O[c+16>>3]=d;Sa(8700,c+16|0);}Sa(10169,0);Sa(7929,0);Sa(10169,0);J[c>>2]=b;Sa(9269,c);}Fa=c+464|0;}function dd(a,b){var c=0,d=0,e=0,f=0,g=Q(0),h=0,i=0,j=Q(0),k=0,l=Q(0),m=0,n=0,o=0;c=J[b+4>>2];J[a>>2]=J[b>>2];J[a+4>>2]=c;c=J[b+20>>2];J[a+16>>2]=J[b+16>>2];J[a+20>>2]=c;c=J[b+12>>2];J[a+8>>2]=J[b+8>>2];J[a+12>>2]=c;c=J[b+40>>2];J[a+32>>2]=c;d=J[b+28>>2];J[a+48>>2]=d;n=a,o=wb(c,P(d,88)),J[n+36>>2]=o;n=a,o=wb(J[a+32>>2],P(J[a+48>>2],156)),J[n+40>>2]=o;J[a+24>>2]=J[b+32>>2];J[a+28>>2]=J[b+36>>2];J[a+44>>2]=J[b+24>>2];if(J[a+48>>2]>0){while(1){b=J[J[a+44>>2]+(h<<2)>>2];c=J[b+48>>2];j=N[J[c+12>>2]+8>>2];d=J[b+52>>2];g=N[J[d+12>>2]+8>>2];k=J[b+124>>2];e=J[d+8>>2];f=J[c+8>>2];c=J[a+40>>2]+P(h,156)|0;N[c+136>>2]=N[b+136>>2];N[c+140>>2]=N[b+140>>2];N[c+144>>2]=N[b+144>>2];J[c+112>>2]=J[f+8>>2];J[c+116>>2]=J[e+8>>2];N[c+120>>2]=N[f+120>>2];N[c+124>>2]=N[e+120>>2];N[c+128>>2]=N[f+128>>2];l=N[e+128>>2];J[c+152>>2]=h;N[c+132>>2]=l;J[c+148>>2]=k;J[c+80>>2]=0;J[c+84>>2]=0;J[c+88>>2]=0;J[c+92>>2]=0;J[c+96>>2]=0;J[c+100>>2]=0;J[c+104>>2]=0;J[c+108>>2]=0;d=J[a+36>>2]+P(h,88)|0;J[d+32>>2]=J[f+8>>2];J[d+36>>2]=J[e+8>>2];N[d+40>>2]=N[f+120>>2];N[d+44>>2]=N[e+120>>2];i=J[f+32>>2];J[d+48>>2]=J[f+28>>2];J[d+52>>2]=i;i=J[e+32>>2];J[d+56>>2]=J[e+28>>2];J[d+60>>2]=i;N[d+64>>2]=N[f+128>>2];N[d+68>>2]=N[e+128>>2];e=J[b+108>>2];J[d+16>>2]=J[b+104>>2];J[d+20>>2]=e;e=J[b+116>>2];f=J[b+112>>2];J[d+84>>2]=k;J[d+24>>2]=f;J[d+28>>2]=e;N[d+80>>2]=g;N[d+76>>2]=j;J[d+72>>2]=J[b+120>>2];if((k|0)>0){i=b- -64|0;e=0;while(1){b=c+P(e,36)|0;f=P(e,20)+i|0;a:{if(!K[a+20|0]){j=Q(0);g=Q(0);break a}g=N[a+8>>2];j=Q(g*N[f+12>>2]);g=Q(g*N[f+8>>2]);}J[b+32>>2]=0;J[b+24>>2]=0;J[b+28>>2]=0;N[b+20>>2]=j;N[b+16>>2]=g;J[b+8>>2]=0;J[b+12>>2]=0;J[b>>2]=0;J[b+4>>2]=0;m=J[f+4>>2];b=d+(e<<3)|0;J[b>>2]=J[f>>2];J[b+4>>2]=m;e=e+1|0;if((k|0)!=(e|0)){continue}break}}h=h+1|0;if((h|0)<J[a+48>>2]){continue}break}}return a}function sh(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=Q(0),f=0,g=Q(0),h=Q(0),i=Q(0),j=0,k=0,l=0,m=Q(0),n=Q(0),o=0,p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=0,v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0);j=J[a+48>>2];u=J[j+8>>2];J[a+104>>2]=u;k=J[a+52>>2];o=J[k+8>>2];J[a+108>>2]=o;q=N[j+32>>2];f=J[j+32>>2];n=N[j+28>>2];l=J[j+28>>2];J[a+128>>2]=l;J[a+132>>2]=f;r=N[k+32>>2];f=J[k+32>>2];g=N[k+28>>2];J[a+136>>2]=J[k+28>>2];J[a+140>>2]=f;s=N[j+120>>2];N[a+144>>2]=s;t=N[k+120>>2];N[a+148>>2]=t;d=N[j+128>>2];N[a+152>>2]=d;p=N[k+128>>2];N[a+156>>2]=p;h=N[a+72>>2];j=P(u,12);k=J[b+24>>2];e=N[(j+k|0)+8>>2];m=Ua(e);i=N[a+68>>2];e=Ta(e);f=J[b+28>>2];o=P(o,12);l=f+o|0;v=N[l+8>>2];w=N[l+4>>2];x=N[l>>2];l=j+f|0;y=N[l+8>>2];z=N[l+4>>2];A=N[l>>2];c=N[(k+o|0)+8>>2];i=Q(i-n);h=Q(h-q);n=Q(Q(e*i)+Q(m*h));N[a+116>>2]=n;m=Q(Q(m*i)-Q(h*e));N[a+112>>2]=m;e=Q(d+p);N[a+176>>2]=e>Q(0)?Q(Q(1)/e):e;e=N[a+80>>2];h=Ua(c);i=N[a+76>>2];c=Ta(c);g=Q(i-g);i=Q(e-r);e=Q(Q(c*g)+Q(h*i));N[a+124>>2]=e;h=Q(Q(h*g)-Q(i*c));N[a+120>>2]=h;c=Q(s+t);g=Q(Q(Q(p*e)*e)+Q(Q(Q(d*n)*n)+c));q=g;g=Q(p*h);i=Q(Q(g*h)+Q(Q(Q(d*m)*m)+c));r=Q(-d);d=Q(Q(Q(m*r)*n)-Q(e*g));c=Q(Q(q*i)-Q(d*d));c=c!=Q(0)?Q(Q(1)/c):c;N[a+172>>2]=q*c;N[a+160>>2]=i*c;d=Q(d*Q(-c));N[a+168>>2]=d;N[a+164>>2]=d;a:{if(K[b+20|0]){c=N[b+8>>2];d=Q(c*N[a+84>>2]);N[a+84>>2]=d;g=Q(c*N[a+92>>2]);N[a+92>>2]=g;c=Q(c*N[a+88>>2]);N[a+88>>2]=c;v=Q(Q(p*Q(g+Q(Q(h*c)-Q(d*e))))+v);y=Q(Q(r*Q(g+Q(Q(m*c)-Q(d*n))))+y);x=Q(x+Q(t*d));A=Q(A-Q(s*d));w=Q(w+Q(t*c));z=Q(z-Q(s*c));break a}J[a+92>>2]=0;J[a+84>>2]=0;J[a+88>>2]=0;}f=J[b+28>>2]+P(u,12)|0;N[f+4>>2]=z;N[f>>2]=A;f=J[b+28>>2];N[(f+P(J[a+104>>2],12)|0)+8>>2]=y;f=f+P(J[a+108>>2],12)|0;N[f+4>>2]=w;N[f>>2]=x;N[(J[b+28>>2]+P(J[a+108>>2],12)|0)+8>>2]=v;}function Hd(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;f=J[a+8>>2];e=J[a+4>>2];if(f-e>>3>>>0>=b>>>0){a:{if(!b){break a}d=e;f=b&7;if(f){while(1){i=J[c+4>>2];J[d>>2]=J[c>>2];J[d+4>>2]=i;d=d+8|0;g=g+1|0;if((f|0)!=(g|0)){continue}break}}e=(b<<3)+e|0;if((b-1&536870911)>>>0<7){break a}while(1){b=J[c+4>>2];J[d>>2]=J[c>>2];J[d+4>>2]=b;b=J[c+4>>2];J[d+8>>2]=J[c>>2];J[d+12>>2]=b;b=J[c+4>>2];J[d+16>>2]=J[c>>2];J[d+20>>2]=b;b=J[c+4>>2];J[d+24>>2]=J[c>>2];J[d+28>>2]=b;b=J[c+4>>2];J[d+32>>2]=J[c>>2];J[d+36>>2]=b;b=J[c+4>>2];J[d+40>>2]=J[c>>2];J[d+44>>2]=b;b=J[c+4>>2];J[d+48>>2]=J[c>>2];J[d+52>>2]=b;b=J[c+4>>2];J[d+56>>2]=J[c>>2];J[d+60>>2]=b;d=d- -64|0;if((e|0)!=(d|0)){continue}break}}J[a+4>>2]=e;return}b:{d=J[a>>2];j=e-d>>3;h=j+b|0;if(h>>>0<536870912){f=f-d|0;d=f>>2;h=f>>>0>=2147483640?536870911:d>>>0>h>>>0?d:h;if(h){if(h>>>0>=536870912){break b}k=Ra(h<<3);}g=(j<<3)+k|0;d=g;j=b&7;if(j){while(1){f=J[c+4>>2];J[d>>2]=J[c>>2];J[d+4>>2]=f;d=d+8|0;i=i+1|0;if((j|0)!=(i|0)){continue}break}}i=(b<<3)+g|0;if((b-1&536870911)>>>0>=7){while(1){b=J[c+4>>2];J[d>>2]=J[c>>2];J[d+4>>2]=b;b=J[c+4>>2];J[d+8>>2]=J[c>>2];J[d+12>>2]=b;b=J[c+4>>2];J[d+16>>2]=J[c>>2];J[d+20>>2]=b;b=J[c+4>>2];J[d+24>>2]=J[c>>2];J[d+28>>2]=b;b=J[c+4>>2];J[d+32>>2]=J[c>>2];J[d+36>>2]=b;b=J[c+4>>2];J[d+40>>2]=J[c>>2];J[d+44>>2]=b;b=J[c+4>>2];J[d+48>>2]=J[c>>2];J[d+52>>2]=b;b=J[c+4>>2];J[d+56>>2]=J[c>>2];J[d+60>>2]=b;d=d- -64|0;if((i|0)!=(d|0)){continue}break}}d=J[a>>2];if((d|0)!=(e|0)){while(1){e=e-8|0;b=J[e+4>>2];g=g-8|0;c=g;J[c>>2]=J[e>>2];J[c+4>>2]=b;if((d|0)!=(e|0)){continue}break}e=J[a>>2];}J[a+8>>2]=(h<<3)+k;J[a+4>>2]=i;J[a>>2]=g;if(e){Wa(e);}return}ma();B();}Fb();B();}function $c(a,b,c,d,e){var f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0);a:{switch(J[b+72>>2]){case 0:g=N[d>>2];f=N[c>>2];h=N[d+8>>2];i=N[b>>2];k=N[d+12>>2];m=N[b+4>>2];n=Q(Q(Q(h*i)+Q(k*m))+N[d+4>>2]);o=N[c+8>>2];p=N[b+24>>2];l=N[c+12>>2];q=N[b+28>>2];r=Q(Q(Q(o*p)+Q(l*q))+N[c+4>>2]);j=Q(n-r);N[a+4>>2]=j;h=Q(g+Q(Q(k*i)-Q(m*h)));i=Q(f+Q(Q(l*p)-Q(q*o)));g=Q(h-i);N[a>>2]=g;f=Q(Q(g*g)+Q(j*j));k=Q(Y(f));if(!(k<Q(1.1920928955078125e-7))){f=Q(Q(1)/k);k=Q(j*f);N[a+4>>2]=k;f=Q(g*f);N[a>>2]=f;f=Q(Q(g*f)+Q(j*k));}N[a+12>>2]=Q(r+n)*Q(.5);N[a+8>>2]=Q(i+h)*Q(.5);N[a+16>>2]=Q(f-N[b+76>>2])-N[b+80>>2];return;case 1:j=N[c+8>>2];g=N[b+16>>2];f=N[c+12>>2];h=N[b+20>>2];i=Q(Q(j*g)+Q(f*h));N[a+4>>2]=i;k=Q(Q(f*g)-Q(h*j));N[a>>2]=k;j=N[b+28>>2];g=N[c+12>>2];m=N[b+80>>2];n=N[b+76>>2];o=N[c>>2];p=N[c+4>>2];f=N[c+8>>2];h=N[b+24>>2];l=N[d>>2];q=N[d+8>>2];b=(e<<3)+b|0;r=N[b>>2];t=N[d+12>>2];u=N[b+4>>2];s=Q(Q(Q(q*r)+Q(t*u))+N[d+4>>2]);N[a+12>>2]=s;l=Q(l+Q(Q(t*r)-Q(u*q)));N[a+8>>2]=l;N[a+16>>2]=Q(Q(Q(Q(l-Q(o+Q(Q(g*h)-Q(j*f))))*k)+Q(Q(s-Q(p+Q(Q(f*h)+Q(g*j))))*i))-n)-m;return;case 2:g=N[d+8>>2];f=N[b+16>>2];h=N[d+12>>2];i=N[b+20>>2];j=Q(Q(g*f)+Q(h*i));N[a+4>>2]=j;g=Q(Q(h*f)-Q(i*g));N[a>>2]=g;f=N[b+28>>2];h=N[d+12>>2];e=(e<<3)+b|0;i=N[e>>2];k=N[e+4>>2];m=N[c+12>>2];l=N[b+80>>2];q=N[b+76>>2];r=N[d>>2];t=N[d+4>>2];n=N[d+8>>2];o=N[b+24>>2];u=N[c>>2];s=N[c+4>>2];p=N[c+8>>2];N[a+4>>2]=-j;N[a>>2]=-g;s=Q(s+Q(Q(p*i)+Q(m*k)));N[a+12>>2]=s;i=Q(u+Q(Q(m*i)-Q(k*p)));N[a+8>>2]=i;N[a+16>>2]=Q(Q(Q(Q(i-Q(r+Q(Q(h*o)-Q(f*n))))*g)+Q(Q(s-Q(t+Q(Q(n*o)+Q(h*f))))*j))-q)-l;break;default:break a}}}function Hb(a,b,c,d){var e=0,f=0,g=0,h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=0,n=Q(0),o=Q(0),p=Q(0),q=0,r=Q(0);f=Fa-48|0;Fa=f;if(J[a+28>>2]>0){while(1){g=J[a+12>>2];e=J[a+24>>2]+P(q,28)|0;Ha[J[J[g>>2]+24>>2]](g,f+32|0,c,J[e+20>>2]);g=J[a+12>>2];Ha[J[J[g>>2]+24>>2]](g,f+16|0,d,J[e+20>>2]);h=N[f+16>>2];j=N[f+32>>2];i=N[f+36>>2];k=N[f+20>>2];N[e+4>>2]=i<k?i:k;N[e>>2]=h>j?j:h;h=N[f+24>>2];j=N[f+40>>2];i=N[f+44>>2];k=N[f+28>>2];N[e+12>>2]=i>k?i:k;N[e+8>>2]=h<j?j:h;N[f+12>>2]=Q(Q(N[f+20>>2]+N[f+28>>2])*Q(.5))-Q(Q(N[f+36>>2]+N[f+44>>2])*Q(.5));N[f+8>>2]=Q(Q(N[f+16>>2]+N[f+24>>2])*Q(.5))-Q(Q(N[f+32>>2]+N[f+40>>2])*Q(.5));h=Q(N[f+12>>2]*Q(4));g=h<Q(0);n=N[e+12>>2];j=Q(Q(n+Q(.10000000149011612))+(g?Q(-0):h));o=N[e+4>>2];h=Q(Q(o+Q(-.10000000149011612))+(g?h:Q(-0)));i=Q(N[f+8>>2]*Q(4));g=i<Q(0);p=N[e+8>>2];k=Q(Q(p+Q(.10000000149011612))+(g?Q(-0):i));l=N[e>>2];i=Q(Q(l+Q(-.10000000149011612))+(g?i:Q(-0)));g=J[e+24>>2];m=P(g,40);e=m+J[b+4>>2]|0;r=N[e>>2];a:{b:{if(!(l>=r)){break b}l=o;o=N[e+4>>2];if(!(l>=o)){break b}l=p;p=N[e+8>>2];if(!(l<=p)){break b}l=n;n=N[e+12>>2];if(!(l<=n)|!(Q(i+Q(-.4000000059604645))<=r)|(!(o>=Q(h+Q(-.4000000059604645)))|!(p<=Q(k+Q(.4000000059604645))))){break b}e=0;if(n<=Q(j+Q(.4000000059604645))){break a}}xd(b,g);e=m+J[b+4>>2]|0;N[e+12>>2]=j;N[e+8>>2]=k;N[e+4>>2]=h;N[e>>2]=i;zd(b,g);H[(m+J[b+4>>2]|0)+36|0]=1;e=1;}if(e){e=J[b+40>>2];if((e|0)==J[b+36>>2]){J[b+36>>2]=e<<1;m=J[b+32>>2];e=_a(e<<3);J[b+32>>2]=e;eb(e,m,J[b+40>>2]<<2);Wa(m);e=J[b+40>>2];}J[J[b+32>>2]+(e<<2)>>2]=g;J[b+40>>2]=J[b+40>>2]+1;}q=q+1|0;if((q|0)<J[a+28>>2]){continue}break}}Fa=f+48|0;}function ed(a,b,c,d,e){var f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=0,k=Q(0),l=Q(0),m=0,n=0,o=0,p=Q(0),q=Q(0),r=Q(0),s=0,t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=0,y=0,z=0,A=0;j=J[b+148>>2];if((j|0)<=0){J[a>>2]=0;return Q(-34028234663852886e22)}a:{b:{c:{o=J[d+148>>2];if((o|0)<=0){c=j&3;if(j>>>0>=4){break c}g=Q(-34028234663852886e22);b=0;break b}g=N[e+12>>2];k=Q(N[c+4>>2]-N[e+4>>2]);f=N[e+8>>2];h=Q(N[c>>2]-N[e>>2]);v=Q(Q(g*k)-Q(f*h));w=Q(Q(g*h)+Q(f*k));h=N[c+12>>2];i=N[c+8>>2];k=Q(Q(g*h)+Q(i*f));d=d+20|0;x=b+20|0;y=b+84|0;p=Q(Q(g*i)-Q(h*f));r=Q(-p);z=o&-2;A=o&1;g=Q(-34028234663852886e22);c=0;while(1){b=c<<3;e=b+y|0;f=N[e>>2];i=N[e+4>>2];h=Q(Q(p*f)+Q(k*i));i=Q(Q(k*f)+Q(i*r));b=b+x|0;f=N[b>>2];l=N[b+4>>2];q=Q(v+Q(Q(p*f)+Q(k*l)));l=Q(w+Q(Q(k*f)+Q(l*r)));f=Q(34028234663852886e22);b=0;e=0;if((o|0)!=1){while(1){n=b<<3;s=d+(n|8)|0;t=Q(Q(i*Q(N[s>>2]-l))+Q(h*Q(N[s+4>>2]-q)));n=d+n|0;u=Q(Q(i*Q(N[n>>2]-l))+Q(h*Q(N[n+4>>2]-q)));f=f>u?u:f;f=f>t?t:f;b=b+2|0;e=e+2|0;if((z|0)!=(e|0)){continue}break}}if(A){b=d+(b<<3)|0;h=Q(Q(i*Q(N[b>>2]-l))+Q(h*Q(N[b+4>>2]-q)));f=f>h?h:f;}b=f>g;m=b?c:m;g=b?f:g;c=c+1|0;if((j|0)!=(c|0)){continue}break}break a}d=j&-4;g=Q(-34028234663852886e22);b=0;e=0;while(1){j=g<Q(34028234663852886e22);m=j?b:m;g=j?Q(34028234663852886e22):g;b=b+4|0;e=e+4|0;if((d|0)!=(e|0)){continue}break}}if(!c){break a}e=0;while(1){d=g<Q(34028234663852886e22);m=d?b:m;g=d?Q(34028234663852886e22):g;b=b+1|0;e=e+1|0;if((c|0)!=(e|0)){continue}break}}J[a>>2]=m;return g}function qg(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=Q(0),f=Q(0),g=0,h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=0,q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0);r=N[a+164>>2];k=N[a+96>>2];p=J[b+24>>2];g=p+P(J[a+120>>2],12)|0;u=N[g+8>>2];j=Ua(u);l=N[a+160>>2];n=N[a+92>>2];h=Ta(u);w=N[g+4>>2];t=N[a+72>>2];x=N[g>>2];m=N[a+68>>2];d=N[a+172>>2];c=N[a+104>>2];p=p+P(J[a+124>>2],12)|0;v=N[p+8>>2];i=Ua(v);s=N[a+168>>2];e=N[a+100>>2];f=Ta(v);y=N[p>>2];q=Q(e-s);d=Q(c-d);s=Q(Q(i*q)-Q(f*d));e=Q(Q(y+s)-N[a+76>>2]);z=N[p+4>>2];q=Q(Q(f*q)+Q(i*d));o=Q(Q(z+q)-N[a+80>>2]);i=Q(Y(Q(Q(e*e)+Q(o*o))));f=Q(0);d=Q(0);c=Q(0);l=Q(n-l);n=Q(k-r);r=Q(Q(j*l)-Q(h*n));k=Q(Q(x+r)-m);l=Q(Q(h*l)+Q(j*n));j=Q(Q(w+l)-t);h=Q(Y(Q(Q(k*k)+Q(j*j))));if(h>Q(.04999999701976776)){d=Q(Q(1)/h);c=Q(j*d);d=Q(k*d);}j=Q(0);if(i>Q(.04999999701976776)){f=Q(Q(1)/i);j=Q(o*f);f=Q(e*f);}A=c;m=Q(N[a+108>>2]-h);h=N[a+112>>2];i=Q(m-Q(h*i));e=Q(Q(s*j)-Q(f*q));k=N[a+188>>2];m=Q(Q(e*k)*e);e=N[a+180>>2];o=N[a+176>>2];c=Q(Q(r*c)-Q(d*l));n=N[a+184>>2];c=Q(Q(Q(h*h)*Q(m+e))+Q(o+Q(Q(c*n)*c)));t=Q(i*Q(-(c>Q(0)?Q(Q(1)/c):c)));m=Q(-t);c=Q(A*m);N[g+4>>2]=w+Q(o*c);d=Q(d*m);N[g>>2]=x+Q(o*d);g=J[b+24>>2];N[(g+P(J[a+120>>2],12)|0)+8>>2]=Q(n*Q(Q(r*c)-Q(d*l)))+u;g=g+P(J[a+124>>2],12)|0;d=Q(t*Q(-h));c=Q(j*d);N[g+4>>2]=z+Q(e*c);f=Q(f*d);N[g>>2]=y+Q(e*f);N[(J[b+24>>2]+P(J[a+124>>2],12)|0)+8>>2]=Q(k*Q(Q(s*c)-Q(f*q)))+v;return (i>Q(0)?i:Q(-i))<Q(.004999999888241291)|0}function Tf(a,b){a=a|0;b=b|0;var c=0,d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=0,t=Q(0),u=Q(0),v=Q(0),w=Q(0);s=J[b+28>>2];c=s+P(J[a+120>>2],12)|0;e=N[c+8>>2];m=N[c+4>>2];n=N[c>>2];c=P(J[a+116>>2],12)+s|0;f=N[c+8>>2];o=N[c+4>>2];p=N[c>>2];q=N[a+168>>2];r=N[a+164>>2];t=N[a+160>>2];u=N[a+156>>2];a:{if(N[a+68>>2]>Q(0)){d=N[a+112>>2];g=Q(Q(Q(d*N[a+100>>2])+Q(Q(e-f)+N[a+76>>2]))*Q(-N[a+204>>2]));N[a+112>>2]=d+g;f=Q(f-Q(r*g));h=N[a+128>>2];i=N[a+136>>2];e=Q(Q(q*g)+e);d=Q(Q(f*h)+Q(Q(n-Q(i*e))-p));j=N[a+132>>2];k=N[a+124>>2];l=Q(Q(Q(m+Q(j*e))-o)-Q(f*k));g=Q(Q(N[a+172>>2]*d)+Q(N[a+184>>2]*l));N[a+104>>2]=N[a+104>>2]-g;d=Q(Q(N[a+176>>2]*d)+Q(l*N[a+188>>2]));N[a+108>>2]=N[a+108>>2]-d;f=Q(f-Q(r*Q(Q(h*g)-Q(k*d))));e=Q(Q(q*Q(Q(i*g)-Q(j*d)))+e);break a}k=N[a+136>>2];l=N[a+128>>2];h=Q(Q(Q(n-Q(e*k))-p)+Q(f*l));v=N[a+132>>2];w=N[a+124>>2];i=Q(Q(Q(m+Q(e*v))-o)-Q(f*w));j=Q(e-f);g=Q(Q(Q(h*N[a+172>>2])+Q(i*N[a+184>>2]))+Q(j*N[a+196>>2]));N[a+104>>2]=N[a+104>>2]-g;d=Q(Q(Q(h*N[a+176>>2])+Q(i*N[a+188>>2]))+Q(j*N[a+200>>2]));N[a+108>>2]=N[a+108>>2]-d;h=Q(Q(Q(h*N[a+180>>2])+Q(i*N[a+192>>2]))+Q(j*N[a+204>>2]));N[a+112>>2]=N[a+112>>2]-h;f=Q(f-Q(r*Q(Q(Q(l*g)-Q(w*d))-h)));e=Q(Q(q*Q(Q(Q(k*g)-Q(v*d))-h))+e);}N[c+4>>2]=o+Q(u*d);N[c>>2]=p+Q(u*g);c=J[b+28>>2];N[(c+P(J[a+116>>2],12)|0)+8>>2]=f;c=c+P(J[a+120>>2],12)|0;N[c+4>>2]=m-Q(t*d);N[c>>2]=n-Q(t*g);N[(J[b+28>>2]+P(J[a+120>>2],12)|0)+8>>2]=e;}function wk(a){a=a|0;var b=0,c=0,d=0,e=0,f=0,g=0,h=0;c=Ra(103028);b=c;J[b+4>>2]=0;J[b+8>>2]=128;d=_a(1024);J[b>>2]=d;xb(d,0,J[b+8>>2]<<3);J[b+60>>2]=0;J[b+64>>2]=0;J[b+52>>2]=0;J[b+56>>2]=0;J[b+44>>2]=0;J[b+48>>2]=0;J[b+36>>2]=0;J[b+40>>2]=0;J[b+28>>2]=0;J[b+32>>2]=0;J[b+20>>2]=0;J[b+24>>2]=0;J[b+12>>2]=0;J[b+16>>2]=0;b=b+68|0;J[b+102796>>2]=0;J[b+102408>>2]=0;J[b+102400>>2]=0;J[b+102404>>2]=0;b=c+102868|0;J[b>>2]=-1;J[b+8>>2]=0;J[b+12>>2]=16;d=_a(640);J[b+4>>2]=d;xb(d,0,P(J[b+12>>2],40));d=J[b+12>>2];a:{if((d|0)<=1){e=d-1|0;d=J[b+4>>2];break a}d=J[b+4>>2];while(1){e=P(f,40);f=f+1|0;J[(e+d|0)+20>>2]=f;d=J[b+4>>2];J[(e+d|0)+32>>2]=-1;e=J[b+12>>2]-1|0;if((e|0)>(f|0)){continue}break}}J[(P(e,40)+d|0)+20>>2]=-1;J[(J[b+4>>2]+P(J[b+12>>2],40)|0)-8>>2]=-1;J[b+24>>2]=0;J[b+16>>2]=0;J[b+20>>2]=0;J[b+48>>2]=16;J[b+52>>2]=0;J[b+28>>2]=0;d=_a(128);J[b+36>>2]=16;J[b+40>>2]=0;J[b+44>>2]=d;g=b,h=_a(64),J[g+32>>2]=h;J[b+76>>2]=0;J[b+72>>2]=23348;J[b+68>>2]=23344;J[b+60>>2]=0;J[b+64>>2]=0;J[c+102948>>2]=0;J[c+102952>>2]=0;J[c+102976>>2]=0;J[c+102980>>2]=0;b=c+102956|0;J[b>>2]=0;J[b+4>>2]=0;H[c+102991|0]=1;H[c+102992|0]=1;H[c+102993|0]=0;H[c+102994|0]=1;H[c+102972|0]=1;b=J[a+4>>2];a=J[a>>2];H[c+102990|0]=1;I[c+102988>>1]=0;J[c+102964>>2]=a;J[c+102968>>2]=b;J[c+102984>>2]=0;J[c+102996>>2]=0;J[c+103e3>>2]=0;J[c+102944>>2]=c;a=c+103004|0;J[a>>2]=0;J[a+4>>2]=0;a=c+103012|0;J[a>>2]=0;J[a+4>>2]=0;a=c+103020|0;J[a>>2]=0;J[a+4>>2]=0;return c|0}function Uk(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0;e=a+16|0;d=e;c=J[a+16>>2];a:{b:{if(!c){break b}while(1){d=c;c=J[c+16>>2];if(c>>>0>b>>>0){e=d;c=J[d>>2];if(c){continue}break b}if(b>>>0<=c>>>0){break a}c=J[d+4>>2];if(c){continue}break}e=d+4|0;}c=Ra(20);J[c+8>>2]=d;J[c>>2]=0;J[c+4>>2]=0;J[c+16>>2]=b;J[e>>2]=c;b=J[J[a+12>>2]>>2];if(b){J[a+12>>2]=b;b=J[e>>2];}else {b=c;}f=J[a+16>>2];d=(f|0)==(b|0);H[b+12|0]=d;c:{if(d){break c}while(1){c=J[b+8>>2];if(K[c+12|0]){break c}d:{d=J[c+8>>2];e=J[d>>2];if((e|0)==(c|0)){e=J[d+4>>2];if(!(!e|K[e+12|0])){break d}e:{if(J[c>>2]==(b|0)){b=c;break e}b=J[c+4>>2];e=J[b>>2];J[c+4>>2]=e;if(e){J[e+8>>2]=c;d=J[c+8>>2];}J[b+8>>2]=d;d=J[c+8>>2];J[((J[d>>2]!=(c|0))<<2)+d>>2]=b;J[b>>2]=c;J[c+8>>2]=b;d=J[b+8>>2];c=J[d>>2];}H[b+12|0]=1;H[d+12|0]=0;b=J[c+4>>2];J[d>>2]=b;if(b){J[b+8>>2]=d;}J[c+8>>2]=J[d+8>>2];b=J[d+8>>2];J[((J[b>>2]!=(d|0))<<2)+b>>2]=c;J[c+4>>2]=d;J[d+8>>2]=c;break c}if(!(K[e+12|0]|!e)){break d}f:{if(J[c>>2]!=(b|0)){b=c;break f}e=J[b+4>>2];J[c>>2]=e;if(e){J[e+8>>2]=c;d=J[c+8>>2];}J[b+8>>2]=d;d=J[c+8>>2];J[((J[d>>2]!=(c|0))<<2)+d>>2]=b;J[b+4>>2]=c;J[c+8>>2]=b;d=J[b+8>>2];}H[b+12|0]=1;H[d+12|0]=0;b=J[d+4>>2];c=J[b>>2];J[d+4>>2]=c;if(c){J[c+8>>2]=d;}J[b+8>>2]=J[d+8>>2];c=J[d+8>>2];J[((J[c>>2]!=(d|0))<<2)+c>>2]=b;J[b>>2]=d;J[d+8>>2]=b;break c}H[c+12|0]=1;H[d+12|0]=(d|0)==(f|0);H[e+12|0]=1;b=d;if((f|0)!=(d|0)){continue}break}}J[a+20>>2]=J[a+20>>2]+1;}}function sd(a,b,c,d,e){var f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=0,r=Q(0),s=Q(0),t=Q(0),u=0,v=0,w=0;J[a+60>>2]=0;h=Q(N[b+8>>2]+N[d+8>>2]);u=J[b+20>>2];q=J[b+12>>2];i=N[b+20>>2];m=N[b+12>>2];g=Q(i-m);f=N[c+12>>2];j=N[e+12>>2];o=N[d+12>>2];k=N[d+16>>2];l=N[e+8>>2];n=Q(Q(N[e>>2]+Q(Q(j*o)-Q(k*l)))-N[c>>2]);k=Q(Q(Q(Q(l*o)+Q(j*k))+N[e+4>>2])-N[c+4>>2]);l=N[c+8>>2];j=Q(Q(f*n)+Q(k*l));o=Q(j-m);e=J[b+16>>2];c=J[b+24>>2];k=Q(Q(f*k)-Q(l*n));l=N[b+16>>2];p=Q(k-l);n=N[b+24>>2];f=Q(n-l);r=Q(Q(g*o)+Q(p*f));a:{b:{if(r<=Q(0)){if(Q(Q(o*o)+Q(p*p))>Q(h*h)){break a}if(!K[b+44|0]){h=Q(0);g=Q(0);break b}h=Q(0);g=Q(0);if(!(Q(Q(Q(m-N[b+28>>2])*Q(m-j))+Q(Q(l-k)*Q(l-N[b+32>>2])))>Q(0))){break b}break a}s=Q(Q(g*Q(i-j))+Q(f*Q(n-k)));if(s<=Q(0)){f=Q(j-i);m=Q(k-n);if(Q(Q(f*f)+Q(m*m))>Q(h*h)){break a}v=1;if(!K[b+45|0]){h=Q(0);g=Q(0);q=u;e=c;break b}h=Q(0);g=Q(0);q=u;e=c;if(!(Q(Q(Q(N[b+36>>2]-i)*f)+Q(m*Q(N[b+40>>2]-n)))>Q(0))){break b}break a}t=j;j=Q(Q(1)/Q(Q(g*g)+Q(f*f)));i=Q(t-Q(j*Q(Q(s*m)+Q(r*i))));t=Q(i*i);i=Q(k-Q(j*Q(Q(s*l)+Q(r*n))));if(Q(t+Q(i*i))>Q(h*h)){break a}v=65536;w=1;b=Q(Q(p*g)-Q(f*o))<Q(0);h=b?f:Q(-f);g=b?Q(-g):g;f=Q(Y(Q(Q(h*h)+Q(g*g))));if(f<Q(1.1920928955078125e-7)){break b}f=Q(Q(1)/f);g=Q(g*f);h=Q(h*f);}J[a+56>>2]=w;J[a+60>>2]=1;J[a+48>>2]=q;N[a+40>>2]=h;J[a+16>>2]=v;J[a+52>>2]=e;N[a+44>>2]=g;b=J[d+16>>2];J[a>>2]=J[d+12>>2];J[a+4>>2]=b;}}function Xc(a,b,c,d){var e=Q(0),f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0);h=N[a+48>>2];f=Q(Q(1)-d);l=Q(Q(f*N[a+68>>2])+Q(N[a+72>>2]*d));m=Ua(l);i=N[a+44>>2];l=Ta(l);p=Q(Q(Q(f*N[a+56>>2])+Q(N[a- -64>>2]*d))-Q(Q(l*i)+Q(h*m)));j=Q(Q(Q(f*N[a+52>>2])+Q(N[a+60>>2]*d))-Q(Q(m*i)-Q(h*l)));e=N[a+12>>2];i=Q(Q(f*N[a+32>>2])+Q(N[a+36>>2]*d));h=Ua(i);g=N[a+8>>2];i=Ta(i);q=Q(Q(Q(f*N[a+20>>2])+Q(N[a+28>>2]*d))-Q(Q(i*g)+Q(e*h)));e=Q(Q(Q(f*N[a+16>>2])+Q(N[a+24>>2]*d))-Q(Q(h*g)-Q(e*i)));d=Q(-l);f=Q(-i);g=Q(0);a:{switch(J[a+80>>2]){case 0:k=j;c=J[J[a+4>>2]+16>>2]+(c<<3)|0;j=N[c>>2];g=N[c+4>>2];n=Q(k+Q(Q(m*j)+Q(g*d)));k=e;b=J[J[a>>2]+16>>2]+(b<<3)|0;d=N[b>>2];e=N[b+4>>2];return Q(Q(Q(n-Q(k+Q(Q(h*d)+Q(e*f))))*N[a+92>>2])+Q(N[a+96>>2]*Q(Q(p+Q(Q(l*j)+Q(m*g)))-Q(q+Q(Q(i*d)+Q(h*e))))));case 1:k=j;b=J[J[a+4>>2]+16>>2]+(c<<3)|0;j=N[b>>2];g=N[b+4>>2];r=Q(k+Q(Q(m*j)+Q(g*d)));k=e;d=N[a+84>>2];e=N[a+88>>2];n=N[a+92>>2];o=N[a+96>>2];return Q(Q(Q(r-Q(k+Q(Q(h*d)+Q(e*f))))*Q(Q(h*n)+Q(o*f)))+Q(Q(Q(i*n)+Q(h*o))*Q(Q(p+Q(Q(l*j)+Q(m*g)))-Q(q+Q(Q(i*d)+Q(h*e))))));case 2:k=e;b=J[J[a>>2]+16>>2]+(b<<3)|0;e=N[b>>2];g=N[b+4>>2];r=Q(k+Q(Q(h*e)+Q(g*f)));k=j;f=N[a+84>>2];j=N[a+88>>2];n=N[a+92>>2];o=N[a+96>>2];g=Q(Q(Q(r-Q(k+Q(Q(m*f)+Q(j*d))))*Q(Q(m*n)+Q(o*d)))+Q(Q(Q(l*n)+Q(m*o))*Q(Q(q+Q(Q(i*e)+Q(h*g)))-Q(p+Q(Q(l*f)+Q(m*j))))));break;default:break a}}return g}function xh(a,b){a=a|0;b=b|0;var c=0,d=Q(0),e=0,f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=0,k=Q(0),l=0,m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0),A=Q(0),B=Q(0),C=Q(0),D=Q(0),E=Q(0),F=Q(0),G=Q(0),H=Q(0),I=Q(0);g=N[a+240>>2];e=J[b+28>>2];c=e+P(J[a+160>>2],12)|0;i=N[c>>2];j=e+P(J[a+168>>2],12)|0;n=N[j>>2];f=N[c+4>>2];o=N[j+4>>2];h=N[a+244>>2];k=N[a+248>>2];l=e+P(J[a+164>>2],12)|0;p=N[l>>2];e=e+P(J[a+172>>2],12)|0;q=N[e>>2];r=N[l+4>>2];s=N[e+4>>2];m=N[a+252>>2];t=N[a+256>>2];u=N[c+8>>2];v=N[j+8>>2];w=N[a+264>>2];x=N[a+260>>2];y=N[l+8>>2];z=N[e+8>>2];A=N[a+268>>2];d=Q(Q(Q(Q(Q(g*Q(i-n))+Q(Q(f-o)*h))+Q(Q(k*Q(p-q))+Q(Q(r-s)*m)))+Q(Q(Q(t*u)-Q(v*w))+Q(Q(x*y)-Q(z*A))))*Q(-N[a+272>>2]));N[a+156>>2]=N[a+156>>2]+d;B=N[a+236>>2];C=N[a+220>>2];D=N[a+232>>2];E=N[a+216>>2];F=N[a+228>>2];G=N[a+212>>2];H=N[a+224>>2];I=f;f=Q(N[a+208>>2]*d);N[c+4>>2]=I+Q(h*f);N[c>>2]=i+Q(g*f);c=J[b+28>>2];N[(c+P(J[a+160>>2],12)|0)+8>>2]=u+Q(t*Q(H*d));c=c+P(J[a+164>>2],12)|0;i=Q(d*G);N[c+4>>2]=r+Q(m*i);N[c>>2]=p+Q(k*i);c=J[b+28>>2];N[(c+P(J[a+164>>2],12)|0)+8>>2]=y+Q(x*Q(d*F));c=c+P(J[a+168>>2],12)|0;f=h;h=Q(d*E);N[c+4>>2]=o-Q(f*h);N[c>>2]=n-Q(g*h);c=J[b+28>>2];N[(c+P(J[a+168>>2],12)|0)+8>>2]=v-Q(w*Q(d*D));c=c+P(J[a+172>>2],12)|0;g=Q(d*C);N[c+4>>2]=s-Q(m*g);N[c>>2]=q-Q(k*g);N[(J[b+28>>2]+P(J[a+172>>2],12)|0)+8>>2]=z-Q(A*Q(d*B));}function Xg(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=Q(0),f=0,g=Q(0),h=Q(0),i=Q(0),j=0,k=Q(0),l=Q(0),m=0,n=Q(0),o=0,p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0);j=J[a+52>>2];o=J[j+8>>2];J[a+116>>2]=o;s=N[j+32>>2];m=J[j+32>>2];e=N[j+28>>2];f=J[j+28>>2];J[a+128>>2]=f;J[a+132>>2]=m;k=N[j+120>>2];N[a+136>>2]=k;l=N[j+128>>2];N[a+140>>2]=l;m=P(o,12);f=m+J[b+28>>2]|0;p=N[f+4>>2];q=N[f>>2];t=N[f+8>>2];f=J[b+24>>2]+m|0;u=N[f>>2];v=N[f+4>>2];i=N[f+8>>2];g=N[b>>2];d=N[j+116>>2];c=Q(N[a+84>>2]*Q(6.2831854820251465));h=Q(g*Q(d*Q(c*c)));d=Q(g*Q(h+Q(c*Q(Q(d+d)*N[a+88>>2]))));c=d!=Q(0)?Q(Q(1)/d):d;N[a+108>>2]=c;h=Q(h*c);N[a+92>>2]=h;g=N[a+72>>2];d=Ta(i);n=N[a+68>>2];r=Ua(i);e=Q(n-e);g=Q(g-s);i=Q(Q(r*e)-Q(d*g));N[a+120>>2]=i;d=Q(Q(d*e)+Q(r*g));N[a+124>>2]=d;g=Q(c+Q(k+Q(Q(l*i)*i)));n=Q(c+Q(k+Q(Q(l*d)*d)));e=Q(d*Q(i*Q(-l)));c=Q(Q(g*n)-Q(e*e));c=c!=Q(0)?Q(Q(1)/c):c;N[a+144>>2]=g*c;N[a+156>>2]=n*c;c=Q(e*Q(-c));N[a+148>>2]=c;N[a+152>>2]=c;N[a+164>>2]=h*Q(Q(v+d)-N[a+80>>2]);N[a+160>>2]=h*Q(Q(u+i)-N[a+76>>2]);c=Q(t*Q(.9800000190734863));a:{if(K[b+20|0]){e=N[b+8>>2];h=Q(e*N[a+96>>2]);N[a+96>>2]=h;e=Q(e*N[a+100>>2]);N[a+100>>2]=e;c=Q(Q(l*Q(Q(i*e)-Q(h*d)))+c);q=Q(q+Q(k*h));p=Q(p+Q(k*e));break a}J[a+96>>2]=0;J[a+100>>2]=0;}f=J[b+28>>2]+P(o,12)|0;N[f+4>>2]=p;N[f>>2]=q;N[(J[b+28>>2]+P(J[a+116>>2],12)|0)+8>>2]=c;}function cf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0;f=Fa+-64|0;Fa=f;a:{if(Za(b,18880,0)){J[c>>2]=0;d=1;break a}b:{if(K[a+8|0]&24){e=1;}else {if(!b){break b}e=lb(b,18612);if(!e){break b}e=(K[e+8|0]&24)!=0;}g=Za(a,b,e);}if(g){d=1;a=J[c>>2];if(!a){break a}J[c>>2]=J[a>>2];break a}c:{if(!b){break c}e=lb(b,18660);if(!e){break a}b=J[c>>2];if(b){J[c>>2]=J[b>>2];}g=J[e+8>>2];b=J[a+8>>2];if(g&(b^-1)&7|b&(g^-1)&96){break a}d=1;if(Za(J[a+12>>2],J[e+12>>2],0)){break a}if(Za(J[a+12>>2],18868,0)){a=J[e+12>>2];if(!a){break a}d=!lb(a,18712);break a}g=J[a+12>>2];if(!g){break c}d=0;b=lb(g,18660);if(b){if(!(H[a+8|0]&1)){break a}a=J[e+12>>2];c=0;d:{e:{while(1){d=0;if(!a){break d}a=lb(a,18660);if(!a|J[a+8>>2]&(J[b+8>>2]^-1)){break e}d=1;if(Za(J[b+12>>2],J[a+12>>2],0)){break d}if(!(H[b+8|0]&1)){break e}d=J[b+12>>2];if(!d){break e}b=lb(d,18660);if(b){a=J[a+12>>2];continue}break}b=lb(d,18772);if(!b){break e}c=Jc(b,J[a+12>>2]);}d=c;}break a}b=lb(g,18772);if(b){if(!(H[a+8|0]&1)){break a}d=Jc(b,J[e+12>>2]);break a}b=lb(g,18564);if(!b){break a}a=J[e+12>>2];if(!a){break a}a=lb(a,18564);if(!a){break a}xb(f+12|0,0,52);J[f+56>>2]=1;J[f+20>>2]=-1;J[f+16>>2]=b;J[f+8>>2]=a;Ha[J[J[a>>2]+28>>2]](a,f+8|0,J[c>>2],1);a=J[f+32>>2];if(!(!J[c>>2]|(a|0)!=1)){J[c>>2]=J[f+24>>2];}d=(a|0)==1;break a}d=0;}Fa=f- -64|0;return d|0}function mh(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=Q(0),f=0,g=Q(0),h=0,i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=Q(0);h=J[b+28>>2];f=h+P(J[a+104>>2],12)|0;m=N[f>>2];h=h+P(J[a+108>>2],12)|0;n=N[h>>2];o=N[f+4>>2];p=N[h+4>>2];q=N[a+160>>2];r=N[a+156>>2];s=N[a+168>>2];t=N[a+164>>2];g=N[b>>2];c=Q(g*N[a+96>>2]);i=Q(-c);j=N[a+88>>2];d=Q(N[b+4>>2]*N[a+100>>2]);e=N[h+8>>2];k=N[f+8>>2];l=Q(j-Q(N[a+188>>2]*Q(Q(d*N[a+152>>2])+Q(e-k))));c=c>l?l:c;c=c<i?i:c;N[a+88>>2]=c;l=N[a+80>>2];u=N[a+116>>2];c=Q(c-j);i=Q(k-Q(t*c));k=N[a+124>>2];j=Q(e+Q(s*c));e=Q(Q(d*N[a+144>>2])+Q(Q(u*i)+Q(Q(n-Q(k*j))-m)));v=N[a+120>>2];w=N[a+112>>2];d=Q(Q(d*N[a+148>>2])+Q(Q(Q(p+Q(v*j))-o)-Q(w*i)));c=Q(l-Q(Q(N[a+172>>2]*e)+Q(N[a+180>>2]*d)));N[a+80>>2]=c;x=N[a+84>>2];d=Q(x-Q(Q(N[a+176>>2]*e)+Q(N[a+184>>2]*d)));N[a+84>>2]=d;e=Q(Q(c*c)+Q(d*d));g=Q(g*N[a+92>>2]);if(e>Q(g*g)){e=Q(Y(e));if(!(e<Q(1.1920928955078125e-7))){e=Q(Q(1)/e);d=Q(d*e);c=Q(c*e);}d=Q(g*d);N[a+84>>2]=d;c=Q(g*c);N[a+80>>2]=c;}d=Q(d-x);N[f+4>>2]=o-Q(r*d);c=Q(c-l);N[f>>2]=m-Q(r*c);f=J[b+28>>2];N[(f+P(J[a+104>>2],12)|0)+8>>2]=Q(Q(-t)*Q(Q(w*d)-Q(c*u)))+i;f=f+P(J[a+108>>2],12)|0;N[f+4>>2]=p+Q(q*d);N[f>>2]=n+Q(q*c);N[(J[b+28>>2]+P(J[a+108>>2],12)|0)+8>>2]=Q(s*Q(Q(v*d)-Q(c*k)))+j;}function _i(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0;e=J[a>>2];f=J[a+4>>2]-e>>2;if(f>>>0<b>>>0){e=b-f|0;f=a;a:{d=J[a+8>>2];b=J[a+4>>2];if(e>>>0<=d-b>>2>>>0){b:{if(!e){break b}a=b;g=e&7;if(g){d=0;while(1){J[a>>2]=J[c>>2];a=a+4|0;d=d+1|0;if((g|0)!=(d|0)){continue}break}}b=(e<<2)+b|0;if((e-1&1073741823)>>>0<7){break b}while(1){J[a>>2]=J[c>>2];J[a+4>>2]=J[c>>2];J[a+8>>2]=J[c>>2];J[a+12>>2]=J[c>>2];J[a+16>>2]=J[c>>2];J[a+20>>2]=J[c>>2];J[a+24>>2]=J[c>>2];J[a+28>>2]=J[c>>2];a=a+32|0;if((b|0)!=(a|0)){continue}break}}J[f+4>>2]=b;break a}c:{g=J[f>>2];h=b-g>>2;a=h+e|0;if(a>>>0<1073741824){d=d-g|0;i=d>>1;i=d>>>0>=2147483644?1073741823:a>>>0<i>>>0?i:a;if(i){if(i>>>0>=1073741824){break c}j=Ra(i<<2);}d=(h<<2)+j|0;a=d;h=e&7;if(h){while(1){J[a>>2]=J[c>>2];a=a+4|0;k=k+1|0;if((h|0)!=(k|0)){continue}break}}h=(e<<2)+d|0;if((e-1&1073741823)>>>0>=7){while(1){J[a>>2]=J[c>>2];J[a+4>>2]=J[c>>2];J[a+8>>2]=J[c>>2];J[a+12>>2]=J[c>>2];J[a+16>>2]=J[c>>2];J[a+20>>2]=J[c>>2];J[a+24>>2]=J[c>>2];J[a+28>>2]=J[c>>2];a=a+32|0;if((h|0)!=(a|0)){continue}break}}if((b|0)!=(g|0)){while(1){d=d-4|0;b=b-4|0;J[d>>2]=J[b>>2];if((b|0)!=(g|0)){continue}break}}J[f+8>>2]=(i<<2)+j;J[f+4>>2]=h;J[f>>2]=d;if(g){Wa(g);}break a}ma();B();}Fb();B();}return}if(b>>>0<f>>>0){J[a+4>>2]=e+(b<<2);}}function ci(a,b,c){a=a|0;b=b|0;c=Q(c);var d=Q(0),e=Q(0),f=0,g=Q(0),h=0,i=Q(0),j=0,k=0,l=Q(0),m=Q(0),n=Q(0),o=0,p=Q(0),q=Q(0),r=0,s=0,t=0,u=0,v=0,w=0,x=Q(0),y=Q(0);a:{b:{c:{d:{j=J[a+148>>2];if((j|0)>0){o=j&3;if(j>>>0<4){break d}r=j&-4;k=a+20|0;while(1){h=f<<3;s=h+k|0;t=k+(h|8)|0;u=k+(h|16)|0;h=k+(h|24)|0;d=Q(Q(Q(Q(d+N[s+4>>2])+N[t+4>>2])+N[u+4>>2])+N[h+4>>2]);e=Q(Q(Q(Q(e+N[s>>2])+N[t>>2])+N[u>>2])+N[h>>2]);f=f+4|0;v=v+4|0;if((r|0)!=(v|0)){continue}break}break d}l=Q(Q(Q(1)/Q(j|0))*Q(0));i=l;break c}if(o){while(1){k=(f<<3)+a|0;d=Q(d+N[k+24>>2]);e=Q(e+N[k+20>>2]);f=f+1|0;w=w+1|0;if((o|0)!=(w|0)){continue}break}}i=d;d=Q(Q(1)/Q(j|0));l=Q(i*d);i=Q(e*d);if((j|0)>0){break b}}d=Q(0);e=Q(0);break a}k=a+24|0;a=a+20|0;f=0;e=Q(0);d=Q(0);while(1){h=a+(f<<3)|0;m=Q(N[h>>2]-i);f=f+1|0;o=a+(f<<3)|0;r=(f|0)<(j|0);g=Q(N[(r?o+4|0:k)>>2]-l);p=Q(N[(r?o:a)>>2]-i);n=Q(N[h+4>>2]-l);q=Q(Q(m*g)-Q(p*n));x=Q(Q(Q(q*Q(.0833333358168602))*Q(Q(Q(g*g)+Q(Q(n*n)+Q(n*g)))+Q(Q(p*p)+Q(Q(m*m)+Q(m*p)))))+x);q=Q(q*Q(.5));e=Q(e+q);n=Q(n+g);g=Q(q*Q(.3333333432674408));y=Q(y+Q(n*g));d=Q(d+Q(Q(m+p)*g));if((f|0)!=(j|0)){continue}break}}m=Q(e*c);N[b>>2]=m;g=Q(Q(1)/e);e=Q(y*g);l=Q(l+e);N[b+8>>2]=l;d=Q(d*g);i=Q(i+d);N[b+4>>2]=i;N[b+12>>2]=Q(m*Q(Q(Q(i*i)+Q(l*l))-Q(Q(d*d)+Q(e*e))))+Q(x*c);}function Fh(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=Q(0),f=0,g=Q(0),h=Q(0),i=Q(0),j=0,k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0);if(N[a+68>>2]>Q(0)){a=1;}else {d=N[a+144>>2];i=N[a+84>>2];j=J[b+24>>2];f=j+P(J[a+108>>2],12)|0;q=N[f+8>>2];c=Ua(q);k=N[a+140>>2];n=N[a+80>>2];e=Ta(q);g=N[a+152>>2];o=N[a+92>>2];j=j+P(J[a+112>>2],12)|0;r=N[j+8>>2];h=Ua(r);l=N[a+148>>2];p=N[a+88>>2];m=Ta(r);s=N[j>>2];l=Q(p-l);g=Q(o-g);o=Q(Q(h*l)-Q(m*g));p=N[f>>2];k=Q(n-k);i=Q(i-d);n=Q(Q(c*k)-Q(e*i));d=Q(Q(Q(s+o)-p)-n);t=N[j+4>>2];m=Q(Q(m*l)+Q(h*g));g=N[f+4>>2];i=Q(Q(e*k)+Q(c*i));c=Q(Q(Q(t+m)-g)-i);e=Q(Y(Q(Q(d*d)+Q(c*c))));a:{if(e<Q(1.1920928955078125e-7)){e=Q(0);break a}h=Q(Q(1)/e);c=Q(c*h);d=Q(d*h);}k=N[a+168>>2];h=N[a+160>>2];l=N[a+164>>2];v=g;g=N[a+156>>2];w=c;c=Q(e-N[a+104>>2]);c=c<Q(.20000000298023224)?c:Q(.20000000298023224);c=c<Q(-.20000000298023224)?Q(-.20000000298023224):c;u=Q(c*Q(-N[a+172>>2]));e=Q(w*u);N[f+4>>2]=v-Q(g*e);d=Q(d*u);N[f>>2]=p-Q(g*d);f=J[b+24>>2];N[(f+P(J[a+108>>2],12)|0)+8>>2]=q-Q(l*Q(Q(n*e)-Q(d*i)));f=f+P(J[a+112>>2],12)|0;N[f+4>>2]=t+Q(h*e);N[f>>2]=s+Q(h*d);N[(J[b+24>>2]+P(J[a+112>>2],12)|0)+8>>2]=Q(k*Q(Q(o*e)-Q(d*m)))+r;a=(c>Q(0)?c:Q(-c))<Q(.004999999888241291);}return a|0}function Nf(a,b){a=a|0;b=b|0;var c=Q(0),d=0,e=Q(0),f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=0,u=Q(0),v=Q(0);q=N[a+160>>2];j=N[a+156>>2];r=N[a+168>>2];s=N[a+164>>2];c=N[a+116>>2];f=N[a+192>>2];d=J[b+28>>2];t=d+P(J[a+136>>2],12)|0;g=N[t+8>>2];h=N[a+172>>2];l=N[t>>2];d=d+P(J[a+132>>2],12)|0;m=N[d>>2];k=N[t+4>>2];n=N[d+4>>2];o=N[a+176>>2];i=N[a+188>>2];p=N[d+8>>2];e=Q(Q(Q(c*N[a+220>>2])+Q(Q(Q(Q(f*g)+Q(Q(h*Q(l-m))+Q(Q(k-n)*o)))-Q(i*p))+N[a+216>>2]))*Q(-N[a+212>>2]));N[a+116>>2]=c+e;c=Q(N[b>>2]*N[a+120>>2]);u=Q(-c);v=N[a+112>>2];f=Q(g+Q(r*Q(f*e)));g=Q(p-Q(s*Q(i*e)));i=Q(v-Q(N[a+208>>2]*Q(Q(f-g)-N[a+124>>2])));c=c>i?i:c;c=c<u?u:c;N[a+112>>2]=c;i=N[a+200>>2];c=Q(c-v);f=Q(Q(r*c)+f);p=N[a+180>>2];h=Q(h*e);l=Q(l+Q(q*h));h=Q(m-Q(j*h));e=Q(o*e);m=Q(k+Q(q*e));k=Q(n-Q(j*e));n=N[a+184>>2];o=N[a+196>>2];c=Q(g-Q(s*c));e=Q(Q(Q(Q(i*f)+Q(Q(p*Q(l-h))+Q(Q(m-k)*n)))-Q(o*c))*Q(-N[a+204>>2]));N[a+108>>2]=N[a+108>>2]+e;g=Q(n*e);N[d+4>>2]=k-Q(j*g);k=j;j=Q(p*e);N[d>>2]=h-Q(k*j);d=J[b+28>>2];N[(d+P(J[a+132>>2],12)|0)+8>>2]=c-Q(s*Q(o*e));d=d+P(J[a+136>>2],12)|0;N[d+4>>2]=m+Q(q*g);N[d>>2]=l+Q(q*j);N[(J[b+28>>2]+P(J[a+136>>2],12)|0)+8>>2]=Q(r*Q(i*e))+f;}function rh(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=0,f=Q(0),g=Q(0),h=0,i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=Q(0);h=J[b+28>>2];e=h+P(J[a+104>>2],12)|0;m=N[e>>2];h=h+P(J[a+108>>2],12)|0;n=N[h>>2];o=N[e+4>>2];p=N[h+4>>2];q=N[a+148>>2];r=N[a+144>>2];s=N[a+156>>2];t=N[a+152>>2];g=N[b>>2];c=Q(g*N[a+100>>2]);d=Q(-c);i=N[a+92>>2];j=N[h+8>>2];k=N[e+8>>2];l=Q(i-Q(Q(j-k)*N[a+176>>2]));c=c>l?l:c;c=c<d?d:c;N[a+92>>2]=c;l=N[a+84>>2];u=N[a+116>>2];c=Q(c-i);i=Q(k-Q(t*c));k=N[a+124>>2];j=Q(j+Q(s*c));d=Q(Q(u*i)+Q(Q(n-Q(k*j))-m));v=N[a+120>>2];w=N[a+112>>2];f=Q(Q(Q(p+Q(v*j))-o)-Q(w*i));c=Q(l-Q(Q(N[a+160>>2]*d)+Q(N[a+168>>2]*f)));N[a+84>>2]=c;x=N[a+88>>2];d=Q(x-Q(Q(N[a+164>>2]*d)+Q(N[a+172>>2]*f)));N[a+88>>2]=d;f=Q(Q(c*c)+Q(d*d));g=Q(g*N[a+96>>2]);if(f>Q(g*g)){f=Q(Y(f));if(!(f<Q(1.1920928955078125e-7))){f=Q(Q(1)/f);d=Q(d*f);c=Q(c*f);}d=Q(g*d);N[a+88>>2]=d;c=Q(g*c);N[a+84>>2]=c;}d=Q(d-x);N[e+4>>2]=o-Q(r*d);c=Q(c-l);N[e>>2]=m-Q(r*c);e=J[b+28>>2];N[(e+P(J[a+104>>2],12)|0)+8>>2]=Q(Q(-t)*Q(Q(w*d)-Q(c*u)))+i;e=e+P(J[a+108>>2],12)|0;N[e+4>>2]=p+Q(q*d);N[e>>2]=n+Q(q*c);N[(J[b+28>>2]+P(J[a+108>>2],12)|0)+8>>2]=Q(s*Q(Q(v*d)-Q(c*k)))+j;}function Mf(a,b){a=a|0;b=b|0;var c=Q(0),d=0,e=Q(0),f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=0,n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=Q(0),y=Q(0),z=Q(0);l=N[a+144>>2];e=N[a+80>>2];m=J[b+24>>2];d=m+P(J[a+132>>2],12)|0;t=N[d+8>>2];c=Ua(t);h=N[a+140>>2];p=N[a+76>>2];g=Ta(t);i=N[a+152>>2];q=N[a+88>>2];m=m+P(J[a+136>>2],12)|0;u=N[m+8>>2];f=Ua(u);j=N[a+148>>2];r=N[a+84>>2];n=Ta(u);o=N[a+100>>2];k=N[a+104>>2];s=Q(Q(g*o)+Q(c*k));v=N[m>>2];w=N[d>>2];j=Q(r-j);i=Q(q-i);q=Q(Q(f*j)-Q(n*i));h=Q(p-h);e=Q(e-l);p=Q(Q(c*h)-Q(g*e));r=Q(Q(Q(v-w)+q)-p);l=Q(Q(c*o)-Q(k*g));o=N[m+4>>2];k=N[d+4>>2];i=Q(Q(n*j)+Q(f*i));h=Q(Q(g*h)+Q(c*e));j=Q(Q(Q(o-k)+i)-h);g=Q(Q(r*l)+Q(s*j));x=Q(-g);y=N[a+168>>2];c=N[a+200>>2];e=Q(Q(y*c)*c);z=N[a+164>>2];c=N[a+196>>2];f=N[a+156>>2];n=N[a+160>>2];c=Q(e+Q(Q(Q(z*c)*c)+Q(f+n)));c=c!=Q(0)?Q(x/c):Q(0);e=Q(s*c);N[d+4>>2]=k-Q(f*e);k=f;f=Q(l*c);N[d>>2]=w-Q(k*f);d=J[b+24>>2];N[(d+P(J[a+132>>2],12)|0)+8>>2]=t-Q(z*Q(Q(Q(Q(p+r)*s)-Q(l*Q(h+j)))*c));d=d+P(J[a+136>>2],12)|0;N[d+4>>2]=o+Q(n*e);N[d>>2]=v+Q(n*f);N[(J[b+24>>2]+P(J[a+136>>2],12)|0)+8>>2]=u+Q(y*Q(Q(Q(q*s)-Q(l*i))*c));return (g>Q(0)?g:x)<=Q(.004999999888241291)|0}function bf(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var f=0,g=0,h=0;if(Za(a,J[b+8>>2],e)){if(!(J[b+28>>2]==1|J[b+4>>2]!=(c|0))){J[b+28>>2]=d;}return}a:{if(Za(a,J[b>>2],e)){if(!(J[b+16>>2]!=(c|0)&J[b+20>>2]!=(c|0))){if((d|0)!=1){break a}J[b+32>>2]=1;return}J[b+32>>2]=d;if(J[b+44>>2]!=4){f=a+16|0;h=f+(J[a+12>>2]<<3)|0;d=0;b:{c:{while(1){d:{if(f>>>0>=h>>>0){break d}I[b+52>>1]=0;hc(f,b,c,c,1,e);if(K[b+54|0]){break d}e:{if(!K[b+53|0]){break e}if(K[b+52|0]){d=1;if(J[b+24>>2]==1){break c}g=1;if(K[a+8|0]&2){break e}break c}g=1;if(!(H[a+8|0]&1)){break c}}f=f+8|0;continue}break}a=4;if(!g){break b}}a=3;}J[b+44>>2]=a;if(d&1){break a}}J[b+20>>2]=c;J[b+40>>2]=J[b+40>>2]+1;if(J[b+36>>2]!=1|J[b+24>>2]!=2){break a}H[b+54|0]=1;return}g=J[a+12>>2];h=a+16|0;Qb(h,b,c,d,e);f=a+24|0;g=h+(g<<3)|0;if(f>>>0>=g>>>0){break a}a=J[a+8>>2];if(!(!(a&2)&J[b+36>>2]!=1)){while(1){if(K[b+54|0]){break a}Qb(f,b,c,d,e);f=f+8|0;if(g>>>0>f>>>0){continue}break}break a}if(!(a&1)){while(1){if(K[b+54|0]|J[b+36>>2]==1){break a}Qb(f,b,c,d,e);f=f+8|0;if(g>>>0>f>>>0){continue}break a}}while(1){if(K[b+54|0]|J[b+36>>2]==1&J[b+24>>2]==1){break a}Qb(f,b,c,d,e);f=f+8|0;if(g>>>0>f>>>0){continue}break}}}function Xf(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=0,f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=0,k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0);d=N[a+132>>2];i=N[a+72>>2];j=J[b+24>>2];e=j+P(J[a+96>>2],12)|0;q=N[e+8>>2];c=Ua(q);k=N[a+128>>2];n=N[a+68>>2];g=Ta(q);f=N[a+140>>2];o=N[a+80>>2];j=j+P(J[a+100>>2],12)|0;r=N[j+8>>2];h=Ua(r);l=N[a+136>>2];p=N[a+76>>2];m=Ta(r);s=N[j>>2];l=Q(p-l);f=Q(o-f);o=Q(Q(h*l)-Q(m*f));p=N[e>>2];k=Q(n-k);i=Q(i-d);n=Q(Q(c*k)-Q(g*i));d=Q(Q(Q(s+o)-p)-n);t=N[j+4>>2];m=Q(Q(m*l)+Q(h*f));f=N[e+4>>2];i=Q(Q(g*k)+Q(c*i));c=Q(Q(Q(t+m)-f)-i);g=Q(Y(Q(Q(d*d)+Q(c*c))));a:{if(g<Q(1.1920928955078125e-7)){g=Q(0);break a}h=Q(Q(1)/g);c=Q(c*h);d=Q(d*h);}k=N[a+156>>2];h=N[a+148>>2];l=N[a+152>>2];v=f;f=N[a+144>>2];w=c;c=Q(g-N[a+84>>2]);c=c<Q(.20000000298023224)?c:Q(.20000000298023224);u=Q((c<Q(0)?Q(0):c)*Q(-N[a+160>>2]));c=Q(w*u);N[e+4>>2]=v-Q(f*c);d=Q(d*u);N[e>>2]=p-Q(f*d);e=J[b+24>>2];N[(e+P(J[a+96>>2],12)|0)+8>>2]=q-Q(l*Q(Q(n*c)-Q(d*i)));e=e+P(J[a+100>>2],12)|0;N[e+4>>2]=t+Q(h*c);N[e>>2]=s+Q(h*d);N[(J[b+24>>2]+P(J[a+100>>2],12)|0)+8>>2]=Q(k*Q(Q(o*c)-Q(d*m)))+r;return Q(g-N[a+84>>2])<Q(.004999999888241291)|0}function oe(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=Q(0);e=J[a+88>>2];if(!K[e+102989|0]){c=Ya(e,44);J[c+40>>2]=0;J[c+32>>2]=-65535;J[c+24>>2]=0;J[c+28>>2]=0;J[c>>2]=0;J[c+4>>2]=0;I[c+36>>1]=0;J[c+8>>2]=0;J[c+12>>2]=0;J[c+40>>2]=J[b+4>>2];N[c+16>>2]=N[b+8>>2];j=N[b+12>>2];J[c+8>>2]=a;N[c+20>>2]=j;J[c+4>>2]=0;d=L[b+22>>1]|L[b+24>>1]<<16;I[c+32>>1]=d;I[c+34>>1]=d>>>16;I[c+36>>1]=L[b+26>>1];H[c+38|0]=K[b+20|0];d=J[b>>2];d=Ha[J[J[d>>2]+8>>2]](d,e)|0;J[c+12>>2]=d;g=Ha[J[J[d>>2]+12>>2]](d)|0;d=Ya(e,P(g,28));J[c+24>>2]=d;a:{if((g|0)<=0){break a}e=0;if(g>>>0>=4){i=g&-4;while(1){f=d+P(e,28)|0;J[f+24>>2]=-1;J[f+16>>2]=0;f=d+P(e|1,28)|0;J[f+24>>2]=-1;J[f+16>>2]=0;f=d+P(e|2,28)|0;J[f+24>>2]=-1;J[f+16>>2]=0;f=d+P(e|3,28)|0;J[f+24>>2]=-1;J[f+16>>2]=0;e=e+4|0;h=h+4|0;if((i|0)!=(h|0)){continue}break}}g=g&3;if(!g){break a}h=0;while(1){i=d+P(e,28)|0;J[i+24>>2]=-1;J[i+16>>2]=0;e=e+1|0;h=h+1|0;if((g|0)!=(h|0)){continue}break}}J[c+28>>2]=0;N[c>>2]=N[b+16>>2];if(K[a+4|0]&32){nd(c,J[a+88>>2]+102868|0,a+12|0);}J[c+4>>2]=J[a+100>>2];J[a+100>>2]=c;J[a+104>>2]=J[a+104>>2]+1;J[c+8>>2]=a;if(N[c>>2]>Q(0)){Jb(a);}H[J[a+88>>2]+102988|0]=1;}return c|0}function eb(a,b,c){var d=0,e=0,f=0;if(c>>>0>=512){Ba(a|0,b|0,c|0);return a}e=a+c|0;a:{if(!((a^b)&3)){b:{if(!(a&3)){c=a;break b}if(!c){c=a;break b}c=a;while(1){H[c|0]=K[b|0];b=b+1|0;c=c+1|0;if(!(c&3)){break b}if(c>>>0<e>>>0){continue}break}}d=e&-4;c:{if(d>>>0<64){break c}f=d+-64|0;if(f>>>0<c>>>0){break c}while(1){J[c>>2]=J[b>>2];J[c+4>>2]=J[b+4>>2];J[c+8>>2]=J[b+8>>2];J[c+12>>2]=J[b+12>>2];J[c+16>>2]=J[b+16>>2];J[c+20>>2]=J[b+20>>2];J[c+24>>2]=J[b+24>>2];J[c+28>>2]=J[b+28>>2];J[c+32>>2]=J[b+32>>2];J[c+36>>2]=J[b+36>>2];J[c+40>>2]=J[b+40>>2];J[c+44>>2]=J[b+44>>2];J[c+48>>2]=J[b+48>>2];J[c+52>>2]=J[b+52>>2];J[c+56>>2]=J[b+56>>2];J[c+60>>2]=J[b+60>>2];b=b- -64|0;c=c- -64|0;if(f>>>0>=c>>>0){continue}break}}if(c>>>0>=d>>>0){break a}while(1){J[c>>2]=J[b>>2];b=b+4|0;c=c+4|0;if(d>>>0>c>>>0){continue}break}break a}if(e>>>0<4){c=a;break a}d=e-4|0;if(d>>>0<a>>>0){c=a;break a}c=a;while(1){H[c|0]=K[b|0];H[c+1|0]=K[b+1|0];H[c+2|0]=K[b+2|0];H[c+3|0]=K[b+3|0];b=b+4|0;c=c+4|0;if(d>>>0>=c>>>0){continue}break}}if(c>>>0<e>>>0){while(1){H[c|0]=K[b|0];b=b+1|0;c=c+1|0;if((e|0)!=(c|0)){continue}break}}return a}function uf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0;g=Fa-16|0;Fa=g;J[g+12>>2]=b;h=a+102868|0;J[g+8>>2]=h;d=Fa-1040|0;Fa=d;J[d+1036>>2]=256;b=d+8|0;J[d+4>>2]=b;J[d+8>>2]=J[h>>2];a=b;e=1;while(1){a:{e=e-1|0;J[d+1032>>2]=e;f=J[(e<<2)+a>>2];b:{if((f|0)==-1){break b}i=P(f,40);f=i+J[h+4>>2]|0;if(Q(N[c>>2]-N[f+8>>2])>Q(0)|Q(N[c+4>>2]-N[f+12>>2])>Q(0)|(Q(N[f>>2]-N[c+8>>2])>Q(0)|Q(N[f+4>>2]-N[c+12>>2])>Q(0))){break b}if(J[f+24>>2]==-1){a=J[g+12>>2];e=Ha[J[J[a>>2]+8>>2]](a,J[J[(J[J[g+8>>2]+4>>2]+i|0)+16>>2]+16>>2])|0;a=J[d+4>>2];if(!e){break a}e=J[d+1032>>2];if((e|0)>0){continue}break a}c:{if(J[d+1036>>2]!=(e|0)){break c}J[d+1036>>2]=e<<1;e=_a(e<<3);J[d+4>>2]=e;eb(e,a,J[d+1032>>2]<<2);if((a|0)==(b|0)){break c}Wa(a);}a=J[d+4>>2];J[a+(J[d+1032>>2]<<2)>>2]=J[f+24>>2];e=J[d+1032>>2]+1|0;J[d+1032>>2]=e;d:{if((e|0)!=J[d+1036>>2]){break d}J[d+1036>>2]=e<<1;e=_a(e<<3);J[d+4>>2]=e;eb(e,a,J[d+1032>>2]<<2);if((a|0)==(b|0)){break d}Wa(a);}a=J[d+4>>2];J[a+(J[d+1032>>2]<<2)>>2]=J[f+28>>2];e=J[d+1032>>2]+1|0;J[d+1032>>2]=e;}if((e|0)>0){continue}}break}if((a|0)!=(b|0)){Wa(a);}Fa=d+1040|0;Fa=g+16|0;}
				function Yh(a,b){a=a|0;b=b|0;var c=0,d=0,e=Q(0),f=0,g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0);d=Fa-16|0;Fa=d;a:{if(K[J[a+88>>2]+102989|0]|J[a>>2]==(b|0)){break a}J[a>>2]=b;Jb(a);b:{if(J[a>>2]){break b}J[a+72>>2]=0;J[a+64>>2]=0;J[a+68>>2]=0;e=N[a+56>>2];N[a+52>>2]=e;j=N[a+48>>2];c=J[a+48>>2];k=N[a+44>>2];J[a+36>>2]=J[a+44>>2];J[a+40>>2]=c;f=J[a+88>>2]+102868|0;if(K[a+4|0]&2){g=Ua(e);N[d+12>>2]=g;e=Ta(e);N[d+8>>2]=e;h=N[a+28>>2];i=N[a+32>>2];N[d+4>>2]=j-Q(Q(e*h)+Q(g*i));N[d>>2]=k-Q(Q(g*h)-Q(i*e));b=J[a+100>>2];if(!b){break b}c=a+12|0;while(1){Hb(b,f,d,c);b=J[b+4>>2];if(b){continue}break}break b}b=J[a+100>>2];if(!b){break b}c=a+12|0;while(1){Hb(b,f,c,c);b=J[b+4>>2];if(b){continue}break}}J[a+144>>2]=0;J[a+84>>2]=0;J[a+76>>2]=0;J[a+80>>2]=0;I[a+4>>1]=L[a+4>>1]|2;b=J[a+112>>2];if(b){while(1){c=J[b+12>>2];Ib(J[a+88>>2]+102868|0,J[b+4>>2]);b=c;if(b){continue}break}}J[a+112>>2]=0;c=J[a+100>>2];if(!c){break a}a=J[a+88>>2]+102868|0;while(1){f=J[c+28>>2];if((f|0)>0){b=0;while(1){uc(a,J[(J[c+24>>2]+P(b,28)|0)+24>>2]);b=b+1|0;if((f|0)!=(b|0)){continue}break}}c=J[c+4>>2];if(c){continue}break}}Fa=d+16|0;}function Jb(a){a=a|0;var b=Q(0),c=Q(0),d=Q(0),e=Q(0),f=0,g=0,h=Q(0),i=0,j=Q(0),k=Q(0);g=Fa-16|0;Fa=g;J[a+116>>2]=0;J[a+120>>2]=0;J[a+28>>2]=0;J[a+32>>2]=0;J[a+124>>2]=0;J[a+128>>2]=0;a:{if(M[a>>2]<=1){f=J[a+16>>2];i=J[a+12>>2];J[a+44>>2]=i;J[a+48>>2]=f;J[a+36>>2]=i;J[a+40>>2]=f;N[a+52>>2]=N[a+56>>2];break a}c=N[3186];d=N[3185];b:{c:{f=J[a+100>>2];if(!f){break c}while(1){b=N[f>>2];if(b!=Q(0)){i=J[f+12>>2];Ha[J[J[i>>2]+28>>2]](i,g,b);b=N[g>>2];e=Q(b+N[a+116>>2]);N[a+116>>2]=e;j=N[g+4>>2];k=N[g+8>>2];h=Q(N[g+12>>2]+N[a+124>>2]);N[a+124>>2]=h;d=Q(d+Q(b*j));c=Q(c+Q(b*k));}f=J[f+4>>2];if(f){continue}break}if(e>Q(0)){b=Q(Q(1)/e);N[a+120>>2]=b;d=Q(d*b);c=Q(c*b);}if(!(h>Q(0))|K[a+4|0]&16){break c}e=Q(h-Q(e*Q(Q(d*d)+Q(c*c))));N[a+124>>2]=e;b=Q(Q(1)/e);break b}J[a+124>>2]=0;b=Q(0);}N[a+128>>2]=b;N[a+32>>2]=c;N[a+28>>2]=d;b=N[a+44>>2];h=N[a+24>>2];j=N[a+20>>2];e=Q(N[a+12>>2]+Q(Q(h*d)-Q(c*j)));N[a+44>>2]=e;k=N[a+48>>2];c=Q(Q(Q(j*d)+Q(h*c))+N[a+16>>2]);N[a+48>>2]=c;N[a+40>>2]=c;N[a+36>>2]=e;d=N[a+72>>2];N[a+64>>2]=N[a+64>>2]-Q(d*Q(c-k));N[a+68>>2]=Q(d*Q(e-b))+N[a+68>>2];}Fa=g+16|0;}function Af(a,b){a=a|0;b=b|0;var c=0,d=0,e=Q(0),f=0,g=0,h=Q(0);if(!K[a+102989|0]){c=Ya(a,152);d=K[b+39|0];I[c+4>>1]=((d|0)!=0)<<3;d=d<<3;if(K[b+38|0]){d=d|16;I[c+4>>1]=d;}if(K[b+36|0]){d=d|4;I[c+4>>1]=d;}if(K[b+37|0]){d=d|2;I[c+4>>1]=d;}if(K[b+40|0]){I[c+4>>1]=d|32;}J[c+88>>2]=a;f=J[b+8>>2];d=J[b+4>>2];J[c+12>>2]=d;J[c+16>>2]=f;e=N[b+12>>2];J[c+44>>2]=d;J[c+48>>2]=f;J[c+36>>2]=d;J[c+40>>2]=f;J[c+28>>2]=0;J[c+32>>2]=0;g=c,h=Ua(e),N[g+24>>2]=h;g=c,h=Ta(e),N[g+20>>2]=h;e=N[b+12>>2];J[c+108>>2]=0;J[c+112>>2]=0;J[c+60>>2]=0;N[c+56>>2]=e;N[c+52>>2]=e;J[c+92>>2]=0;J[c+96>>2]=0;d=J[b+20>>2];J[c+64>>2]=J[b+16>>2];J[c+68>>2]=d;N[c+72>>2]=N[b+24>>2];N[c+132>>2]=N[b+28>>2];N[c+136>>2]=N[b+32>>2];e=N[b+48>>2];J[c+144>>2]=0;J[c+84>>2]=0;J[c+76>>2]=0;J[c+80>>2]=0;N[c+140>>2]=e;d=J[b>>2];J[c+124>>2]=0;J[c+128>>2]=0;J[c>>2]=d;e=(d|0)==2?Q(1):Q(0);N[c+120>>2]=e;N[c+116>>2]=e;b=J[b+44>>2];J[c+100>>2]=0;J[c+104>>2]=0;J[c+148>>2]=b;d=c;J[c+92>>2]=0;b=J[a+102948>>2];J[c+96>>2]=b;if(b){J[b+92>>2]=d;}J[a+102948>>2]=d;J[a+102956>>2]=J[a+102956>>2]+1;}return d|0}function xd(a,b){var c=0,d=0,e=0,f=0,g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=0;if(J[a>>2]==(b|0)){J[a>>2]=-1;return}f=J[a+4>>2];d=J[(f+P(b,40)|0)+20>>2];e=P(d,40)+f|0;c=J[e+20>>2];k=b;b=J[e+24>>2];if((k|0)==(b|0)){b=J[e+28>>2];}a:{if((c|0)!=-1){e=f+P(c,40)|0;J[((d|0)==J[e+24>>2]?e+24|0:e+28|0)>>2]=b;J[(f+P(b,40)|0)+20>>2]=c;b=P(d,40);J[(b+J[a+4>>2]|0)+20>>2]=J[a+16>>2];J[(b+J[a+4>>2]|0)+32>>2]=-1;J[a+16>>2]=d;J[a+8>>2]=J[a+8>>2]-1;while(1){b=yd(a,c);c=J[a+4>>2];e=P(b,40);b=c+e|0;f=P(J[b+28>>2],40);d=c+f|0;g=N[d>>2];k=P(J[b+24>>2],40);c=k+c|0;h=N[c>>2];i=N[c+4>>2];j=N[d+4>>2];N[b+4>>2]=i<j?i:j;N[b>>2]=g>h?h:g;g=N[c+8>>2];h=N[d+8>>2];i=N[c+12>>2];j=N[d+12>>2];N[b+12>>2]=i>j?i:j;N[b+8>>2]=g>h?g:h;b=J[a+4>>2];d=b+e|0;c=J[(b+k|0)+32>>2];b=J[(b+f|0)+32>>2];J[d+32>>2]=((b|0)<(c|0)?c:b)+1;c=J[d+20>>2];if((c|0)!=-1){continue}break}break a}J[a>>2]=b;J[(f+P(b,40)|0)+20>>2]=-1;b=P(d,40);J[(b+J[a+4>>2]|0)+20>>2]=J[a+16>>2];J[(b+J[a+4>>2]|0)+32>>2]=-1;J[a+16>>2]=d;J[a+8>>2]=J[a+8>>2]-1;}}function rg(a,b){a=a|0;b=b|0;var c=0,d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=0,n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0),w=Q(0),x=Q(0),y=Q(0);k=Q(-N[a+112>>2]);l=N[a+136>>2];m=J[b+28>>2];c=m+P(J[a+124>>2],12)|0;n=N[c>>2];j=N[c+8>>2];o=N[a+156>>2];p=N[c+4>>2];q=N[a+152>>2];r=N[a+140>>2];f=N[a+128>>2];c=P(J[a+120>>2],12)+m|0;s=N[c>>2];d=N[c+8>>2];t=N[a+148>>2];g=N[c+4>>2];u=N[a+144>>2];h=N[a+132>>2];e=Q(Q(Q(k*Q(Q(l*Q(n-Q(j*o)))+Q(Q(p+Q(j*q))*r)))-Q(Q(f*Q(s-Q(d*t)))+Q(Q(g+Q(d*u))*h)))*Q(-N[a+192>>2]));N[a+116>>2]=N[a+116>>2]+e;w=N[a+188>>2];v=N[a+180>>2];x=N[a+184>>2];y=g;g=N[a+176>>2];i=h;h=Q(-e);i=Q(i*h);N[c+4>>2]=y+Q(g*i);f=Q(f*h);N[c>>2]=s+Q(g*f);c=J[b+28>>2];N[(c+P(J[a+120>>2],12)|0)+8>>2]=d+Q(x*Q(Q(u*i)-Q(f*t)));c=c+P(J[a+124>>2],12)|0;d=Q(e*k);e=Q(r*d);N[c+4>>2]=p+Q(v*e);d=Q(l*d);N[c>>2]=n+Q(v*d);N[(J[b+28>>2]+P(J[a+124>>2],12)|0)+8>>2]=j+Q(w*Q(Q(q*e)-Q(d*o)));}function Yf(a,b){a=a|0;b=b|0;var c=Q(0),d=0,e=Q(0),f=Q(0),g=Q(0),h=0,i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0),v=Q(0);e=N[a+92>>2];c=Q(N[a+88>>2]-N[a+84>>2]);k=N[a+104>>2];d=J[b+28>>2];h=d+P(J[a+100>>2],12)|0;l=N[h>>2];i=N[h+8>>2];m=N[a+124>>2];d=d+P(J[a+96>>2],12)|0;n=N[d>>2];j=N[d+8>>2];o=N[a+116>>2];p=N[h+4>>2];q=N[a+120>>2];f=N[d+4>>2];r=N[a+112>>2];s=N[a+108>>2];g=Q(Q(k*Q(Q(l-Q(i*m))-Q(n-Q(j*o))))+Q(Q(Q(p+Q(i*q))-Q(f+Q(j*r)))*s));c=Q(e-Q(N[a+160>>2]*(c<Q(0)?Q(Q(N[b+4>>2]*c)+g):g)));c=c>Q(0)?Q(0):c;N[a+92>>2]=c;t=N[a+156>>2];g=N[a+148>>2];u=N[a+152>>2];v=f;f=N[a+144>>2];c=Q(c-e);e=Q(s*c);N[d+4>>2]=v-Q(f*e);c=Q(k*c);N[d>>2]=n-Q(f*c);d=J[b+28>>2];N[(d+P(J[a+96>>2],12)|0)+8>>2]=j-Q(u*Q(Q(r*e)-Q(c*o)));d=d+P(J[a+100>>2],12)|0;N[d+4>>2]=p+Q(g*e);N[d>>2]=l+Q(g*c);N[(J[b+28>>2]+P(J[a+100>>2],12)|0)+8>>2]=i+Q(t*Q(Q(q*e)-Q(c*m)));}function ei(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=0,m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0);l=J[a+148>>2];if((l|0)<=0){return 0}g=N[d+4>>2];f=Q(N[c+12>>2]-g);h=N[d>>2];i=Q(N[c+8>>2]-h);j=N[d+12>>2];k=Q(N[c+4>>2]-g);g=N[d+8>>2];h=Q(N[c>>2]-h);m=Q(Q(j*k)-Q(g*h));p=Q(Q(Q(j*f)-Q(g*i))-m);n=Q(Q(j*h)+Q(k*g));q=Q(Q(Q(j*i)+Q(g*f))-n);r=Q(-g);k=N[c+16>>2];e=-1;d=0;h=Q(0);a:{while(1){c=(d<<3)+a|0;f=N[c+84>>2];o=N[c+88>>2];i=Q(Q(f*Q(N[c+20>>2]-n))+Q(Q(N[c+24>>2]-m)*o));f=Q(Q(f*q)+Q(p*o));b:{if(f==Q(0)){if(!(i<Q(0))){break b}return 0}if(!(!(f<Q(0))|!(i<Q(h*f)))){h=Q(i/f);e=d;break b}if(!(f>Q(0))|!(i<Q(k*f))){break b}k=Q(i/f);}if(!(h>k)){d=d+1|0;if((l|0)==(d|0)){break a}continue}break}return 0}if((e|0)>=0){N[b+8>>2]=h;a=(e<<3)+a|0;f=N[a+84>>2];h=Q(g*f);g=N[a+88>>2];N[b+4>>2]=h+Q(j*g);N[b>>2]=Q(j*f)+Q(g*r);a=1;}else {a=0;}return a|0}function ii(a,b,c){a=a|0;b=b|0;c=c|0;var d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0),i=0,j=Q(0),k=0;g=N[c+12>>2];f=N[c+4>>2];d=N[c>>2];e=Q(N[c+8>>2]-d);a:{b:{if((e>Q(0)?e:Q(-e))<Q(1.1920928955078125e-7)){if(d<N[a>>2]|d>N[a+8>>2]){break a}e=Q(34028234663852886e22);d=Q(-34028234663852886e22);break b}e=Q(Q(1)/e);h=Q(e*Q(N[a+8>>2]-d));d=Q(e*Q(N[a>>2]-d));i=h<d;e=i?d:h;e=e>Q(34028234663852886e22)?Q(34028234663852886e22):e;d=i?h:d;d=d>Q(-34028234663852886e22)?d:Q(-34028234663852886e22);if(e<d){break a}h=i?Q(1):Q(-1);}g=Q(g-f);c:{if(!((g>Q(0)?g:Q(-g))<Q(1.1920928955078125e-7))){j=Q(Q(1)/g);g=Q(j*Q(N[a+12>>2]-f));f=Q(j*Q(N[a+4>>2]-f));a=g<f;j=a?g:f;i=j>d;d=i?j:d;f=a?f:g;if(d>(e<f?e:f)){break a}e=i?a?Q(1):Q(-1):Q(0);h=i?Q(0):h;break c}if(f<N[a+4>>2]){break a}e=Q(0);if(f>N[a+12>>2]){break a}}if(d<Q(0)|N[c+16>>2]<d){break a}N[b+4>>2]=e;N[b>>2]=h;N[b+8>>2]=d;k=1;}return k|0}function Ph(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;var f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0);i=N[d+4>>2];f=Q(N[c+12>>2]-i);g=N[d>>2];h=Q(N[c+8>>2]-g);j=N[d+12>>2];i=Q(N[c+4>>2]-i);k=N[d+8>>2];g=Q(N[c>>2]-g);m=Q(Q(j*i)-Q(k*g));r=Q(Q(Q(j*f)-Q(k*h))-m);n=Q(Q(j*g)+Q(i*k));s=Q(Q(Q(j*h)+Q(k*f))-n);o=N[a+12>>2];l=Q(N[a+20>>2]-o);i=Q(-l);d=0;p=N[a+16>>2];h=Q(N[a+24>>2]-p);q=Q(Q(h*h)+Q(l*l));f=Q(Y(q));a:{if(f<Q(1.1920928955078125e-7)){f=h;break a}f=Q(Q(1)/f);i=Q(f*i);f=Q(h*f);}g=Q(Q(f*s)+Q(r*i));b:{if(g==Q(0)){break b}t=Q(Q(f*Q(o-n))+Q(Q(p-m)*i));g=Q(t/g);if(g<Q(0)|g>N[c+16>>2]|q==Q(0)){break b}h=Q(Q(Q(Q(Q(n+Q(s*g))-o)*l)+Q(h*Q(Q(m+Q(r*g))-p)))/q);if(h<Q(0)|h>Q(1)){break b}N[b+8>>2]=g;h=Q(Q(k*f)+Q(j*i));a=t>Q(0);N[b+4>>2]=a?Q(-h):h;f=Q(Q(j*f)+Q(i*Q(-k)));N[b>>2]=a?Q(-f):f;d=1;}return d|0}function Ya(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;if(!b){return 0}if((b|0)>=641){return _a(b)}d=K[b+23620|0];g=(d<<2)+a|0;b=J[g+12>>2];if(b){J[g+12>>2]=J[b>>2];return b}b=J[a+4>>2];if((b|0)==J[a+8>>2]){c=b+128|0;J[a+8>>2]=c;b=J[a>>2];c=_a(c<<3);J[a>>2]=c;eb(c,b,J[a+4>>2]<<3);xb(J[a>>2]+(J[a+4>>2]<<3)|0,0,1024);Wa(b);b=J[a+4>>2];}h=J[a>>2]+(b<<3)|0;c=_a(16384);J[h+4>>2]=c;d=J[(d<<2)+12640>>2];J[h>>2]=d;e=16384/(d|0)|0;i=e-1|0;a:{if((e|0)<2){break a}b=0;if(e-2>>>0>=3){l=i&-4;e=0;while(1){f=c+P(d,b|1)|0;J[c+P(b,d)>>2]=f;j=f;f=c+P(d,b|2)|0;J[j>>2]=f;j=f;f=c+P(d,b|3)|0;J[j>>2]=f;b=b+4|0;J[f>>2]=c+P(d,b);e=e+4|0;if((l|0)!=(e|0)){continue}break}}e=i&3;if(!e){break a}while(1){f=c+P(b,d)|0;b=b+1|0;J[f>>2]=c+P(d,b);k=k+1|0;if((e|0)!=(k|0)){continue}break}}J[c+P(d,i)>>2]=0;J[g+12>>2]=J[c>>2];J[a+4>>2]=J[a+4>>2]+1;return J[h+4>>2]}function kl(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=0,j=0,k=0,l=0;a:{b:{c:{d:{e:{f:{g:{h:{i:{j:{if(b){if(!c){break j}break i}Ga=0;a=(a>>>0)/(c>>>0)|0;break a}if(!a){break h}break g}if(!(c-1&c)){break f}f=(S(c)+33|0)-S(b)|0;g=0-f|0;break d}Ga=0;a=(b>>>0)/0|0;break a}d=32-S(b)|0;if(d>>>0<31){break e}break c}if((c|0)==1){break b}f=il(c);c=f&31;if((f&63)>>>0>=32){a=b>>>c|0;}else {d=b>>>c|0;a=((1<<c)-1&b)<<32-c|a>>>c;}Ga=d;break a}f=d+1|0;g=63-d|0;}d=f&63;e=d&31;if(d>>>0>=32){d=0;h=b>>>e|0;}else {d=b>>>e|0;h=((1<<e)-1&b)<<32-e|a>>>e;}g=g&63;e=g&31;if(g>>>0>=32){b=a<<e;a=0;}else {b=(1<<e)-1&a>>>32-e|b<<e;a=a<<e;}if(f){g=c-1|0;l=(g|0)==-1?-1:0;while(1){i=d<<1|h>>>31;d=h<<1|b>>>31;e=l-(i+(d>>>0>g>>>0)|0)>>31;j=c&e;h=d-j|0;d=i-(d>>>0<j>>>0)|0;b=b<<1|a>>>31;a=k|a<<1;k=e&1;f=f-1|0;if(f){continue}break}}Ga=b<<1|a>>>31;a=k|a<<1;break a}a=0;b=0;}Ga=b;}return a}function Wc(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0;a:{if(K[a+102989|0]){break a}g=K[b+61|0];c=J[b+8>>2];if(c){J[c+12>>2]=J[b+12>>2];}d=J[b+12>>2];if(d){J[d+8>>2]=c;}if(J[a+102952>>2]==(b|0)){J[a+102952>>2]=d;}c=J[b+52>>2];d=J[b+48>>2];J[d+144>>2]=0;I[d+4>>1]=L[d+4>>1]|2;J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;e=J[b+24>>2];if(e){J[e+12>>2]=J[b+28>>2];}f=J[b+28>>2];if(f){J[f+8>>2]=e;}if(J[d+108>>2]==(b+16|0)){J[d+108>>2]=f;}J[b+24>>2]=0;J[b+28>>2]=0;e=J[b+40>>2];if(e){J[e+12>>2]=J[b+44>>2];}f=J[b+44>>2];if(f){J[f+8>>2]=e;}if(J[c+108>>2]==(b+32|0)){J[c+108>>2]=f;}J[b+40>>2]=0;J[b+44>>2]=0;Ha[J[J[b>>2]+24>>2]](b)|0;e=J[b+4>>2]-1|0;if(e>>>0<=10){Bb(a,b,J[(e<<2)+13480>>2]);}J[a+102960>>2]=J[a+102960>>2]-1;if(g){break a}b=J[c+112>>2];if(!b){break a}while(1){if((d|0)==J[b>>2]){a=J[b+4>>2];J[a+4>>2]=J[a+4>>2]|8;}b=J[b+12>>2];if(b){continue}break}}}function Gh(a,b){a=a|0;b=b|0;var c=0,d=Q(0),e=Q(0),f=Q(0),g=0,h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0),u=Q(0);f=N[a+100>>2];l=N[a+116>>2];c=J[b+28>>2];g=c+P(J[a+112>>2],12)|0;m=N[g>>2];h=N[g+8>>2];n=N[a+136>>2];c=c+P(J[a+108>>2],12)|0;o=N[c>>2];i=N[c+8>>2];p=N[a+128>>2];e=N[a+120>>2];q=N[g+4>>2];r=N[a+132>>2];d=N[c+4>>2];s=N[a+124>>2];j=Q(Q(Q(f*N[a+96>>2])+Q(N[a+76>>2]+Q(Q(l*Q(Q(m-Q(h*n))-Q(o-Q(i*p))))+Q(e*Q(Q(q+Q(h*r))-Q(d+Q(i*s)))))))*Q(-N[a+172>>2]));N[a+100>>2]=f+j;u=N[a+168>>2];t=N[a+160>>2];f=N[a+164>>2];k=d;d=N[a+156>>2];e=Q(e*j);N[c+4>>2]=k-Q(d*e);k=d;d=Q(l*j);N[c>>2]=o-Q(k*d);c=J[b+28>>2];N[(c+P(J[a+108>>2],12)|0)+8>>2]=i-Q(f*Q(Q(s*e)-Q(d*p)));c=c+P(J[a+112>>2],12)|0;N[c+4>>2]=q+Q(e*t);N[c>>2]=m+Q(d*t);N[(J[b+28>>2]+P(J[a+112>>2],12)|0)+8>>2]=h+Q(u*Q(Q(r*e)-Q(d*n)));}function ud(a){a=a|0;var b=0,c=Q(0),d=0;b=Fa-224|0;Fa=b;d=J[a+8>>2];Sa(7074,0);Sa(8318,0);J[b+208>>2]=J[a>>2];Sa(9758,b+208|0);c=N[a+12>>2];O[b+200>>3]=N[a+16>>2];O[b+192>>3]=c;Sa(8738,b+192|0);O[b+176>>3]=N[a+56>>2];Sa(7653,b+176|0);c=N[a+64>>2];O[b+168>>3]=N[a+68>>2];O[b+160>>3]=c;Sa(8520,b+160|0);O[b+144>>3]=N[a+72>>2];Sa(7171,b+144|0);O[b+128>>3]=N[a+132>>2];Sa(7563,b+128|0);O[b+112>>3]=N[a+136>>2];Sa(7531,b+112|0);J[b+96>>2]=L[a+4>>1]&4;Sa(9540,b+96|0);J[b+80>>2]=L[a+4>>1]&2;Sa(9601,b+80|0);J[b+64>>2]=L[a+4>>1]&16;Sa(9569,b- -64|0);J[b+48>>2]=L[a+4>>1]&8;Sa(9456,b+48|0);J[b+32>>2]=L[a+4>>1]&32;Sa(9660,b+32|0);O[b+16>>3]=N[a+140>>2];Sa(7764,b+16|0);J[b>>2]=J[a+8>>2];Sa(9306,b);Sa(10169,0);a=J[a+100>>2];if(a){while(1){Sa(7072,0);md(a,d);Sa(7067,0);a=J[a+4>>2];if(a){continue}break}}Sa(7069,0);Fa=b+224|0;}function Vc(){Ea(18868,5355);Da(18892,3614,1,1,0);ha(18904,2966,1,-128,127);ha(18928,2959,1,-128,127);ha(18916,2957,1,0,255);ha(18940,1622,2,-32768,32767);ha(18952,1613,2,0,65535);ha(18964,2035,4,-2147483648,2147483647);ha(18976,2026,4,0,-1);ha(18988,3849,4,-2147483648,2147483647);ha(19e3,3840,4,0,-1);Ic(19012,2331,-2147483648,2147483647);Ic(19024,2330,0,-1);sa(19036,2324,4);sa(19048,5069,8);ra(20352,3867);ra(14396,6818);pa(14468,4,3854);pa(14544,2,3879);pa(14620,4,3894);Ca(19940,3636);ea(14660,0,6749);ea(14700,0,6851);ea(14740,1,6779);ea(14780,2,6314);ea(14820,3,6345);ea(14860,4,6385);ea(14900,5,6414);ea(14940,4,6888);ea(14980,5,6918);ea(14700,0,6516);ea(14740,1,6483);ea(14780,2,6582);ea(14820,3,6548);ea(14860,4,6716);ea(14900,5,6682);ea(15020,8,6649);ea(15060,9,6615);ea(15100,6,6452);ea(15140,7,6957);}function nf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0;f=Fa-32|0;Fa=f;d=J[a+28>>2];J[f+16>>2]=d;g=J[a+20>>2];J[f+28>>2]=c;J[f+24>>2]=b;b=g-d|0;J[f+20>>2]=b;g=b+c|0;i=2;a:{b:{b=f+16|0;d=qa(J[a+60>>2],b|0,2,f+12|0)|0;if(d){J[6386]=d;d=-1;}else {d=0;}c:{d:{if(d){d=b;break d}while(1){e=J[f+12>>2];if((e|0)==(g|0)){break c}if((e|0)<0){d=b;break b}h=J[b+4>>2];j=h>>>0<e>>>0;d=(j<<3)+b|0;h=e-(j?h:0)|0;J[d>>2]=h+J[d>>2];b=(j?12:4)+b|0;J[b>>2]=J[b>>2]-h;g=g-e|0;b=d;i=i-j|0;e=qa(J[a+60>>2],b|0,i|0,f+12|0)|0;if(e){J[6386]=e;e=-1;}else {e=0;}if(!e){continue}break}}if((g|0)!=-1){break b}}b=J[a+44>>2];J[a+28>>2]=b;J[a+20>>2]=b;J[a+16>>2]=b+J[a+48>>2];a=c;break a}J[a+28>>2]=0;J[a+16>>2]=0;J[a+20>>2]=0;J[a>>2]=J[a>>2]|32;a=0;if((i|0)==2){break a}a=c-J[d+4>>2]|0;}Fa=f+32|0;return a|0}function Ua(a){var b=Q(0),c=0,d=0,e=0,f=0;c=Fa-16|0;Fa=c;e=(C(a),v(2));d=e&2147483647;a:{if(d>>>0<=1061752794){b=Q(1);if(d>>>0<964689920){break a}b=ob(+a);break a}if(d>>>0<=1081824209){if(d>>>0>=1075235812){b=Q(-ob(((e|0)<0?3.141592653589793:-3.141592653589793)+ +a));break a}f=+a;if((e|0)<0){b=nb(f+1.5707963267948966);break a}b=nb(1.5707963267948966-f);break a}if(d>>>0<=1088565717){if(d>>>0>=1085271520){b=ob(((e|0)<0?6.283185307179586:-6.283185307179586)+ +a);break a}if((e|0)<0){b=nb(-4.71238898038469-+a);break a}b=nb(+a+-4.71238898038469);break a}b=Q(a-a);if(d>>>0>=2139095040){break a}b:{switch(Uc(a,c+8|0)&3){case 0:b=ob(O[c+8>>3]);break a;case 1:b=nb(-O[c+8>>3]);break a;case 2:b=Q(-ob(O[c+8>>3]));break a;default:break b}}b=nb(O[c+8>>3]);}a=b;Fa=c+16|0;return a}function nd(a,b,c){var d=0,e=0,f=0,g=0,h=0,i=Q(0);e=J[a+12>>2];e=Ha[J[J[e>>2]+12>>2]](e)|0;J[a+28>>2]=e;if((e|0)>0){while(1){g=J[a+12>>2];e=J[a+24>>2]+P(h,28)|0;Ha[J[J[g>>2]+24>>2]](g,e,c,h);g=Ad(b);i=N[e>>2];d=P(g,40);f=d+J[b+4>>2]|0;N[f+4>>2]=N[e+4>>2]+Q(-.10000000149011612);N[f>>2]=i+Q(-.10000000149011612);i=N[e+12>>2];f=d+J[b+4>>2]|0;N[f+8>>2]=N[e+8>>2]+Q(.10000000149011612);N[f+12>>2]=i+Q(.10000000149011612);d=d+J[b+4>>2]|0;H[d+36|0]=1;J[d+32>>2]=0;J[d+16>>2]=e;zd(b,g);J[b+28>>2]=J[b+28>>2]+1;d=J[b+40>>2];if((d|0)==J[b+36>>2]){J[b+36>>2]=d<<1;f=J[b+32>>2];d=_a(d<<3);J[b+32>>2]=d;eb(d,f,J[b+40>>2]<<2);Wa(f);d=J[b+40>>2];}J[J[b+32>>2]+(d<<2)>>2]=g;J[b+40>>2]=J[b+40>>2]+1;J[e+20>>2]=h;J[e+16>>2]=a;J[e+24>>2]=g;h=h+1|0;if((h|0)<J[a+28>>2]){continue}break}}}function Ta(a){var b=0,c=0,d=0,e=0;b=Fa-16|0;Fa=b;e=(C(a),v(2));c=e&2147483647;a:{if(c>>>0<=1061752794){if(c>>>0<964689920){break a}a=nb(+a);break a}if(c>>>0<=1081824209){d=+a;if(c>>>0<=1075235811){if((e|0)<0){a=Q(-ob(d+1.5707963267948966));break a}a=ob(d+-1.5707963267948966);break a}a=nb(-(((e|0)>=0?-3.141592653589793:3.141592653589793)+d));break a}if(c>>>0<=1088565717){if(c>>>0<=1085271519){d=+a;if((e|0)<0){a=ob(d+4.71238898038469);break a}a=Q(-ob(d+-4.71238898038469));break a}a=nb(((e|0)<0?6.283185307179586:-6.283185307179586)+ +a);break a}if(c>>>0>=2139095040){a=Q(a-a);break a}b:{switch(Uc(a,b+8|0)&3){case 0:a=nb(O[b+8>>3]);break a;case 1:a=ob(O[b+8>>3]);break a;case 2:a=nb(-O[b+8>>3]);break a;default:break b}}a=Q(-ob(O[b+8>>3]));}Fa=b+16|0;return a}function rf(a){a=a|0;var b=0,c=0,d=0,e=0,f=Q(0);c=Fa-48|0;Fa=c;if(!K[a+102989|0]){f=N[a+102968>>2];O[c+32>>3]=N[a+102964>>2];O[c+40>>3]=f;Sa(9197,c+32|0);Sa(8495,0);J[c+16>>2]=J[a+102956>>2];Sa(9962,c+16|0);J[c>>2]=J[a+102960>>2];Sa(10022,c);b=J[a+102948>>2];if(b){while(1){J[b+8>>2]=e;ud(b);e=e+1|0;b=J[b+96>>2];if(b){continue}break}}d=J[a+102952>>2];a:{if(!d){break a}b=0;e=d;while(1){J[e+56>>2]=b;b=b+1|0;e=J[e+12>>2];if(e){continue}break}if(!d){break a}while(1){if(J[d+4>>2]!=6){Sa(7074,0);Ha[J[J[d>>2]+16>>2]](d);Sa(7069,0);}d=J[d+12>>2];if(d){continue}break}b=J[a+102952>>2];if(!b){break a}while(1){if(J[b+4>>2]==6){Sa(7074,0);Ha[J[J[b>>2]+16>>2]](b);Sa(7069,0);}b=J[b+12>>2];if(b){continue}break}}Sa(8461,0);Sa(8478,0);Sa(7077,0);Sa(7096,0);}Fa=c+48|0;}function Ib(a,b){var c=0,d=0,e=0,f=0;f=J[J[b+52>>2]+8>>2];d=J[J[b+48>>2]+8>>2];c=J[a+72>>2];if(!(!c|!(K[b+4|0]&2))){Ha[J[J[c>>2]+12>>2]](c,b);}c=J[b+8>>2];if(c){J[c+12>>2]=J[b+12>>2];}e=J[b+12>>2];if(e){J[e+8>>2]=c;}if(J[a+60>>2]==(b|0)){J[a+60>>2]=e;}c=J[b+24>>2];if(c){J[c+12>>2]=J[b+28>>2];}e=J[b+28>>2];if(e){J[e+8>>2]=c;}if(J[d+112>>2]==(b+16|0)){J[d+112>>2]=e;}d=J[b+40>>2];if(d){J[d+12>>2]=J[b+44>>2];}c=J[b+44>>2];if(c){J[c+8>>2]=d;}if(J[f+112>>2]==(b+32|0)){J[f+112>>2]=c;}e=J[a+76>>2];f=J[b+52>>2];d=J[b+48>>2];if(!(K[f+38|0]|(K[d+38|0]|J[b+124>>2]<=0))){c=J[d+8>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;c=J[f+8>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;}Ha[J[((P(J[J[d+12>>2]+4>>2],48)+24272|0)+P(J[J[f+12>>2]+4>>2],12)|0)+4>>2]](b,e);J[a+64>>2]=J[a+64>>2]-1;}function pe(a,b,c,d,e,f){a=a|0;b=Q(b);c=Q(c);d=Q(d);e=Q(e);f=Q(f);var g=0,h=0,i=0,j=Q(0),k=Q(0);h=Fa-16|0;Fa=h;N[h+12>>2]=e;N[h+8>>2]=d;J[a+84>>2]=0;J[a+88>>2]=-1082130432;J[a+148>>2]=4;J[a+108>>2]=-1082130432;J[a+112>>2]=0;J[a+100>>2]=0;J[a+104>>2]=1065353216;J[a+92>>2]=1065353216;J[a+96>>2]=0;N[a+48>>2]=c;d=Q(-b);N[a+44>>2]=d;N[a+40>>2]=c;N[a+36>>2]=b;c=Q(-c);N[a+32>>2]=c;N[a+28>>2]=b;N[a+24>>2]=c;N[a+20>>2]=d;g=J[h+12>>2];J[a+12>>2]=J[h+8>>2];J[a+16>>2]=g;c=Ta(f);d=Q(-c);j=N[h+12>>2];k=N[h+8>>2];b=Ua(f);while(1){g=(i<<3)+a|0;e=N[g+84>>2];f=N[g+88>>2];N[g+84>>2]=Q(b*e)+Q(f*d);N[g+88>>2]=Q(c*e)+Q(b*f);e=N[g+20>>2];f=N[g+24>>2];N[g+24>>2]=j+Q(Q(c*e)+Q(b*f));N[g+20>>2]=k+Q(Q(b*e)+Q(f*d));i=i+1|0;if((i|0)<J[a+148>>2]){continue}break}Fa=h+16|0;}function zf(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0;if(!K[a+102989|0]){c=J[b+108>>2];if(c){while(1){d=J[c+12>>2];e=J[a+102976>>2];if(e){Ha[J[J[e>>2]+8>>2]](e,J[c+4>>2]);}Wc(a,J[c+4>>2]);J[b+108>>2]=d;c=d;if(c){continue}break}}J[b+108>>2]=0;c=J[b+112>>2];if(c){e=a+102868|0;while(1){d=J[c+12>>2];Ib(e,J[c+4>>2]);c=d;if(c){continue}break}}J[b+112>>2]=0;c=J[b+100>>2];if(c){f=a+102868|0;while(1){d=J[c+4>>2];e=J[a+102976>>2];if(e){Ha[J[J[e>>2]+12>>2]](e,c);}pc(c,f);qc(c,a);Bb(a,c,44);J[b+100>>2]=d;J[b+104>>2]=J[b+104>>2]-1;c=d;if(c){continue}break}}J[b+100>>2]=0;J[b+104>>2]=0;c=J[b+92>>2];if(c){J[c+96>>2]=J[b+96>>2];}d=J[b+96>>2];if(d){J[d+92>>2]=c;}if(J[a+102948>>2]==(b|0)){J[a+102948>>2]=d;}J[a+102956>>2]=J[a+102956>>2]-1;Bb(a,b,152);}}function ug(a){a=a|0;var b=0,c=Q(0),d=0,e=0;b=Fa-224|0;Fa=b;d=J[J[a+52>>2]+8>>2];e=J[J[a+48>>2]+8>>2];Sa(8269,0);J[b+208>>2]=e;Sa(8435,b+208|0);J[b+192>>2]=d;Sa(8409,b+192|0);J[b+176>>2]=K[a+61|0];Sa(9625,b+176|0);c=N[a+68>>2];O[b+168>>3]=N[a+72>>2];O[b+160>>3]=c;Sa(8936,b+160|0);c=N[a+76>>2];O[b+152>>3]=N[a+80>>2];O[b+144>>3]=c;Sa(8811,b+144|0);c=N[a+84>>2];O[b+136>>3]=N[a+88>>2];O[b+128>>3]=c;Sa(8896,b+128|0);O[b+112>>3]=N[a+100>>2];Sa(7732,b+112|0);J[b+96>>2]=K[a+136|0];Sa(9426,b+96|0);O[b+80>>3]=N[a+120>>2];Sa(7412,b+80|0);O[b+64>>3]=N[a+124>>2];Sa(7446,b- -64|0);J[b+48>>2]=K[a+137|0];Sa(9481,b+48|0);O[b+32>>3]=N[a+132>>2];Sa(7851,b+32|0);O[b+16>>3]=N[a+128>>2];Sa(7820,b+16|0);J[b>>2]=J[a+56>>2];Sa(9226,b);Fa=b+224|0;}function li(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0);e=0;k=N[c>>2];i=Q(N[c+8>>2]-k);l=N[c+4>>2];f=Q(N[c+12>>2]-l);h=Q(Q(i*i)+Q(f*f));a:{if(h<Q(1.1920928955078125e-7)){break a}g=N[d+12>>2];j=N[a+12>>2];n=N[a+16>>2];m=N[d+8>>2];k=Q(k-Q(N[d>>2]+Q(Q(g*j)-Q(n*m))));l=Q(l-Q(N[d+4>>2]+Q(Q(m*j)+Q(g*n))));g=Q(Q(k*i)+Q(l*f));j=N[a+8>>2];j=Q(Q(g*g)-Q(Q(Q(Q(k*k)+Q(l*l))-Q(j*j))*h));if(j<Q(0)){break a}g=Q(g+Q(Y(j)));if(!(g<=Q(0))){break a}g=Q(-g);if(!(g<=Q(h*N[c+16>>2]))){break a}h=Q(g/h);N[b+8>>2]=h;f=Q(l+Q(f*h));N[b+4>>2]=f;i=Q(k+Q(i*h));N[b>>2]=i;e=1;h=Q(Y(Q(Q(i*i)+Q(f*f))));if(h<Q(1.1920928955078125e-7)){break a}m=f;f=Q(Q(1)/h);N[b+4>>2]=m*f;N[b>>2]=i*f;}return e|0}function Pc(a,b,c){a:{switch(b-9|0){case 0:b=J[c>>2];J[c>>2]=b+4;J[a>>2]=J[b>>2];return;case 6:b=J[c>>2];J[c>>2]=b+4;b=I[b>>1];J[a>>2]=b;J[a+4>>2]=b>>31;return;case 7:b=J[c>>2];J[c>>2]=b+4;J[a>>2]=L[b>>1];J[a+4>>2]=0;return;case 8:b=J[c>>2];J[c>>2]=b+4;b=H[b|0];J[a>>2]=b;J[a+4>>2]=b>>31;return;case 9:b=J[c>>2];J[c>>2]=b+4;J[a>>2]=K[b|0];J[a+4>>2]=0;return;case 16:b=J[c>>2]+7&-8;J[c>>2]=b+8;O[a>>3]=O[b>>3];return;case 17:Nc(a,c);default:return;case 1:case 4:case 14:b=J[c>>2];J[c>>2]=b+4;b=J[b>>2];J[a>>2]=b;J[a+4>>2]=b>>31;return;case 2:case 5:case 11:case 15:b=J[c>>2];J[c>>2]=b+4;J[a>>2]=J[b>>2];J[a+4>>2]=0;return;case 3:case 10:case 12:case 13:break a}}b=J[c>>2]+7&-8;J[c>>2]=b+8;c=J[b+4>>2];J[a>>2]=J[b>>2];J[a+4>>2]=c;}function lb(a,b){var c=0,d=0,e=0;c=Fa+-64|0;Fa=c;d=J[a>>2];e=J[d-4>>2];d=J[d-8>>2];J[c+32>>2]=0;J[c+36>>2]=0;J[c+40>>2]=0;J[c+44>>2]=0;J[c+48>>2]=0;J[c+52>>2]=0;H[c+55|0]=0;H[c+56|0]=0;H[c+57|0]=0;H[c+58|0]=0;H[c+59|0]=0;H[c+60|0]=0;H[c+61|0]=0;H[c+62|0]=0;J[c+24>>2]=0;J[c+28>>2]=0;J[c+20>>2]=0;J[c+16>>2]=18516;J[c+12>>2]=a;J[c+8>>2]=b;a=a+d|0;d=0;a:{if(Za(e,b,0)){J[c+56>>2]=1;Ha[J[J[e>>2]+20>>2]](e,c+8|0,a,a,1,0);d=J[c+32>>2]==1?a:0;break a}Ha[J[J[e>>2]+24>>2]](e,c+8|0,a,1,0);b:{switch(J[c+44>>2]){case 0:d=J[c+48>>2]==1?J[c+36>>2]==1?J[c+40>>2]==1?J[c+28>>2]:0:0:0;break a;case 1:break b;default:break a}}if(J[c+32>>2]!=1){if(J[c+48>>2]|J[c+36>>2]!=1|J[c+40>>2]!=1){break a}}d=J[c+24>>2];}Fa=c- -64|0;return d}function Wg(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=Q(0),f=Q(0),g=0,h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0);g=J[b+28>>2]+P(J[a+116>>2],12)|0;j=N[g>>2];h=N[g+8>>2];k=N[a+124>>2];c=N[a+108>>2];i=N[a+96>>2];d=Q(-Q(Q(Q(j-Q(h*k))+N[a+160>>2])+Q(c*i)));l=N[g+4>>2];m=N[a+120>>2];f=N[a+100>>2];e=Q(Q(Q(l+Q(h*m))+N[a+164>>2])+Q(c*f));c=Q(f+Q(Q(N[a+148>>2]*d)-Q(N[a+156>>2]*e)));N[a+100>>2]=c;d=Q(i+Q(Q(N[a+144>>2]*d)-Q(N[a+152>>2]*e)));N[a+96>>2]=d;n=Q(Q(d*d)+Q(c*c));e=Q(N[b>>2]*N[a+104>>2]);if(n>Q(e*e)){e=Q(e/Q(Y(n)));c=Q(c*e);N[a+100>>2]=c;d=Q(d*e);N[a+96>>2]=d;}e=N[a+140>>2];c=Q(c-f);f=N[a+136>>2];N[g+4>>2]=l+Q(c*f);d=Q(d-i);N[g>>2]=j+Q(f*d);N[(J[b+28>>2]+P(J[a+116>>2],12)|0)+8>>2]=Q(e*Q(Q(m*c)-Q(d*k)))+h;}function xb(a,b,c){var d=0,e=0;a:{if(!c){break a}H[a|0]=b;d=a+c|0;H[d-1|0]=b;if(c>>>0<3){break a}H[a+2|0]=b;H[a+1|0]=b;H[d-3|0]=b;H[d-2|0]=b;if(c>>>0<7){break a}H[a+3|0]=b;H[d-4|0]=b;if(c>>>0<9){break a}d=0-a&3;e=d+a|0;a=P(b&255,16843009);J[e>>2]=a;c=c-d&-4;b=c+e|0;J[b-4>>2]=a;if(c>>>0<9){break a}J[e+8>>2]=a;J[e+4>>2]=a;J[b-8>>2]=a;J[b-12>>2]=a;if(c>>>0<25){break a}J[e+24>>2]=a;J[e+20>>2]=a;J[e+16>>2]=a;J[e+12>>2]=a;J[b-16>>2]=a;J[b-20>>2]=a;J[b-24>>2]=a;J[b-28>>2]=a;b=e&4|24;c=c-b|0;if(c>>>0<32){break a}a=jl(a,0,1,1);d=Ga;b=b+e|0;while(1){J[b+24>>2]=a;J[b+28>>2]=d;J[b+16>>2]=a;J[b+20>>2]=d;J[b+8>>2]=a;J[b+12>>2]=d;J[b>>2]=a;J[b+4>>2]=d;b=b+32|0;c=c-32|0;if(c>>>0>31){continue}break}}}function Dg(a){a=a|0;var b=0,c=0,d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0);b=J[a+52>>2];f=N[b+20>>2];g=Q(N[a+76>>2]-N[b+28>>2]);i=N[b+24>>2];j=Q(N[a+80>>2]-N[b+32>>2]);k=Q(Q(f*g)+Q(i*j));c=J[a+48>>2];d=N[c+20>>2];l=Q(N[a+68>>2]-N[c+28>>2]);e=N[c+24>>2];m=Q(N[a+72>>2]-N[c+32>>2]);n=Q(Q(d*l)+Q(e*m));o=N[a+84>>2];p=N[a+88>>2];q=Q(Q(e*o)-Q(p*d));h=N[c+72>>2];f=Q(Q(i*g)-Q(j*f));g=Q(Q(e*l)-Q(m*d));d=Q(Q(d*o)+Q(e*p));e=N[b+72>>2];return Q(Q(Q(Q(Q(Q(k+N[b+48>>2])-Q(n+N[c+48>>2]))*Q(q*h))-Q(Q(Q(f+N[b+44>>2])-Q(g+N[c+44>>2]))*Q(d*h)))+Q(Q(q*Q(Q(n*h)+Q(Q(N[b+64>>2]-Q(k*e))-N[c+64>>2])))+Q(d*Q(Q(Q(N[b+68>>2]+Q(f*e))-N[c+68>>2])-Q(g*h))))))}function Wj(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=0,i=Q(0),j=Q(0);if(!(K[J[a+88>>2]+102989|0]|J[a>>2]!=2)){J[a+124>>2]=0;J[a+128>>2]=0;c=N[b>>2];c=c<=Q(0)?Q(1):c;N[a+116>>2]=c;N[a+120>>2]=Q(1)/c;e=N[b+12>>2];if(!(!(e>Q(0))|K[a+4|0]&16)){f=c;c=N[b+4>>2];d=Q(c*c);c=N[b+8>>2];c=Q(e-Q(f*Q(d+Q(c*c))));N[a+124>>2]=c;N[a+128>>2]=Q(1)/c;}f=N[b+8>>2];h=J[b+8>>2];c=N[b+4>>2];b=J[b+4>>2];J[a+28>>2]=b;J[a+32>>2]=h;i=N[a+48>>2];d=N[a+20>>2];g=N[a+24>>2];e=Q(Q(Q(d*c)+Q(g*f))+N[a+16>>2]);N[a+48>>2]=e;j=N[a+44>>2];c=Q(N[a+12>>2]+Q(Q(g*c)-Q(f*d)));N[a+44>>2]=c;N[a+40>>2]=e;N[a+36>>2]=c;d=N[a+72>>2];N[a+64>>2]=N[a+64>>2]-Q(d*Q(e-i));N[a+68>>2]=Q(d*Q(c-j))+N[a+68>>2];}}function $f(a){a=a|0;var b=0,c=Q(0),d=0,e=0;b=Fa-208|0;Fa=b;d=J[J[a+52>>2]+8>>2];e=J[J[a+48>>2]+8>>2];Sa(8173,0);J[b+192>>2]=e;Sa(8435,b+192|0);J[b+176>>2]=d;Sa(8409,b+176|0);J[b+160>>2]=K[a+61|0];Sa(9625,b+160|0);c=N[a+68>>2];O[b+152>>3]=N[a+72>>2];O[b+144>>3]=c;Sa(8936,b+144|0);c=N[a+76>>2];O[b+136>>3]=N[a+80>>2];O[b+128>>3]=c;Sa(8811,b+128|0);O[b+112>>3]=N[a+116>>2];Sa(7732,b+112|0);J[b+96>>2]=K[a+112|0];Sa(9426,b+96|0);O[b+80>>3]=N[a+120>>2];Sa(7676,b+80|0);O[b+64>>3]=N[a+124>>2];Sa(7704,b- -64|0);J[b+48>>2]=K[a+100|0];Sa(9481,b+48|0);O[b+32>>3]=N[a+108>>2];Sa(7851,b+32|0);O[b+16>>3]=N[a+104>>2];Sa(7621,b+16|0);J[b>>2]=J[a+56>>2];Sa(9226,b);Fa=b+208|0;}function Df(a){a=a|0;var b=0,c=Q(0),d=0,e=0;b=Fa-192|0;Fa=b;d=J[J[a+52>>2]+8>>2];e=J[J[a+48>>2]+8>>2];Sa(8150,0);J[b+176>>2]=e;Sa(8435,b+176|0);J[b+160>>2]=d;Sa(8409,b+160|0);J[b+144>>2]=K[a+61|0];Sa(9625,b+144|0);c=N[a+76>>2];O[b+136>>3]=N[a+80>>2];O[b+128>>3]=c;Sa(8936,b+128|0);c=N[a+84>>2];O[b+120>>3]=N[a+88>>2];O[b+112>>3]=c;Sa(8811,b+112|0);c=N[a+92>>2];O[b+104>>3]=N[a+96>>2];O[b+96>>3]=c;Sa(8896,b+96|0);J[b+80>>2]=K[a+128|0];Sa(9481,b+80|0);O[b+64>>3]=N[a+124>>2];Sa(7851,b- -64|0);O[b+48>>3]=N[a+120>>2];Sa(7621,b+48|0);O[b+32>>3]=N[a+68>>2];Sa(7115,b+32|0);O[b+16>>3]=N[a+72>>2];Sa(7323,b+16|0);J[b>>2]=J[a+56>>2];Sa(9226,b);Fa=b+192|0;}function og(a){a=a|0;var b=0,c=Q(0),d=0,e=0;b=Fa-176|0;Fa=b;d=J[J[a+52>>2]+8>>2];e=J[J[a+48>>2]+8>>2];Sa(8055,0);J[b+160>>2]=e;Sa(8435,b+160|0);J[b+144>>2]=d;Sa(8409,b+144|0);J[b+128>>2]=K[a+61|0];Sa(9625,b+128|0);c=N[a+68>>2];O[b+120>>3]=N[a+72>>2];O[b+112>>3]=c;Sa(8978,b+112|0);c=N[a+76>>2];O[b+104>>3]=N[a+80>>2];O[b+96>>3]=c;Sa(8853,b+96|0);c=N[a+92>>2];O[b+88>>3]=N[a+96>>2];O[b+80>>3]=c;Sa(8936,b+80|0);c=N[a+100>>2];O[b+72>>3]=N[a+104>>2];O[b+64>>3]=c;Sa(8811,b- -64|0);O[b+48>>3]=N[a+84>>2];Sa(7904,b+48|0);O[b+32>>3]=N[a+88>>2];Sa(7879,b+32|0);O[b+16>>3]=N[a+112>>2];Sa(7300,b+16|0);J[b>>2]=J[a+56>>2];Sa(9226,b);Fa=b+176|0;}function Sh(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=Q(0),f=0,g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0);f=J[J[a+48>>2]+12>>2];a=J[J[a+52>>2]+12>>2];J[b+60>>2]=0;e=N[d+12>>2];g=N[a+12>>2];h=N[a+16>>2];i=N[d+8>>2];j=N[c+12>>2];k=N[f+12>>2];l=N[f+16>>2];m=N[c+8>>2];n=Q(Q(N[d>>2]+Q(Q(e*g)-Q(h*i)))-Q(N[c>>2]+Q(Q(j*k)-Q(l*m))));e=Q(Q(Q(Q(i*g)+Q(e*h))+N[d+4>>2])-Q(Q(Q(m*k)+Q(j*l))+N[c+4>>2]));g=Q(Q(n*n)+Q(e*e));e=Q(N[f+8>>2]+N[a+8>>2]);if(!(g>Q(e*e))){J[b+56>>2]=0;c=J[f+12>>2];d=J[f+16>>2];J[b+60>>2]=1;J[b+40>>2]=0;J[b+44>>2]=0;J[b+48>>2]=c;J[b+52>>2]=d;c=J[a+12>>2];a=J[a+16>>2];J[b+16>>2]=0;J[b>>2]=c;J[b+4>>2]=a;}}function Vb(a,b,c){var d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=Q(0),q=Q(0),r=Q(0),s=Q(0),t=Q(0);l=N[b+8>>2];g=N[b+12>>2];h=N[b+28>>2];i=N[b+24>>2];d=N[b+16>>2];p=Q(Q(g*h)-Q(i*d));m=N[b>>2];j=N[b+32>>2];k=N[b+20>>2];q=Q(Q(d*j)-Q(h*k));n=N[b+4>>2];r=Q(Q(k*i)-Q(j*g));e=Q(Q(l*p)+Q(Q(m*q)+Q(n*r)));o=e!=Q(0)?Q(Q(1)/e):e;e=N[c+4>>2];f=N[c>>2];s=Q(l*Q(Q(g*e)-Q(f*d)));t=d;d=N[c+8>>2];N[a+8>>2]=o*Q(s+Q(Q(m*Q(Q(t*d)-Q(e*k)))+Q(n*Q(Q(k*f)-Q(d*g)))));N[a+4>>2]=o*Q(Q(l*Q(Q(f*h)-Q(i*e)))+Q(Q(m*Q(Q(e*j)-Q(h*d)))+Q(n*Q(Q(d*i)-Q(j*f)))));N[a>>2]=o*Q(Q(d*p)+Q(Q(f*q)+Q(r*e)));}function Ad(a){var b=0,c=0,d=0;b=J[a+4>>2];c=J[a+16>>2];if((c|0)==-1){c=J[a+12>>2];J[a+12>>2]=c<<1;c=_a(P(c,80));J[a+4>>2]=c;eb(c,b,P(J[a+8>>2],40));Wa(b);c=J[a+4>>2];b=J[a+8>>2];d=J[a+12>>2]-1|0;if((b|0)<(d|0)){while(1){d=P(b,40);b=b+1|0;J[(d+c|0)+20>>2]=b;c=J[a+4>>2];J[(d+c|0)+32>>2]=-1;d=J[a+12>>2]-1|0;if((d|0)>(b|0)){continue}break}}J[(P(d,40)+c|0)+20>>2]=-1;b=J[a+4>>2];J[(b+P(J[a+12>>2],40)|0)-8>>2]=-1;c=J[a+8>>2];J[a+16>>2]=c;}d=b;b=P(c,40);d=d+b|0;J[a+16>>2]=J[d+20>>2];J[d+20>>2]=-1;b=b+J[a+4>>2]|0;J[b+32>>2]=0;J[b+24>>2]=-1;J[b+28>>2]=-1;H[b+36|0]=0;J[b+16>>2]=0;J[a+8>>2]=J[a+8>>2]+1;return c}function Ze(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;var g=0,h=0,i=0,j=0,k=0,l=0,m=0;if(Za(a,J[b+8>>2],f)){ic(b,c,d,e);return}g=K[b+53|0];j=J[a+12>>2];H[b+53|0]=0;h=K[b+52|0];H[b+52|0]=0;m=a+16|0;hc(m,b,c,d,e,f);k=K[b+53|0];g=g|k;l=K[b+52|0];h=h|l;i=a+24|0;j=(j<<3)+m|0;a:{if(i>>>0>=j>>>0){break a}while(1){if(K[b+54|0]){break a}b:{if(l){if(J[b+24>>2]==1){break a}if(K[a+8|0]&2){break b}break a}if(!k){break b}if(!(H[a+8|0]&1)){break a}}I[b+52>>1]=0;hc(i,b,c,d,e,f);k=K[b+53|0];g=k|g;l=K[b+52|0];h=l|h;i=i+8|0;if(j>>>0>i>>>0){continue}break}}H[b+53|0]=(g&255)!=0;H[b+52|0]=(h&255)!=0;}function Si(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0;c=J[a+4>>2];if((c|0)!=J[a+8>>2]){e=J[b+4>>2];J[c>>2]=J[b>>2];J[c+4>>2]=e;J[a+4>>2]=c+8;return}a:{h=J[a>>2];e=c-h|0;g=e>>3;d=g+1|0;if(d>>>0<536870912){f=e>>2;f=e>>>0>=2147483640?536870911:d>>>0<f>>>0?f:d;if(f){if(f>>>0>=536870912){break a}e=Ra(f<<3);}else {e=0;}d=e+(g<<3)|0;g=J[b+4>>2];J[d>>2]=J[b>>2];J[d+4>>2]=g;b=d+8|0;if((c|0)!=(h|0)){while(1){c=c-8|0;g=J[c+4>>2];d=d-8|0;J[d>>2]=J[c>>2];J[d+4>>2]=g;if((c|0)!=(h|0)){continue}break}c=J[a>>2];}J[a+8>>2]=e+(f<<3);J[a+4>>2]=b;J[a>>2]=d;if(c){Wa(c);}return}ma();B();}Fb();B();}function di(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=Q(0),f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0),o=Q(0),p=0,q=Q(0);j=N[c+8>>2];f=N[a+20>>2];k=N[c+12>>2];e=N[a+24>>2];n=N[c+4>>2];h=Q(Q(Q(j*f)+Q(k*e))+n);o=N[c>>2];i=Q(o+Q(Q(k*f)-Q(e*j)));c=1;p=J[a+148>>2];a:{if((p|0)<=1){f=i;e=h;break a}m=Q(-j);e=h;f=i;while(1){d=(c<<3)+a|0;q=N[d+20>>2];g=N[d+24>>2];l=Q(n+Q(Q(j*q)+Q(k*g)));e=e>l?e:l;g=Q(o+Q(Q(k*q)+Q(g*m)));f=f>g?f:g;h=h<l?h:l;i=i<g?i:g;c=c+1|0;if((p|0)!=(c|0)){continue}break}}m=e;e=N[a+8>>2];N[b+12>>2]=m+e;N[b+8>>2]=f+e;N[b+4>>2]=h-e;N[b>>2]=i-e;}function me(a,b,c){a=a|0;b=Q(b);c=Q(c);var d=0,e=0,f=0,g=0,h=0,i=0,j=0;a:{d=J[a+4>>2];e=J[a+8>>2];if(d>>>0<e>>>0){N[d+4>>2]=c;N[d>>2]=b;J[a+4>>2]=d+8;break a}b:{h=J[a>>2];i=d-h>>3;f=i+1|0;if(f>>>0<536870912){e=e-h|0;g=e>>2;g=e>>>0>=2147483640?536870911:g>>>0>f>>>0?g:f;if(g){if(g>>>0>=536870912){break b}f=Ra(g<<3);}else {f=0;}e=f+(i<<3)|0;N[e+4>>2]=c;N[e>>2]=b;i=e+8|0;if((d|0)!=(h|0)){while(1){d=d-8|0;j=J[d+4>>2];e=e-8|0;J[e>>2]=J[d>>2];J[e+4>>2]=j;if((d|0)!=(h|0)){continue}break}d=J[a>>2];}J[a+8>>2]=f+(g<<3);J[a+4>>2]=i;J[a>>2]=e;if(d){Wa(d);}break a}ma();B();}Fb();B();}}function Xb(a,b,c,d,e){var f=Q(0),g=Q(0),h=Q(0),i=0,j=0,k=0,l=Q(0),m=Q(0);f=N[c>>2];g=N[c+4>>2];h=Q(Q(Q(f*N[b+12>>2])+Q(g*N[b+16>>2]))-d);c=0;d=Q(Q(Q(f*N[b>>2])+Q(g*N[b+4>>2]))-d);if(d<=Q(0)){c=J[b+4>>2];J[a>>2]=J[b>>2];J[a+4>>2]=c;J[a+8>>2]=J[b+8>>2];c=1;}if(h<=Q(0)){i=b+12|0;k=J[i+4>>2];j=P(c,12)+a|0;J[j>>2]=J[i>>2];J[j+4>>2]=k;J[j+8>>2]=J[i+8>>2];c=c+1|0;}if(Q(d*h)<Q(0)){f=N[b>>2];l=N[b+12>>2];g=N[b+4>>2];m=N[b+16>>2];a=P(c,12)+a|0;H[a+8|0]=e;d=Q(d/Q(d-h));N[a+4>>2]=g+Q(d*Q(m-g));N[a>>2]=f+Q(d*Q(l-f));b=K[b+9|0];I[a+10>>1]=256;H[a+9|0]=b;c=c+1|0;}return c}function td(a,b,c){var d=0,e=0,f=0,g=0;f=1;J[b+4>>2]=1;N[b+8>>2]=N[a+8>>2];e=c<<3;d=e+J[a+12>>2]|0;g=J[d+4>>2];J[b+12>>2]=J[d>>2];J[b+16>>2]=g;d=J[a+12>>2]+e|0;g=J[d+12>>2];J[b+20>>2]=J[d+8>>2];J[b+24>>2]=g;a:{if((c|0)>0){e=(J[a+12>>2]+e|0)-8|0;d=J[e+4>>2];J[b+28>>2]=J[e>>2];J[b+32>>2]=d;break a}f=J[a+24>>2];J[b+28>>2]=J[a+20>>2];J[b+32>>2]=f;f=K[a+36|0];}H[b+44|0]=f;if((J[a+16>>2]-2|0)>(c|0)){a=J[a+12>>2]+(c<<3)|0;c=J[a+20>>2];J[b+36>>2]=J[a+16>>2];J[b+40>>2]=c;H[b+45|0]=1;return}c=J[a+32>>2];J[b+36>>2]=J[a+28>>2];J[b+40>>2]=c;H[b+45|0]=K[a+37|0];}function $e(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;if(Za(a,J[b+8>>2],e)){if(!(J[b+28>>2]==1|J[b+4>>2]!=(c|0))){J[b+28>>2]=d;}return}a:{if(Za(a,J[b>>2],e)){if(!(J[b+16>>2]!=(c|0)&J[b+20>>2]!=(c|0))){if((d|0)!=1){break a}J[b+32>>2]=1;return}J[b+32>>2]=d;b:{if(J[b+44>>2]==4){break b}I[b+52>>1]=0;a=J[a+8>>2];Ha[J[J[a>>2]+20>>2]](a,b,c,c,1,e);if(K[b+53|0]){J[b+44>>2]=3;if(!K[b+52|0]){break b}break a}J[b+44>>2]=4;}J[b+20>>2]=c;J[b+40>>2]=J[b+40>>2]+1;if(J[b+36>>2]!=1|J[b+24>>2]!=2){break a}H[b+54|0]=1;return}a=J[a+8>>2];Ha[J[J[a>>2]+24>>2]](a,b,c,d,e);}}function Sa(a,b){var c=0,d=0,e=0,f=0;d=Fa-16|0;Fa=d;J[d+12>>2]=b;c=Fa-208|0;Fa=c;J[c+204>>2]=b;b=c+160|0;xb(b,0,40);J[c+200>>2]=J[c+204>>2];a:{if((Rc(0,a,c+200|0,c+80|0,b)|0)<0){break a}f=J[5859]>=0;b=J[5840];if(J[5858]<=0){J[5840]=b&-33;}b:{c:{d:{if(!J[5852]){J[5852]=80;J[5847]=0;J[5844]=0;J[5845]=0;e=J[5851];J[5851]=c;break d}if(J[5844]){break c}}if(Tc(23360)){break b}}Rc(23360,a,c+200|0,c+80|0,c+160|0);}if(e){Ha[J[5849]](23360,0,0)|0;J[5852]=0;J[5851]=e;J[5847]=0;J[5844]=0;J[5845]=0;}J[5840]=J[5840]|b&32;if(!f){break a}}Fa=c+208|0;Fa=d+16|0;}function Ci(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0;h=Fa-16|0;Fa=h;f=a+16|0;g=J[f>>2];a:{if(!g){break a}j=J[b+48>>2];e=f;d=g;while(1){i=M[d+16>>2]<j>>>0;e=i?e:d;d=J[(i?d+4|0:d)>>2];if(d){continue}break}if(!((e|0)!=(f|0)&M[e+16>>2]<=j>>>0)){i=J[b+52>>2];d=f;while(1){e=i>>>0>M[g+16>>2];d=e?d:g;e=J[(e?g+4|0:g)>>2];g=e;if(e){continue}break}if((d|0)==(f|0)|i>>>0<M[d+16>>2]){break a}}f=J[a+8>>2];if(!(H[23564]&1)){a=ba(3,20844)|0;H[23564]=1;J[5890]=a;}a=J[5890];J[h+8>>2]=c;J[h>>2]=b;da(a|0,f|0,4289,h|0);}Fa=h+16|0;}function Bi(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0,j=0;h=Fa-16|0;Fa=h;f=a+16|0;g=J[f>>2];a:{if(!g){break a}j=J[b+48>>2];e=f;d=g;while(1){i=M[d+16>>2]<j>>>0;e=i?e:d;d=J[(i?d+4|0:d)>>2];if(d){continue}break}if(!((e|0)!=(f|0)&M[e+16>>2]<=j>>>0)){i=J[b+52>>2];d=f;while(1){e=i>>>0>M[g+16>>2];d=e?d:g;e=J[(e?g+4|0:g)>>2];g=e;if(e){continue}break}if((d|0)==(f|0)|i>>>0<M[d+16>>2]){break a}}f=J[a+8>>2];if(!(H[23564]&1)){a=ba(3,20844)|0;H[23564]=1;J[5890]=a;}a=J[5890];J[h+8>>2]=c;J[h>>2]=b;da(a|0,f|0,4279,h|0);}Fa=h+16|0;}function Ei(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0;h=Fa-16|0;Fa=h;e=a+16|0;f=J[e>>2];a:{if(!f){break a}i=J[b+48>>2];d=e;c=f;while(1){g=M[c+16>>2]<i>>>0;d=g?d:c;c=J[(g?c+4|0:c)>>2];if(c){continue}break}if(!((d|0)!=(e|0)&M[d+16>>2]<=i>>>0)){g=J[b+52>>2];c=e;while(1){d=g>>>0>M[f+16>>2];c=d?c:f;d=J[(d?f+4|0:f)>>2];f=d;if(d){continue}break}if((c|0)==(e|0)|g>>>0<M[c+16>>2]){break a}}e=J[a+8>>2];if(!(H[23556]&1)){a=ba(2,20836)|0;H[23556]=1;J[5888]=a;}a=J[5888];J[h+8>>2]=b;da(a|0,e|0,2300,h+8|0);}Fa=h+16|0;}function Di(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0,i=0;h=Fa-16|0;Fa=h;e=a+16|0;f=J[e>>2];a:{if(!f){break a}i=J[b+48>>2];d=e;c=f;while(1){g=M[c+16>>2]<i>>>0;d=g?d:c;c=J[(g?c+4|0:c)>>2];if(c){continue}break}if(!((d|0)!=(e|0)&M[d+16>>2]<=i>>>0)){g=J[b+52>>2];c=e;while(1){d=g>>>0>M[f+16>>2];c=d?c:f;d=J[(d?f+4|0:f)>>2];f=d;if(d){continue}break}if((c|0)==(e|0)|g>>>0<M[c+16>>2]){break a}}e=J[a+8>>2];if(!(H[23556]&1)){a=ba(2,20836)|0;H[23556]=1;J[5888]=a;}a=J[5888];J[h+8>>2]=b;da(a|0,e|0,2313,h+8|0);}Fa=h+16|0;}function Xh(a,b,c){a=a|0;b=b|0;c=Q(c);var d=0,e=Q(0),f=Q(0),g=Q(0),h=0,i=Q(0),j=Q(0);d=J[a+88>>2];if(!K[d+102989|0]){f=Ua(c);N[a+24>>2]=f;g=Ta(c);N[a+20>>2]=g;j=N[b+4>>2];h=J[b+4>>2];e=N[b>>2];b=J[b>>2];J[a+12>>2]=b;J[a+16>>2]=h;N[a+56>>2]=c;N[a+52>>2]=c;c=N[a+28>>2];i=N[a+32>>2];e=Q(Q(Q(f*c)-Q(g*i))+e);N[a+44>>2]=e;N[a+36>>2]=e;c=Q(Q(Q(g*c)+Q(f*i))+j);N[a+48>>2]=c;N[a+40>>2]=c;b=J[a+100>>2];if(b){h=d+102868|0;d=a+12|0;while(1){Hb(b,h,d,d);b=J[b+4>>2];if(b){continue}break}d=J[a+88>>2];}Ub(d+102868|0);}}function ne(a,b){a=a|0;b=b|0;var c=0,d=0;if(!(K[J[a+88>>2]+102989|0]|!b)){c=J[a+100>>2];a:{if(!c){break a}if((b|0)==(c|0)){d=a+100|0;}else {while(1){d=c;c=J[c+4>>2];if(!c){break a}if((b|0)!=(c|0)){continue}break}d=d+4|0;}J[d>>2]=J[b+4>>2];}c=J[a+112>>2];if(c){while(1){d=J[c+4>>2];c=J[c+12>>2];if(!(J[d+48>>2]!=(b|0)&J[d+52>>2]!=(b|0))){Ib(J[a+88>>2]+102868|0,d);}if(c){continue}break}}d=J[a+88>>2];if(K[a+4|0]&32){pc(b,d+102868|0);}J[b+4>>2]=0;J[b+8>>2]=0;qc(b,d);Bb(d,b,44);J[a+104>>2]=J[a+104>>2]-1;Jb(a);}}function aj(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0,g=0,h=0;e=J[a+4>>2];if((e|0)!=J[a+8>>2]){J[e>>2]=J[b>>2];J[a+4>>2]=e+4;return}a:{g=J[a>>2];f=e-g|0;c=f>>2;d=c+1|0;if(d>>>0<1073741824){h=c<<2;c=f>>1;c=f>>>0>=2147483644?1073741823:c>>>0>d>>>0?c:d;if(c){if(c>>>0>=1073741824){break a}f=Ra(c<<2);}else {f=0;}d=h+f|0;J[d>>2]=J[b>>2];b=d+4|0;if((e|0)!=(g|0)){while(1){d=d-4|0;e=e-4|0;J[d>>2]=J[e>>2];if((e|0)!=(g|0)){continue}break}}J[a+8>>2]=f+(c<<2);J[a+4>>2]=b;J[a>>2]=d;if(g){Wa(g);}return}ma();B();}Fb();B();}function Dh(a){a=a|0;var b=0,c=Q(0),d=0,e=0;b=Fa-144|0;Fa=b;d=J[J[a+52>>2]+8>>2];e=J[J[a+48>>2]+8>>2];Sa(8221,0);J[b+128>>2]=e;Sa(8435,b+128|0);J[b+112>>2]=d;Sa(8409,b+112|0);J[b+96>>2]=K[a+61|0];Sa(9625,b+96|0);c=N[a+80>>2];O[b+88>>3]=N[a+84>>2];O[b+80>>3]=c;Sa(8936,b+80|0);c=N[a+88>>2];O[b+72>>3]=N[a+92>>2];O[b+64>>3]=c;Sa(8811,b- -64|0);O[b+48>>3]=N[a+104>>2];Sa(7480,b+48|0);O[b+32>>3]=N[a+68>>2];Sa(7115,b+32|0);O[b+16>>3]=N[a+72>>2];Sa(7323,b+16|0);J[b>>2]=J[a+56>>2];Sa(9226,b);Fa=b+144|0;}function Pf(a){a=a|0;var b=0,c=Q(0),d=0,e=0;b=Fa-144|0;Fa=b;d=J[J[a+52>>2]+8>>2];e=J[J[a+48>>2]+8>>2];Sa(8247,0);J[b+128>>2]=e;Sa(8435,b+128|0);J[b+112>>2]=d;Sa(8409,b+112|0);J[b+96>>2]=K[a+61|0];Sa(9625,b+96|0);c=N[a+80>>2];O[b+88>>3]=N[a+84>>2];O[b+80>>3]=c;Sa(8936,b+80|0);c=N[a+88>>2];O[b+72>>3]=N[a+92>>2];O[b+64>>3]=c;Sa(8811,b- -64|0);O[b+48>>3]=N[a+96>>2];Sa(7732,b+48|0);O[b+32>>3]=N[a+68>>2];Sa(7115,b+32|0);O[b+16>>3]=N[a+72>>2];Sa(7323,b+16|0);J[b>>2]=J[a+56>>2];Sa(9226,b);Fa=b+144|0;}function fi(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=Q(0),f=0,g=0,h=Q(0),i=Q(0),j=Q(0),k=Q(0);g=J[a+148>>2];d=1;a:{if((g|0)<=0){break a}e=N[b+12>>2];h=Q(N[c>>2]-N[b>>2]);i=Q(N[c+4>>2]-N[b+4>>2]);j=N[b+8>>2];k=Q(Q(e*h)+Q(i*j));e=Q(Q(e*i)-Q(j*h));d=0;if(Q(Q(N[a+84>>2]*Q(k-N[a+20>>2]))+Q(Q(e-N[a+24>>2])*N[a+88>>2]))>Q(0)){break a}while(1){f=f+1|0;if((g|0)!=(f|0)){b=(f<<3)+a|0;if(!(Q(Q(N[b+84>>2]*Q(k-N[b+20>>2]))+Q(Q(e-N[b+24>>2])*N[b+88>>2]))>Q(0))){continue}}break}d=(f|0)>=(g|0);}return d|0}function wd(a){var b=0,c=0,d=Q(0),e=Q(0),f=0,g=Q(0),h=Q(0),i=Q(0);c=Fa-16|0;Fa=c;f=J[a+88>>2]+102868|0;a:{if(K[a+4|0]&2){d=N[a+52>>2];e=Ua(d);N[c+12>>2]=e;d=Ta(d);N[c+8>>2]=d;i=N[a+36>>2];g=N[a+28>>2];h=N[a+32>>2];N[c+4>>2]=N[a+40>>2]-Q(Q(d*g)+Q(e*h));N[c>>2]=i-Q(Q(e*g)-Q(h*d));b=J[a+100>>2];if(!b){break a}a=a+12|0;while(1){Hb(b,f,c,a);b=J[b+4>>2];if(b){continue}break}break a}b=J[a+100>>2];if(!b){break a}a=a+12|0;while(1){Hb(b,f,a,a);b=J[b+4>>2];if(b){continue}break}}Fa=c+16|0;}function ah(a){a=a|0;var b=0,c=0,d=0,e=Q(0);b=Fa-144|0;Fa=b;c=J[J[a+52>>2]+8>>2];d=J[J[a+48>>2]+8>>2];Sa(8079,0);J[b+128>>2]=d;Sa(8435,b+128|0);J[b+112>>2]=c;Sa(8409,b+112|0);J[b+96>>2]=K[a+61|0];Sa(9625,b+96|0);e=N[a+68>>2];O[b+88>>3]=N[a+72>>2];O[b+80>>3]=e;Sa(8658,b+80|0);O[b+64>>3]=N[a+76>>2];Sa(7204,b- -64|0);O[b+48>>3]=N[a+92>>2];Sa(7794,b+48|0);O[b+32>>3]=N[a+96>>2];Sa(7594,b+32|0);O[b+16>>3]=N[a+100>>2];Sa(7266,b+16|0);J[b>>2]=J[a+56>>2];Sa(9226,b);Fa=b+144|0;}function pc(a,b){var c=0,d=0,e=0,f=0,g=0,h=0,i=0;if(J[a+28>>2]>0){while(1){g=J[a+24>>2]+P(e,28)|0;d=J[g+24>>2];c=0;f=J[b+40>>2];if((f|0)>0){i=J[b+32>>2];while(1){h=(c<<2)+i|0;if(J[h>>2]==(d|0)){J[h>>2]=-1;f=J[b+40>>2];}c=c+1|0;if((f|0)>(c|0)){continue}break}}J[b+28>>2]=J[b+28>>2]-1;xd(b,d);c=P(d,40);J[(c+J[b+4>>2]|0)+20>>2]=J[b+16>>2];J[(c+J[b+4>>2]|0)+32>>2]=-1;J[b+16>>2]=d;J[b+8>>2]=J[b+8>>2]-1;J[g+24>>2]=-1;e=e+1|0;if((e|0)<J[a+28>>2]){continue}break}}J[a+28>>2]=0;}function Wh(a,b){a=a|0;b=b|0;var c=0,d=0;c=L[a+4>>1];if(((c&32)>>>5|0)!=(b|0)){if(b){I[a+4>>1]=c|32;c=J[a+88>>2];b=J[a+100>>2];if(b){c=c+102868|0;d=a+12|0;while(1){nd(b,c,d);b=J[b+4>>2];if(b){continue}break}c=J[a+88>>2];}H[c+102988|0]=1;return}I[a+4>>1]=c&65503;b=J[a+100>>2];if(b){c=J[a+88>>2]+102868|0;while(1){pc(b,c);b=J[b+4>>2];if(b){continue}break}}b=J[a+112>>2];if(b){while(1){c=J[b+12>>2];Ib(J[a+88>>2]+102868|0,J[b+4>>2]);b=c;if(b){continue}break}}J[a+112>>2]=0;}}function Mc(a,b){if(!a){return 0}a:{b:{if(a){if(b>>>0<=127){break b}c:{if(!J[J[6425]>>2]){if((b&-128)==57216){break b}break c}if(b>>>0<=2047){H[a+1|0]=b&63|128;H[a|0]=b>>>6|192;a=2;break a}if(!((b&-8192)!=57344&b>>>0>=55296)){H[a+2|0]=b&63|128;H[a|0]=b>>>12|224;H[a+1|0]=b>>>6&63|128;a=3;break a}if(b-65536>>>0<=1048575){H[a+3|0]=b&63|128;H[a|0]=b>>>18|240;H[a+2|0]=b>>>6&63|128;H[a+1|0]=b>>>12&63|128;a=4;break a}}J[6386]=25;a=-1;}else {a=1;}break a}H[a|0]=b;a=1;}return a}function ph(a){a=a|0;var b=0,c=Q(0),d=0,e=0;b=Fa-128|0;Fa=b;d=J[J[a+52>>2]+8>>2];e=J[J[a+48>>2]+8>>2];Sa(8124,0);J[b+112>>2]=e;Sa(8435,b+112|0);J[b+96>>2]=d;Sa(8409,b+96|0);J[b+80>>2]=K[a+61|0];Sa(9625,b+80|0);c=N[a+68>>2];O[b+72>>3]=N[a+72>>2];O[b+64>>3]=c;Sa(8936,b- -64|0);c=N[a+76>>2];O[b+56>>3]=N[a+80>>2];O[b+48>>3]=c;Sa(8811,b+48|0);O[b+32>>3]=N[a+96>>2];Sa(7794,b+32|0);O[b+16>>3]=N[a+100>>2];Sa(7594,b+16|0);J[b>>2]=J[a+56>>2];Sa(9226,b);Fa=b+128|0;}function If(a){a=a|0;var b=0,c=0,d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0);b=J[a+52>>2];f=N[b+24>>2];c=J[a+48>>2];d=N[c+24>>2];g=N[a+84>>2];h=N[a+88>>2];i=N[b+20>>2];j=N[a+76>>2];k=N[a+80>>2];e=N[c+20>>2];l=N[a+92>>2];m=N[a+96>>2];return Q(Q(Q(Q(Q(N[b+12>>2]+Q(Q(f*g)-Q(h*i)))-Q(N[c+12>>2]+Q(Q(d*j)-Q(k*e))))*Q(Q(d*l)-Q(m*e)))+Q(Q(Q(Q(Q(i*g)+Q(f*h))+N[b+16>>2])-Q(Q(Q(e*j)+Q(d*k))+N[c+16>>2]))*Q(Q(e*l)+Q(d*m)))))}function Fg(a){a=a|0;var b=0,c=0,d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0);b=J[a+52>>2];f=N[b+24>>2];c=J[a+48>>2];d=N[c+24>>2];g=N[a+76>>2];h=N[a+80>>2];i=N[b+20>>2];j=N[a+68>>2];k=N[a+72>>2];e=N[c+20>>2];l=N[a+84>>2];m=N[a+88>>2];return Q(Q(Q(Q(Q(N[b+12>>2]+Q(Q(f*g)-Q(h*i)))-Q(N[c+12>>2]+Q(Q(d*j)-Q(k*e))))*Q(Q(d*l)-Q(m*e)))+Q(Q(Q(Q(Q(i*g)+Q(f*h))+N[b+16>>2])-Q(Q(Q(e*j)+Q(d*k))+N[c+16>>2]))*Q(Q(e*l)+Q(d*m)))))}function Te(a,b){a=a|0;b=b|0;var c=0,d=0;c=L[b>>1]|L[b+2>>1]<<16;I[a+32>>1]=c;I[a+34>>1]=c>>>16;I[a+36>>1]=L[b+4>>1];d=J[a+8>>2];a:{if(!d){break a}b=J[d+112>>2];if(b){while(1){c=J[b+4>>2];if(!(J[c+48>>2]!=(a|0)&J[c+52>>2]!=(a|0))){J[c+4>>2]=J[c+4>>2]|8;}b=J[b+12>>2];if(b){continue}break}}b=J[d+88>>2];if(!b|J[a+28>>2]<=0){break a}c=b+102868|0;b=0;while(1){uc(c,J[(J[a+24>>2]+P(b,28)|0)+24>>2]);b=b+1|0;if((b|0)<J[a+28>>2]){continue}break}}}function si(a,b,c,d,e){a=a|0;b=b|0;c=Q(c);d=d|0;e=e|0;var f=0,g=0,h=0,i=0;f=Fa-32|0;Fa=f;g=J[a+12>>2];if(!(H[23588]&1)){a=ba(5,21280)|0;H[23588]=1;J[5896]=a;}h=J[5896];a=Ra(8);i=J[b+4>>2];J[a>>2]=J[b>>2];J[a+4>>2]=i;N[f+8>>2]=c;J[f>>2]=a;a=Ra(8);b=J[d+4>>2];J[a>>2]=J[d>>2];J[a+4>>2]=b;J[f+16>>2]=a;a=Ra(16);b=J[e+12>>2];J[a+8>>2]=J[e+8>>2];J[a+12>>2]=b;b=J[e+4>>2];J[a>>2]=J[e>>2];J[a+4>>2]=b;J[f+24>>2]=a;da(h|0,g|0,5053,f|0);Fa=f+32|0;}function Rh(a,b){a=a|0;b=b|0;var c=0;b=Ya(b,48);J[b+4>>2]=1;J[b+8>>2]=1008981770;J[b>>2]=12808;J[b+28>>2]=0;J[b+32>>2]=0;J[b+36>>2]=0;J[b+40>>2]=0;I[b+44>>1]=0;c=J[a+8>>2];J[b+4>>2]=J[a+4>>2];J[b+8>>2]=c;c=J[a+16>>2];J[b+12>>2]=J[a+12>>2];J[b+16>>2]=c;c=J[a+24>>2];J[b+20>>2]=J[a+20>>2];J[b+24>>2]=c;c=J[a+32>>2];J[b+28>>2]=J[a+28>>2];J[b+32>>2]=c;c=J[a+40>>2];J[b+36>>2]=J[a+36>>2];J[b+40>>2]=c;I[b+44>>1]=L[a+44>>1];return b|0}function ri(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0,h=0;e=Fa-32|0;Fa=e;f=J[a+12>>2];if(!(H[23596]&1)){a=ba(4,21312)|0;H[23596]=1;J[5898]=a;}g=J[5898];a=Ra(8);h=J[b+4>>2];J[a>>2]=J[b>>2];J[a+4>>2]=h;J[e+8>>2]=a;a=Ra(8);b=J[c+4>>2];J[a>>2]=J[c>>2];J[a+4>>2]=b;J[e+16>>2]=a;a=Ra(16);b=J[d+12>>2];J[a+8>>2]=J[d+8>>2];J[a+12>>2]=b;b=J[d+4>>2];J[a>>2]=J[d>>2];J[a+4>>2]=b;J[e+24>>2]=a;da(g|0,f|0,2039,e+8|0);Fa=e+32|0;}function Yk(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0,g=0,h=0,i=0;d=Fa-32|0;Fa=d;e=J[b>>2];if(e>>>0<2147483632){a:{if(e>>>0<=10){H[d+27|0]=e;f=d+16|0;break a}g=(e|15)+1|0;f=Ra(g);J[d+24>>2]=g|-2147483648;J[d+16>>2]=f;J[d+20>>2]=e;}h=eb(f,b+4|0,e)+e|0,i=0,H[h|0]=i;J[d+12>>2]=c;Ha[a|0](d+28|0,d+16|0,d+12|0);wa(J[d+28>>2]);a=J[d+28>>2];fa(a|0);fa(J[d+12>>2]);if(H[d+27|0]<0){Wa(J[d+16>>2]);}Fa=d+32|0;return a|0}ma();B();}function Vf(a){a=a|0;var b=0,c=Q(0),d=0,e=0;b=Fa-112|0;Fa=b;d=J[J[a+52>>2]+8>>2];e=J[J[a+48>>2]+8>>2];Sa(8199,0);J[b+96>>2]=e;Sa(8435,b+96|0);J[b+80>>2]=d;Sa(8409,b+80|0);J[b+64>>2]=K[a+61|0];Sa(9625,b- -64|0);c=N[a+68>>2];O[b+56>>3]=N[a+72>>2];O[b+48>>3]=c;Sa(8936,b+48|0);c=N[a+76>>2];O[b+40>>3]=N[a+80>>2];O[b+32>>3]=c;Sa(8811,b+32|0);O[b+16>>3]=N[a+84>>2];Sa(7504,b+16|0);J[b>>2]=J[a+56>>2];Sa(9226,b);Fa=b+112|0;}function Oh(a,b,c,d){a=a|0;b=b|0;c=c|0;var e=Q(0),f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0),l=Q(0),m=Q(0),n=Q(0);k=N[c>>2];h=N[a+8>>2];e=N[c+8>>2];f=N[a+12>>2];i=N[c+12>>2];l=N[a+16>>2];g=N[c+4>>2];j=Q(Q(Q(e*f)+Q(i*l))+g);m=N[a+20>>2];n=N[a+24>>2];g=Q(g+Q(Q(e*m)+Q(i*n)));N[b+12>>2]=h+(g<j?j:g);f=Q(k+Q(Q(i*f)-Q(l*e)));e=Q(k+Q(Q(i*m)-Q(n*e)));N[b+8>>2]=h+(e<f?f:e);N[b+4>>2]=(g>j?j:g)-h;N[b>>2]=(e>f?f:e)-h;}function Wb(a,b,c){var d=0,e=0;a:{b:{c:{d:{switch(J[b+4>>2]){case 0:J[a+16>>2]=b+12;c=1;break b;case 2:J[a+16>>2]=b+20;c=J[b+148>>2];break b;case 3:d=J[b+12>>2]+(c<<3)|0;e=J[d+4>>2];J[a>>2]=J[d>>2];J[a+4>>2]=e;c=c+1|0;c=J[b+12>>2]+(((c|0)<J[b+16>>2]?c:0)<<3)|0;d=J[c+4>>2];J[a+8>>2]=J[c>>2];J[a+12>>2]=d;J[a+16>>2]=a;break c;case 1:break d;default:break a}}J[a+16>>2]=b+12;}c=2;}J[a+20>>2]=c;N[a+24>>2]=N[b+8>>2];}}function qc(a,b){var c=0,d=0;c=J[a+12>>2];c=Ha[J[J[c>>2]+12>>2]](c)|0;Bb(b,J[a+24>>2],P(c,28));J[a+24>>2]=0;a:{b:{c:{d:{e:{f:{c=J[a+12>>2];switch(J[c+4>>2]){case 3:break c;case 2:break d;case 1:break e;case 0:break f;default:break a}}Ha[J[J[c>>2]>>2]](c)|0;d=20;break b}Ha[J[J[c>>2]>>2]](c)|0;d=48;break b}Ha[J[J[c>>2]>>2]](c)|0;d=152;break b}Ha[J[J[c>>2]>>2]](c)|0;d=40;}Bb(b,c,d);}J[a+12>>2]=0;}function th(a){a=a|0;var b=0,c=0,d=0,e=0,f=0;b=Fa-112|0;Fa=b;c=J[J[a+72>>2]+56>>2];d=J[J[a+68>>2]+56>>2];e=J[J[a+52>>2]+8>>2];f=J[J[a+48>>2]+8>>2];Sa(8102,0);J[b+96>>2]=f;Sa(8435,b+96|0);J[b+80>>2]=e;Sa(8409,b+80|0);J[b+64>>2]=K[a+61|0];Sa(9625,b- -64|0);J[b+48>>2]=d;Sa(8382,b+48|0);J[b+32>>2]=c;Sa(8355,b+32|0);O[b+16>>3]=N[a+152>>2];Sa(7300,b+16|0);J[b>>2]=J[a+56>>2];Sa(9226,b);Fa=b+112|0;}function bb(a,b,c){var d=0,e=0,f=0;if(!(K[a|0]&32)){a:{d=b;b=a;a=J[b+16>>2];b:{if(!a){if(Tc(b)){break b}a=J[b+16>>2];}f=J[b+20>>2];if(a-f>>>0<c>>>0){Ha[J[b+36>>2]](b,d,c)|0;break a}c:{if(J[b+80>>2]<0){break c}a=c;while(1){e=a;if(!a){break c}a=e-1|0;if(K[d+a|0]!=10){continue}break}if(Ha[J[b+36>>2]](b,d,e)>>>0<e>>>0){break b}d=d+e|0;c=c-e|0;f=J[b+20>>2];}eb(f,d,c);J[b+20>>2]=J[b+20>>2]+c;}}}}function Ii(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=Q(e);var f=0,g=0,h=0,i=0;f=Fa-48|0;Fa=f;g=J[a+8>>2];if(!(H[23548]&1)){a=ba(5,20560)|0;H[23548]=1;J[5886]=a;}h=J[5886];J[f+16>>2]=b;a=Ra(8);b=J[c+4>>2];J[a>>2]=J[c>>2];J[a+4>>2]=b;J[f+24>>2]=a;a=Ra(8);b=J[d+4>>2];J[a>>2]=J[d>>2];J[a+4>>2]=b;N[f+40>>2]=e;J[f+32>>2]=a;i=+ua(h|0,g|0,4735,f+12|0,f+16|0);ta(J[f+12>>2]);Fa=f+48|0;return Q(Q(i))}function yb(a,b,c,d,e){var f=Q(0),g=Q(0);J[a+60>>2]=e;J[a+56>>2]=c;J[a+52>>2]=d;J[a+48>>2]=b;J[a+4>>2]=4;J[a+8>>2]=0;J[a+12>>2]=0;J[a+124>>2]=0;J[a+128>>2]=0;J[a>>2]=12996;J[a+16>>2]=0;J[a+20>>2]=0;J[a+24>>2]=0;J[a+28>>2]=0;J[a+32>>2]=0;J[a+36>>2]=0;J[a+40>>2]=0;J[a+44>>2]=0;N[a+136>>2]=Y(Q(N[b+16>>2]*N[d+16>>2]));f=N[d+20>>2];g=N[b+20>>2];J[a+144>>2]=0;N[a+140>>2]=f<g?g:f;return a}function lj(){var a=0;a=Ra(64);J[a+4>>2]=0;J[a+8>>2]=0;J[a>>2]=1;J[a+56>>2]=0;J[a+60>>2]=0;J[a+20>>2]=0;J[a+24>>2]=0;J[a+44>>2]=0;J[a+48>>2]=0;H[a+9|0]=0;H[a+10|0]=0;H[a+11|0]=0;H[a+12|0]=0;H[a+13|0]=0;H[a+14|0]=0;H[a+15|0]=0;H[a+16|0]=0;J[a+28>>2]=0;J[a+32>>2]=0;H[a+33|0]=0;H[a+34|0]=0;H[a+35|0]=0;H[a+36|0]=0;H[a+37|0]=0;H[a+38|0]=0;H[a+39|0]=0;H[a+40|0]=0;H[a+52|0]=0;return a|0}function yk(a){a=a|0;var b=0,c=0,d=0;if(a){b=a;c=J[a+102948>>2];if(c){while(1){a=J[c+100>>2];c=J[c+96>>2];if(a){while(1){J[a+28>>2]=0;d=J[a+4>>2];qc(a,b);a=d;if(a){continue}break}}if(c){continue}break}}a=b+102868|0;Wa(J[a+32>>2]);Wa(J[a+44>>2]);Wa(J[a+4>>2]);a=0;if(J[b+4>>2]>0){while(1){Wa(J[(J[b>>2]+(a<<3)|0)+4>>2]);a=a+1|0;if((a|0)<J[b+4>>2]){continue}break}}Wa(J[b>>2]);Wa(b);}}function $j(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=Q(0),f=0,g=Q(0);a:{if(J[a>>2]!=2){break a}f=!d;d=L[a+4>>1];if(!(f|d&2)){J[a+144>>2]=0;d=d|2;I[a+4>>1]=d;}if(!(d&2)){break a}g=N[b+4>>2];e=N[a+120>>2];N[a+64>>2]=Q(e*N[b>>2])+N[a+64>>2];N[a+68>>2]=Q(e*g)+N[a+68>>2];N[a+72>>2]=Q(N[a+128>>2]*Q(Q(Q(N[c>>2]-N[a+44>>2])*N[b+4>>2])-Q(N[b>>2]*Q(N[c+4>>2]-N[a+48>>2]))))+N[a+72>>2];}}function ti(a,b,c,d){a=a|0;b=b|0;c=Q(c);d=d|0;var e=0,f=0,g=0,h=0;e=Fa-32|0;Fa=e;f=J[a+12>>2];if(!(H[23580]&1)){a=ba(4,21264)|0;H[23580]=1;J[5894]=a;}g=J[5894];a=Ra(8);h=J[b+4>>2];J[a>>2]=J[b>>2];J[a+4>>2]=h;N[e+16>>2]=c;J[e+8>>2]=a;a=Ra(16);b=J[d+12>>2];J[a+8>>2]=J[d+8>>2];J[a+12>>2]=b;b=J[d+4>>2];J[a>>2]=J[d>>2];J[a+4>>2]=b;J[e+24>>2]=a;da(g|0,f|0,5042,e+8|0);Fa=e+32|0;}function pi(a,b,c,d){a=a|0;b=b|0;c=Q(c);d=d|0;var e=0,f=0,g=0,h=0;e=Fa-32|0;Fa=e;f=J[a+12>>2];if(!(H[23580]&1)){a=ba(4,21264)|0;H[23580]=1;J[5894]=a;}g=J[5894];a=Ra(8);h=J[b+4>>2];J[a>>2]=J[b>>2];J[a+4>>2]=h;N[e+16>>2]=c;J[e+8>>2]=a;a=Ra(16);b=J[d+12>>2];J[a+8>>2]=J[d+8>>2];J[a+12>>2]=b;b=J[d+4>>2];J[a>>2]=J[d>>2];J[a+4>>2]=b;J[e+24>>2]=a;da(g|0,f|0,1753,e+8|0);Fa=e+32|0;}function qf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;a=J[c+8>>2];e=J[b+8>>2];if(!(J[a>>2]|J[e>>2])){return 0}a=J[a+108>>2];a:{if(a){while(1){d=0;if(!(K[J[a+4>>2]+61|0]|(e|0)!=J[a>>2])){break a}a=J[a+12>>2];if(a){continue}break}}d=1;}if(d){a=I[b+36>>1];if(!(!a|L[c+36>>1]!=(a&65535))){return (a|0)>0|0}a=(L[c+32>>1]&L[b+34>>1])!=0&(L[c+34>>1]&L[b+32>>1])!=0;}else {a=0;}return a|0}function pf(a){a=a|0;var b=0,c=0,d=0;a:{b:{c:{c=J[a+4>>2];a=c;if(!(a&3)){break c}b=0;if(!K[c|0]){break a}while(1){a=a+1|0;if(!(a&3)){break c}if(K[a|0]){continue}break}break b}while(1){b=a;a=a+4|0;d=J[b>>2];if(!((d^-1)&d-16843009&-2139062144)){continue}break}while(1){a=b;b=a+1|0;if(K[a|0]){continue}break}}b=a-c|0;}a=b+1|0;b=_a(a);if(b){a=eb(b,c,a);}else {a=0;}return a|0}function _e(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;if(Za(a,J[b+8>>2],e)){if(!(J[b+28>>2]==1|J[b+4>>2]!=(c|0))){J[b+28>>2]=d;}return}a:{if(!Za(a,J[b>>2],e)){break a}if(!(J[b+16>>2]!=(c|0)&J[b+20>>2]!=(c|0))){if((d|0)!=1){break a}J[b+32>>2]=1;return}J[b+20>>2]=c;J[b+32>>2]=d;J[b+40>>2]=J[b+40>>2]+1;if(!(J[b+36>>2]!=1|J[b+24>>2]!=2)){H[b+54|0]=1;}J[b+44>>2]=4;}}function Pe(a){a=a|0;var b=0,c=0,d=0;a:{d=J[a+8>>2];if(!d){break a}b=J[d+112>>2];if(b){while(1){c=J[b+4>>2];if(!(J[c+48>>2]!=(a|0)&J[c+52>>2]!=(a|0))){J[c+4>>2]=J[c+4>>2]|8;}b=J[b+12>>2];if(b){continue}break}}b=J[d+88>>2];if(!b|J[a+28>>2]<=0){break a}c=b+102868|0;b=0;while(1){uc(c,J[(J[a+24>>2]+P(b,28)|0)+24>>2]);b=b+1|0;if((b|0)<J[a+28>>2]){continue}break}}}function re(a,b,c){a=a|0;b=Q(b);c=Q(c);var d=Q(0);J[a+84>>2]=0;J[a+88>>2]=-1082130432;J[a+148>>2]=4;J[a+12>>2]=0;J[a+16>>2]=0;J[a+108>>2]=-1082130432;J[a+112>>2]=0;J[a+100>>2]=0;J[a+104>>2]=1065353216;J[a+92>>2]=1065353216;J[a+96>>2]=0;N[a+48>>2]=c;d=Q(-b);N[a+44>>2]=d;N[a+40>>2]=c;N[a+36>>2]=b;c=Q(-c);N[a+32>>2]=c;N[a+28>>2]=b;N[a+24>>2]=c;N[a+20>>2]=d;}function wb(a,b){var c=0,d=0,e=0,f=0;f=a+102412|0;c=J[a+102796>>2];J[(f+P(c,12)|0)+4>>2]=b;d=J[a+102400>>2];e=d+b|0;a:{if((e|0)>=102401){e=1;d=_a(b);break a}J[a+102400>>2]=e;e=0;d=a+d|0;}c=f+P(c,12)|0;H[c+8|0]=e;J[c>>2]=d;b=J[a+102404>>2]+b|0;J[a+102404>>2]=b;J[a+102796>>2]=J[a+102796>>2]+1;c=a;a=J[a+102408>>2];J[c+102408>>2]=(a|0)>(b|0)?a:b;return d}function oj(){var a=0;a=Ra(72);J[a+4>>2]=0;J[a+8>>2]=0;J[a+20>>2]=0;J[a+24>>2]=0;J[a>>2]=2;J[a+64>>2]=0;J[a+68>>2]=0;J[a+36>>2]=1065353216;J[a+52>>2]=0;J[a+56>>2]=0;H[a+9|0]=0;H[a+10|0]=0;H[a+11|0]=0;H[a+12|0]=0;H[a+13|0]=0;H[a+14|0]=0;H[a+15|0]=0;H[a+16|0]=0;J[a+28>>2]=0;J[a+32>>2]=0;J[a+40>>2]=0;J[a+44>>2]=0;H[a+48|0]=0;H[a+60|0]=0;return a|0}function ic(a,b,c,d){H[a+53|0]=1;a:{if(J[a+4>>2]!=(c|0)){break a}H[a+52|0]=1;c=J[a+16>>2];b:{if(!c){J[a+36>>2]=1;J[a+24>>2]=d;J[a+16>>2]=b;if((d|0)!=1){break a}if(J[a+48>>2]==1){break b}break a}if((b|0)==(c|0)){c=J[a+24>>2];if((c|0)==2){J[a+24>>2]=d;c=d;}if(J[a+48>>2]!=1){break a}if((c|0)==1){break b}break a}J[a+36>>2]=J[a+36>>2]+1;}H[a+54|0]=1;}}function _c(a,b,c,d,e,f){var g=0,h=0;J[a+48>>2]=d;J[a+44>>2]=c;J[a+40>>2]=b;J[a+36>>2]=0;J[a+28>>2]=0;J[a+32>>2]=0;J[a+4>>2]=f;J[a>>2]=e;g=a,h=wb(e,b<<2),J[g+8>>2]=h;g=a,h=wb(J[a>>2],c<<2),J[g+12>>2]=h;g=a,h=wb(J[a>>2],d<<2),J[g+16>>2]=h;g=a,h=wb(J[a>>2],P(J[a+40>>2],12)),J[g+24>>2]=h;g=a,h=wb(J[a>>2],P(J[a+40>>2],12)),J[g+20>>2]=h;return a}function dj(){var a=0;a=Ra(64);J[a+4>>2]=0;J[a+8>>2]=0;J[a+20>>2]=0;J[a+24>>2]=0;J[a>>2]=7;J[a+56>>2]=1073741824;J[a+60>>2]=1060320051;J[a+48>>2]=0;J[a+52>>2]=0;H[a+44|0]=0;J[a+36>>2]=1065353216;J[a+40>>2]=0;H[a+9|0]=0;H[a+10|0]=0;H[a+11|0]=0;H[a+12|0]=0;H[a+13|0]=0;H[a+14|0]=0;H[a+15|0]=0;H[a+16|0]=0;J[a+28>>2]=0;J[a+32>>2]=0;return a|0}function Rb(a,b){a:{if((b|0)>=1024){a=a*898846567431158e293;if(b>>>0<2047){b=b-1023|0;break a}a=a*898846567431158e293;b=((b|0)>=3069?3069:b)-2046|0;break a}if((b|0)>-1023){break a}a=a*2004168360008973e-307;if(b>>>0>4294965304){b=b+969|0;break a}a=a*2004168360008973e-307;b=((b|0)<=-2960?-2960:b)+1938|0;}x(0,0);x(1,b+1023<<20);return a*+z()}function hf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=Fa+-64|0;Fa=d;e=1;a:{if(Za(a,b,0)){break a}e=0;if(!b){break a}b=lb(b,18564);e=0;if(!b){break a}xb(d+12|0,0,52);J[d+56>>2]=1;J[d+20>>2]=-1;J[d+16>>2]=a;J[d+8>>2]=b;Ha[J[J[b>>2]+28>>2]](b,d+8|0,J[c>>2],1);a=J[d+32>>2];if((a|0)==1){J[c>>2]=J[d+24>>2];}e=(a|0)==1;}a=e;Fa=d- -64|0;return a|0}function ge(){var a=0,b=0;J[5881]=0;J[5880]=670;Vd();J[5881]=J[6124];J[6124]=23520;H[23620]=0;a=1;while(1){b=(J[(b<<2)+12640>>2]<(a|0))+b|0;H[a+23620|0]=b;b=b+(J[(b<<2)+12640>>2]<=(a|0))|0;H[a+23621|0]=b;a=a+2|0;if((a|0)!=641){continue}break}J[6125]=866;J[6126]=0;Vc();J[6126]=J[6124];J[6124]=24500;J[6425]=25580;J[6407]=42;}function Hk(a){a=a|0;var b=Q(0),c=Q(0),d=Q(0),e=Q(0),f=0;c=N[a+8>>2];b=N[a>>2];a:{if(!(Q(c-b)>=Q(0))){break a}d=N[a+12>>2];e=N[a+4>>2];if(!(Q(d-e)>=Q(0))){break a}b=Q(R(b));if(!(b>Q(Infinity)|b<Q(Infinity))){break a}b=Q(R(e));if(!(b>Q(Infinity)|b<Q(Infinity))){break a}f=Q(R(c))<Q(Infinity)&Q(R(d))<Q(Infinity);}return f|0}function vi(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;e=Fa-32|0;Fa=e;f=J[a+12>>2];if(!(H[23572]&1)){a=ba(4,21248)|0;H[23572]=1;J[5892]=a;}g=J[5892];J[e+16>>2]=c;J[e+8>>2]=b;a=Ra(16);b=J[d+12>>2];J[a+8>>2]=J[d+8>>2];J[a+12>>2]=b;b=J[d+4>>2];J[a>>2]=J[d>>2];J[a+4>>2]=b;J[e+24>>2]=a;da(g|0,f|0,3533,e+8|0);Fa=e+32|0;}function ui(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0,g=0;e=Fa-32|0;Fa=e;f=J[a+12>>2];if(!(H[23572]&1)){a=ba(4,21248)|0;H[23572]=1;J[5892]=a;}g=J[5892];J[e+16>>2]=c;J[e+8>>2]=b;a=Ra(16);b=J[d+12>>2];J[a+8>>2]=J[d+8>>2];J[a+12>>2]=b;b=J[d+4>>2];J[a>>2]=J[d>>2];J[a+4>>2]=b;J[e+24>>2]=a;da(g|0,f|0,3545,e+8|0);Fa=e+32|0;}function pb(a,b){var c=0;J[a>>2]=13436;c=J[b>>2];J[a+8>>2]=0;J[a+12>>2]=0;J[a+4>>2]=c;J[a+48>>2]=J[b+8>>2];c=J[b+12>>2];J[a+56>>2]=0;J[a+52>>2]=c;c=K[b+16|0];H[a+60|0]=0;H[a+61|0]=c;b=J[b+4>>2];J[a+16>>2]=0;J[a+20>>2]=0;J[a+64>>2]=b;J[a+24>>2]=0;J[a+28>>2]=0;J[a+32>>2]=0;J[a+36>>2]=0;J[a+40>>2]=0;J[a+44>>2]=0;return a}function dk(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0;a:{if(J[a>>2]!=2){break a}e=!d;d=L[a+4>>1];if(!(e|d&2)){J[a+144>>2]=0;d=d|2;I[a+4>>1]=d;}if(!(d&2)){break a}N[a+76>>2]=N[b>>2]+N[a+76>>2];N[a+80>>2]=N[b+4>>2]+N[a+80>>2];N[a+84>>2]=N[a+84>>2]+Q(Q(Q(N[c>>2]-N[a+44>>2])*N[b+4>>2])-Q(N[b>>2]*Q(N[c+4>>2]-N[a+48>>2])));}}function Pj(a,b,c){a=a|0;b=b|0;c=c|0;var d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0);i=N[b+48>>2];j=N[b+16>>2];k=N[b+64>>2];d=N[b+72>>2];e=N[b+24>>2];f=N[c>>2];g=N[c+4>>2];h=N[b+20>>2];N[a+4>>2]=Q(d*Q(Q(N[b+12>>2]+Q(Q(e*f)-Q(g*h)))-N[b+44>>2]))+N[b+68>>2];N[a>>2]=k-Q(d*Q(Q(j+Q(Q(h*f)+Q(e*g)))-i));}function qi(a,b){a=a|0;b=b|0;var c=0,d=0,e=0,f=0;c=Fa-16|0;Fa=c;e=J[a+12>>2];if(!(H[23604]&1)){a=ba(2,21328)|0;H[23604]=1;J[5900]=a;}f=J[5900];a=Ra(16);d=J[b+12>>2];J[a+8>>2]=J[b+8>>2];J[a+12>>2]=d;d=J[b+4>>2];J[a>>2]=J[b>>2];J[a+4>>2]=d;J[c+8>>2]=a;da(f|0,e|0,3574,c+8|0);Fa=c+16|0;}function ki(a,b,c,d){a=a|0;b=b|0;c=c|0;var e=Q(0),f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0),k=Q(0);e=N[c>>2];g=N[c+8>>2];h=N[a+12>>2];i=N[c+12>>2];j=N[a+16>>2];k=Q(N[c+4>>2]+Q(Q(g*h)+Q(i*j)));f=N[a+8>>2];N[b+12>>2]=k+f;e=Q(e+Q(Q(i*h)-Q(j*g)));N[b+8>>2]=f+e;N[b+4>>2]=k-f;N[b>>2]=e-f;}function tj(){var a=0;a=Ra(44);J[a+4>>2]=0;J[a+8>>2]=0;J[a+36>>2]=1065353216;J[a+40>>2]=1050253722;J[a+28>>2]=0;J[a+32>>2]=1065353216;J[a+20>>2]=0;J[a+24>>2]=0;J[a>>2]=11;H[a+9|0]=0;H[a+10|0]=0;H[a+11|0]=0;H[a+12|0]=0;H[a+13|0]=0;H[a+14|0]=0;H[a+15|0]=0;H[a+16|0]=0;return a|0}function Bd(a,b){var c=Q(0),d=Q(0),e=Q(0),f=Q(0),g=Q(0);c=N[a+16>>2];d=N[a>>2];e=N[a+4>>2];f=N[a+12>>2];J[b+8>>2]=0;J[b+20>>2]=0;J[b+24>>2]=0;J[b+28>>2]=0;J[b+32>>2]=0;g=c;c=Q(Q(d*c)-Q(e*f));c=c!=Q(0)?Q(Q(1)/c):c;N[b>>2]=g*c;N[b+16>>2]=d*c;c=Q(-c);N[b+12>>2]=f*c;N[b+4>>2]=e*c;}function Za(a,b,c){var d=0;if(!c){return J[a+4>>2]==J[b+4>>2]}if((a|0)==(b|0)){return 1}d=J[a+4>>2];a=K[d|0];c=J[b+4>>2];b=K[c|0];a:{if(!a|(b|0)!=(a|0)){break a}while(1){b=K[c+1|0];a=K[d+1|0];if(!a){break a}c=c+1|0;d=d+1|0;if((a|0)==(b|0)){continue}break}}return (a|0)==(b|0)}function ji(a,b,c){a=a|0;b=b|0;c=Q(c);var d=Q(0),e=0,f=Q(0),g=Q(0);d=N[a+8>>2];c=Q(d*Q(d*Q(c*Q(3.1415927410125732))));N[b>>2]=c;e=J[a+16>>2];J[b+4>>2]=J[a+12>>2];J[b+8>>2]=e;d=c;c=N[a+8>>2];f=Q(Q(c*Q(.5))*c);c=N[a+12>>2];g=Q(c*c);c=N[a+16>>2];N[b+12>>2]=d*Q(f+Q(g+Q(c*c)));}function vj(){var a=0;a=Ra(48);J[a+4>>2]=0;J[a+8>>2]=0;J[a+20>>2]=0;J[a+24>>2]=0;J[a>>2]=3;J[a+44>>2]=0;J[a+36>>2]=1065353216;J[a+40>>2]=0;H[a+9|0]=0;H[a+10|0]=0;H[a+11|0]=0;H[a+12|0]=0;H[a+13|0]=0;H[a+14|0]=0;H[a+15|0]=0;H[a+16|0]=0;J[a+28>>2]=0;J[a+32>>2]=0;return a|0}function Kh(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0;e=Fa-48|0;Fa=e;f=J[J[a+48>>2]+12>>2];J[e+36>>2]=0;J[e+40>>2]=0;I[e+44>>1]=0;J[e+28>>2]=0;J[e+32>>2]=0;J[e+4>>2]=1;J[e+8>>2]=1008981770;J[e>>2]=12808;td(f,e,J[a+56>>2]);sd(b,e,c,J[J[a+52>>2]+12>>2],d);Fa=e+48|0;}function Ih(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0;e=Fa-48|0;Fa=e;f=J[J[a+48>>2]+12>>2];J[e+36>>2]=0;J[e+40>>2]=0;I[e+44>>1]=0;J[e+28>>2]=0;J[e+32>>2]=0;J[e+4>>2]=1;J[e+8>>2]=1008981770;J[e>>2]=12808;td(f,e,J[a+56>>2]);rd(b,e,c,J[J[a+52>>2]+12>>2],d);Fa=e+48|0;}function df(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0;if(Za(a,J[b+8>>2],0)){jc(b,c,d);return}e=J[a+12>>2];f=a+16|0;Kc(f,b,c,d);a=a+24|0;e=(e<<3)+f|0;a:{if(a>>>0>=e>>>0){break a}while(1){Kc(a,b,c,d);if(K[b+54|0]){break a}a=a+8|0;if(e>>>0>a>>>0){continue}break}}}function mi(a,b,c){a=a|0;b=b|0;c=c|0;var d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0);d=N[b+12>>2];e=N[a+12>>2];f=N[a+16>>2];g=N[b+8>>2];h=Q(N[c>>2]-Q(N[b>>2]+Q(Q(d*e)-Q(f*g))));d=Q(N[c+4>>2]-Q(N[b+4>>2]+Q(Q(g*e)+Q(d*f))));e=Q(Q(h*h)+Q(d*d));d=N[a+8>>2];return e<=Q(d*d)|0}function fj(){var a=0;a=Ra(48);J[a+4>>2]=0;J[a+8>>2]=0;J[a+20>>2]=0;J[a+24>>2]=0;J[a>>2]=8;H[a+9|0]=0;H[a+10|0]=0;H[a+11|0]=0;H[a+12|0]=0;H[a+13|0]=0;H[a+14|0]=0;H[a+15|0]=0;H[a+16|0]=0;J[a+28>>2]=0;J[a+32>>2]=0;J[a+36>>2]=0;J[a+40>>2]=0;J[a+44>>2]=0;return a|0}function _j(a,b,c){a=a|0;b=b|0;c=c|0;var d=Q(0),e=0,f=Q(0);a:{if(J[a>>2]!=2){break a}e=!c;c=L[a+4>>1];if(!(e|c&2)){J[a+144>>2]=0;c=c|2;I[a+4>>1]=c;}if(!(c&2)){break a}f=N[b+4>>2];d=N[a+120>>2];N[a+64>>2]=Q(d*N[b>>2])+N[a+64>>2];N[a+68>>2]=Q(d*f)+N[a+68>>2];}}function ij(){var a=0;a=Ra(40);J[a+4>>2]=0;J[a+8>>2]=0;J[a+36>>2]=0;J[a+28>>2]=1065353216;J[a+32>>2]=0;J[a+20>>2]=-1082130432;J[a+24>>2]=0;J[a>>2]=10;H[a+9|0]=0;H[a+10|0]=0;H[a+11|0]=0;H[a+12|0]=0;H[a+13|0]=0;H[a+14|0]=0;H[a+15|0]=0;H[a+16|0]=0;return a|0}function Db(a,b,c){var d=0,e=0,f=0,g=0;if(b){while(1){c=c-1|0;e=a;a=kl(a,b,10);d=Ga;f=c,g=e-jl(a,d,10,0)|48,H[f|0]=g;e=b>>>0>9;b=d;if(e){continue}break}}if(a){while(1){c=c-1|0;b=(a>>>0)/10|0;H[c|0]=a-P(b,10)|48;d=a>>>0>9;a=b;if(d){continue}break}}return c}function qj(){var a=0;a=Ra(40);J[a+4>>2]=0;J[a+8>>2]=0;J[a+36>>2]=1060320051;J[a+28>>2]=0;J[a+32>>2]=1084227584;J[a+20>>2]=0;J[a+24>>2]=0;J[a>>2]=5;H[a+9|0]=0;H[a+10|0]=0;H[a+11|0]=0;H[a+12|0]=0;H[a+13|0]=0;H[a+14|0]=0;H[a+15|0]=0;H[a+16|0]=0;return a|0}function Sc(a,b){var c=0,d=0,e=0;A(+a);d=v(1)|0;e=v(0)|0;c=d>>>20&2047;if((c|0)!=2047){if(!c){if(a==0){c=0;}else {a=Sc(a*0x10000000000000000,b);c=J[b>>2]+-64|0;}J[b>>2]=c;return a}J[b>>2]=c-1022;x(0,e|0);x(1,d&-2146435073|1071644672);a=+z();}return a}function Rk(a,b){a=a|0;b=b|0;var c=0,d=0,e=0;d=a+16|0;a=J[d>>2];a:{b:{if(!a){break b}c=d;while(1){e=M[a+16>>2]<b>>>0;c=e?c:a;a=J[(e?a+4|0:a)>>2];if(a){continue}break}if((c|0)==(d|0)){break b}if(M[c+16>>2]<=b>>>0){break a}}c=d;}return (c|0)!=(d|0)|0}function Ab(a,b,c){var d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0),i=Q(0),j=Q(0);e=N[b>>2];f=N[b+16>>2];g=N[b+4>>2];h=N[b+12>>2];d=Q(Q(e*f)-Q(g*h));d=d!=Q(0)?Q(Q(1)/d):d;j=e;e=N[c+4>>2];i=N[c>>2];N[a+4>>2]=d*Q(Q(j*e)-Q(i*g));N[a>>2]=d*Q(Q(f*i)-Q(e*h));}function vb(a,b){var c=0,d=0,e=0;c=J[a+102796>>2];d=P(c,12)+a|0;e=d+102412|0;a:{if(K[d+102408|0]){Wa(b);b=J[e-8>>2];c=J[a+102796>>2];break a}b=J[e-8>>2];J[a+102400>>2]=J[a+102400>>2]-b;}J[a+102796>>2]=c-1;J[a+102404>>2]=J[a+102404>>2]-b;}function Ck(a,b,c){a=a|0;b=b|0;c=c|0;var d=Q(0),e=Q(0),f=Q(0),g=Q(0);d=N[c>>2];e=N[b>>2];f=N[b+4>>2];g=N[c+4>>2];N[a+4>>2]=f<g?f:g;N[a>>2]=d>e?e:d;d=N[c+8>>2];e=N[b+8>>2];f=N[b+12>>2];g=N[c+12>>2];N[a+12>>2]=f>g?f:g;N[a+8>>2]=d<e?e:d;}function Qc(a){var b=0,c=0,d=0;if(H[J[a>>2]]-48>>>0>=10){return 0}while(1){d=J[a>>2];c=-1;if(b>>>0<=214748364){c=H[d|0]-48|0;b=P(b,10);c=(c|0)>(b^2147483647)?-1:c+b|0;}J[a>>2]=d+1;b=c;if(H[d+1|0]-48>>>0<10){continue}break}return b}function hl(a,b,c,d){a=a|0;b=b|0;c=Q(c);d=Q(d);var e=0,f=0,g=0;e=Fa-16|0;Fa=e;N[e+12>>2]=d;N[e+8>>2]=c;g=J[a>>2];f=J[a+4>>2]-g>>3;a:{if(f>>>0<b>>>0){Hd(a,b-f|0,e+8|0);break a}if(b>>>0>=f>>>0){break a}J[a+4>>2]=(b<<3)+g;}Fa=e+16|0;}function dh(a,b){a=a|0;b=b|0;var c=0;if(!(N[b>>2]==N[a+68>>2]&N[b+4>>2]==N[a+72>>2])){c=J[a+48>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;c=J[a+52>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;c=J[b+4>>2];J[a+68>>2]=J[b>>2];J[a+72>>2]=c;}}function Qi(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=Fa-16|0;Fa=d;e=a;a=J[b>>2];if(J[b+4>>2]-a>>3>>>0>c>>>0){b=Ra(8);c=a+(c<<3)|0;a=J[c+4>>2];J[b>>2]=J[c>>2];J[b+4>>2]=a;J[d+8>>2]=b;a=xa(19432,d+8|0)|0;}else {a=1;}J[e>>2]=a;Fa=d+16|0;}function Sd(a,b,c){a=a|0;b=b|0;c=Q(c);var d=0,e=0,f=0;d=Fa-16|0;Fa=d;e=J[a>>2];f=d+8|0;a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){e=J[e+J[b>>2]>>2];}Ha[e|0](f,b,c);a=Ra(8);b=J[d+12>>2];J[a>>2]=J[d+8>>2];J[a+4>>2]=b;Fa=d+16|0;return a|0}function Ag(a,b,c){a=a|0;b=Q(b);c=Q(c);var d=0;if(!(N[a+120>>2]==b&N[a+124>>2]==c)){d=J[a+48>>2];J[d+144>>2]=0;I[d+4>>1]=L[d+4>>1]|2;d=J[a+52>>2];J[d+144>>2]=0;I[d+4>>1]=L[d+4>>1]|2;N[a+124>>2]=c;N[a+120>>2]=b;J[a+112>>2]=0;}}function ag(a,b,c){a=a|0;b=Q(b);c=Q(c);var d=0;if(!(N[a+120>>2]==b&N[a+124>>2]==c)){d=J[a+48>>2];J[d+144>>2]=0;I[d+4>>1]=L[d+4>>1]|2;d=J[a+52>>2];J[d+144>>2]=0;I[d+4>>1]=L[d+4>>1]|2;N[a+124>>2]=c;N[a+120>>2]=b;J[a+92>>2]=0;}}function Uj(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0,f=0;d=Fa-16|0;Fa=d;e=J[a>>2];f=d+8|0;a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){e=J[e+J[b>>2]>>2];}Ha[e|0](f,b,c);a=Ra(8);b=J[d+12>>2];J[a>>2]=J[d+8>>2];J[a+4>>2]=b;Fa=d+16|0;return a|0}function kk(a,b){a=a|0;b=b|0;var c=0;c=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){c=J[c+J[b>>2]>>2];}a=Ha[c|0](b)|0;b=Ra(16);c=J[a+12>>2];J[b+8>>2]=J[a+8>>2];J[b+12>>2]=c;c=J[a+4>>2];J[b>>2]=J[a>>2];J[b+4>>2]=c;return b|0}function Li(a,b){a=a|0;b=b|0;var c=0,d=0,e=0;c=Fa-16|0;Fa=c;a=J[a+8>>2];if(!(H[23540]&1)){d=ba(2,20332)|0;H[23540]=1;J[5884]=d;}d=J[5884];J[c+8>>2]=b;e=+ua(d|0,a|0,4735,c+4|0,c+8|0);ta(J[c+4>>2]);Fa=c+16|0;return e!=0|0}function Dk(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=Q(0);d=N[b>>2];c=N[a+4>>2];e=N[b+4>>2];N[a+4>>2]=c<e?c:e;c=N[a>>2];N[a>>2]=c<d?c:d;d=N[b+8>>2];c=N[a+12>>2];e=N[b+12>>2];N[a+12>>2]=c>e?c:e;c=N[a+8>>2];N[a+8>>2]=c>d?c:d;}function kd(a,b){a=a|0;b=b|0;var c=0,d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0);c=J[b+52>>2];h=N[c+12>>2];d=N[c+20>>2];e=N[b+100>>2];f=N[c+24>>2];g=N[b+104>>2];N[a+4>>2]=Q(Q(d*e)+Q(f*g))+N[c+16>>2];N[a>>2]=h+Q(Q(f*e)-Q(g*d));}function Hg(a,b,c){a=a|0;b=b|0;c=Q(c);var d=Q(0),e=Q(0),f=Q(0),g=Q(0);f=N[b+184>>2];g=N[b+192>>2];d=N[b+104>>2];e=Q(N[b+116>>2]+N[b+112>>2]);N[a+4>>2]=Q(Q(d*N[b+196>>2])+Q(e*N[b+188>>2]))*c;N[a>>2]=Q(Q(d*g)+Q(f*e))*c;}function ok(){var a=0;a=Ra(52);J[a+4>>2]=0;J[a+8>>2]=0;J[a+44>>2]=0;J[a+48>>2]=1065353216;J[a+36>>2]=257;H[a+40|0]=1;J[a>>2]=0;J[a+12>>2]=0;J[a+16>>2]=0;J[a+20>>2]=0;J[a+24>>2]=0;J[a+28>>2]=0;J[a+32>>2]=0;return a|0}function jl(a,b,c,d){var e=0,f=0,g=0,h=0,i=0,j=0;e=c>>>16|0;f=a>>>16|0;j=P(e,f);g=c&65535;h=a&65535;i=P(g,h);f=(i>>>16|0)+P(f,g)|0;e=(f&65535)+P(e,h)|0;Ga=(P(b,c)+j|0)+P(a,d)+(f>>>16)+(e>>>16)|0;return i&65535|e<<16}function qd(a,b){a=a|0;b=b|0;var c=0,d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0);c=J[b+48>>2];h=N[c+12>>2];d=N[c+20>>2];e=N[b+80>>2];f=N[c+24>>2];g=N[b+84>>2];N[a+4>>2]=Q(Q(d*e)+Q(f*g))+N[c+16>>2];N[a>>2]=h+Q(Q(f*e)-Q(g*d));}function pd(a,b){a=a|0;b=b|0;var c=0,d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0);c=J[b+52>>2];h=N[c+12>>2];d=N[c+20>>2];e=N[b+88>>2];f=N[c+24>>2];g=N[b+92>>2];N[a+4>>2]=Q(Q(d*e)+Q(f*g))+N[c+16>>2];N[a>>2]=h+Q(Q(f*e)-Q(g*d));}function ni(a,b){a=a|0;b=b|0;var c=0;b=Ya(b,20);J[b+4>>2]=0;J[b+8>>2]=0;J[b>>2]=12488;J[b+12>>2]=0;J[b+16>>2]=0;c=J[a+8>>2];J[b+4>>2]=J[a+4>>2];J[b+8>>2]=c;c=J[a+16>>2];J[b+12>>2]=J[a+12>>2];J[b+16>>2]=c;return b|0}function ld(a,b){a=a|0;b=b|0;var c=0,d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0);c=J[b+48>>2];h=N[c+12>>2];d=N[c+20>>2];e=N[b+92>>2];f=N[c+24>>2];g=N[b+96>>2];N[a+4>>2]=Q(Q(d*e)+Q(f*g))+N[c+16>>2];N[a>>2]=h+Q(Q(f*e)-Q(g*d));}function ck(a,b,c){a=a|0;b=b|0;c=c|0;var d=0;a:{if(J[a>>2]!=2){break a}d=!c;c=L[a+4>>1];if(!(d|c&2)){J[a+144>>2]=0;c=c|2;I[a+4>>1]=c;}if(!(c&2)){break a}N[a+76>>2]=N[b>>2]+N[a+76>>2];N[a+80>>2]=N[b+4>>2]+N[a+80>>2];}}function Ug(a,b){a=a|0;b=b|0;var c=0,d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0);c=J[b+52>>2];h=N[c+12>>2];d=N[c+20>>2];e=N[b+68>>2];f=N[c+24>>2];g=N[b+72>>2];N[a+4>>2]=Q(Q(d*e)+Q(f*g))+N[c+16>>2];N[a>>2]=h+Q(Q(f*e)-Q(g*d));}function Tb(a,b){a=a|0;b=b|0;var c=0,d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0);c=J[b+48>>2];h=N[c+12>>2];d=N[c+20>>2];e=N[b+68>>2];f=N[c+24>>2];g=N[b+72>>2];N[a+4>>2]=Q(Q(d*e)+Q(f*g))+N[c+16>>2];N[a>>2]=h+Q(Q(f*e)-Q(g*d));}function Sb(a,b){a=a|0;b=b|0;var c=0,d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0);c=J[b+52>>2];h=N[c+12>>2];d=N[c+20>>2];e=N[b+76>>2];f=N[c+24>>2];g=N[b+80>>2];N[a+4>>2]=Q(Q(d*e)+Q(f*g))+N[c+16>>2];N[a>>2]=h+Q(Q(f*e)-Q(g*d));}function Lf(a,b){a=a|0;b=b|0;var c=0,d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0);c=J[b+48>>2];h=N[c+12>>2];d=N[c+20>>2];e=N[b+76>>2];f=N[c+24>>2];g=N[b+80>>2];N[a+4>>2]=Q(Q(d*e)+Q(f*g))+N[c+16>>2];N[a>>2]=h+Q(Q(f*e)-Q(g*d));}function Kf(a,b){a=a|0;b=b|0;var c=0,d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0);c=J[b+52>>2];h=N[c+12>>2];d=N[c+20>>2];e=N[b+84>>2];f=N[c+24>>2];g=N[b+88>>2];N[a+4>>2]=Q(Q(d*e)+Q(f*g))+N[c+16>>2];N[a>>2]=h+Q(Q(f*e)-Q(g*d));}function Fc(a,b){a=a|0;b=b|0;var c=0,d=0,e=0;c=Fa-16|0;Fa=c;d=J[a>>2];e=c+8|0;a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){d=J[d+J[b>>2]>>2];}Ha[d|0](e,b);a=Ra(8);b=J[c+12>>2];J[a>>2]=J[c+8>>2];J[a+4>>2]=b;Fa=c+16|0;return a|0}function gi(a,b){a=a|0;b=b|0;var c=0;b=Ya(b,152);J[b+148>>2]=0;J[b+4>>2]=2;J[b+8>>2]=1008981770;J[b>>2]=12576;J[b+12>>2]=0;J[b+16>>2]=0;c=J[a+8>>2];J[b+4>>2]=J[a+4>>2];J[b+8>>2]=c;eb(b+12|0,a+12|0,140);return b|0}function Gi(a){a=a|0;var b=0,c=0;J[a>>2]=20780;Yb(a+12|0,J[a+16>>2]);J[a>>2]=20812;if(K[a+4|0]){b=J[a+8>>2];if(!(H[23532]&1)){c=ba(1,20328)|0;H[23532]=1;J[5882]=c;}da(J[5882],b|0,2289,0);}fa(J[a+8>>2]);return a|0}function jc(a,b,c){var d=0;d=J[a+16>>2];if(!d){J[a+36>>2]=1;J[a+24>>2]=c;J[a+16>>2]=b;return}a:{if((b|0)==(d|0)){if(J[a+24>>2]!=2){break a}J[a+24>>2]=c;return}H[a+54|0]=1;J[a+24>>2]=2;J[a+36>>2]=J[a+36>>2]+1;}}function wf(a,b){a=a|0;b=b|0;a:{if(K[a+102972|0]==(b|0)){break a}H[a+102972|0]=b;if(b){break a}b=J[a+102948>>2];if(!b){break a}while(1){J[b+144>>2]=0;I[b+4>>1]=L[b+4>>1]|2;b=J[b+96>>2];if(b){continue}break}}}function uc(a,b){var c=0,d=0;c=J[a+40>>2];if((c|0)==J[a+36>>2]){J[a+36>>2]=c<<1;d=J[a+32>>2];c=_a(c<<3);J[a+32>>2]=c;eb(c,d,J[a+40>>2]<<2);Wa(d);c=J[a+40>>2];}J[J[a+32>>2]+(c<<2)>>2]=b;J[a+40>>2]=J[a+40>>2]+1;}function gf(a,b,c,d,e,f,g,h,i,j){a=a|0;b=b|0;c=c|0;d=Q(d);e=Q(e);f=Q(f);g=g|0;h=h|0;i=i|0;j=j|0;I[a+22>>1]=h;H[a+20|0]=g;N[a+16>>2]=f;N[a+12>>2]=e;N[a+8>>2]=d;J[a+4>>2]=c;J[a>>2]=b;I[a+26>>1]=j;I[a+24>>1]=i;}function Xj(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=Q(0),f=0;c=N[a+116>>2];N[b>>2]=c;d=c;c=N[a+28>>2];e=Q(c*c);c=N[a+32>>2];N[b+12>>2]=Q(d*Q(e+Q(c*c)))+N[a+124>>2];f=J[a+32>>2];J[b+4>>2]=J[a+28>>2];J[b+8>>2]=f;}function Fi(a){a=a|0;var b=0,c=0;J[a>>2]=20780;Yb(a+12|0,J[a+16>>2]);J[a>>2]=20812;if(K[a+4|0]){b=J[a+8>>2];if(!(H[23532]&1)){c=ba(1,20328)|0;H[23532]=1;J[5882]=c;}da(J[5882],b|0,2289,0);}fa(J[a+8>>2]);Wa(a);}function cb(a,b,c,d,e){var f=0;f=Fa-256|0;Fa=f;if(!(e&73728|(c|0)<=(d|0))){d=c-d|0;c=d>>>0<256;xb(f,b&255,c?d:256);if(!c){while(1){bb(a,f,256);d=d-256|0;if(d>>>0>255){continue}break}}bb(a,f,d);}Fa=f+256|0;}function Vj(a,b,c){a=a|0;b=b|0;c=c|0;var d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0);h=N[b+12>>2];d=N[b+20>>2];e=N[c>>2];f=N[b+24>>2];g=N[c+4>>2];N[a+4>>2]=Q(Q(d*e)+Q(f*g))+N[b+16>>2];N[a>>2]=h+Q(Q(f*e)-Q(g*d));}function Jf(a,b,c){a=a|0;b=b|0;c=Q(c);var d=Q(0),e=Q(0),f=Q(0),g=Q(0);f=N[b+184>>2];g=N[b+176>>2];d=N[b+108>>2];e=N[b+116>>2];N[a>>2]=Q(Q(d*N[b+180>>2])+Q(e*N[b+172>>2]))*c;N[a+4>>2]=Q(Q(d*f)+Q(e*g))*c;}function Re(a,b,c){a=a|0;b=b|0;c=c|0;var d=Q(0),e=Q(0),f=Q(0),g=Q(0),h=Q(0);h=N[b>>2];d=N[b+8>>2];e=N[c>>2];f=N[b+12>>2];g=N[c+4>>2];N[a+4>>2]=Q(Q(d*e)+Q(f*g))+N[b+4>>2];N[a>>2]=h+Q(Q(f*e)-Q(g*d));}function gk(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0),e=0;if(J[a>>2]){c=N[b>>2];d=Q(c*c);c=N[b+4>>2];if(Q(d+Q(c*c))>Q(0)){J[a+144>>2]=0;I[a+4>>1]=L[a+4>>1]|2;}e=J[b+4>>2];J[a+64>>2]=J[b>>2];J[a+68>>2]=e;}}function Zj(a,b,c){a=a|0;b=Q(b);c=c|0;var d=0;a:{if(J[a>>2]!=2){break a}d=!c;c=L[a+4>>1];if(!(d|c&2)){J[a+144>>2]=0;c=c|2;I[a+4>>1]=c;}if(!(c&2)){break a}N[a+72>>2]=Q(N[a+128>>2]*b)+N[a+72>>2];}}function Sj(a,b,c){a=a|0;b=b|0;c=c|0;var d=Q(0),e=Q(0),f=Q(0),g=Q(0);d=N[b+24>>2];e=Q(N[c+4>>2]-N[b+16>>2]);f=N[b+20>>2];g=Q(N[c>>2]-N[b+12>>2]);N[a+4>>2]=Q(d*e)-Q(f*g);N[a>>2]=Q(d*g)+Q(e*f);}function Qj(a,b,c){a=a|0;b=b|0;c=c|0;var d=Q(0),e=Q(0),f=Q(0),g=Q(0);e=N[b+48>>2];f=N[b+64>>2];g=N[c+4>>2];d=N[b+72>>2];N[a+4>>2]=Q(d*Q(N[c>>2]-N[b+44>>2]))+N[b+68>>2];N[a>>2]=f-Q(d*Q(g-e));}function Jj(a,b){a=a|0;b=b|0;var c=0;c=L[a+4>>1];a:{if(b){I[a+4>>1]=c|2;break a}J[a+64>>2]=0;J[a+68>>2]=0;I[a+4>>1]=c&65533;J[a+72>>2]=0;J[a+76>>2]=0;J[a+80>>2]=0;J[a+84>>2]=0;}J[a+144>>2]=0;}function Tc(a){var b=0;b=J[a+72>>2];J[a+72>>2]=b-1|b;b=J[a>>2];if(b&8){J[a>>2]=b|32;return -1}J[a+4>>2]=0;J[a+8>>2]=0;b=J[a+44>>2];J[a+28>>2]=b;J[a+20>>2]=b;J[a+16>>2]=b+J[a+48>>2];return 0}function Bg(a,b){a=a|0;b=b|0;var c=0;if(K[a+136|0]!=(b|0)){c=J[a+48>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;c=J[a+52>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;J[a+112>>2]=0;H[a+136|0]=b;}}function bg(a,b){a=a|0;b=b|0;var c=0;if(K[a+112|0]!=(b|0)){c=J[a+48>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;c=J[a+52>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;J[a+92>>2]=0;H[a+112|0]=b;}}function Nh(a,b,c){a=a|0;b=b|0;c=Q(c);var d=Q(0),e=Q(0),f=Q(0);J[b>>2]=0;c=N[a+24>>2];d=N[a+16>>2];e=N[a+20>>2];f=N[a+12>>2];J[b+12>>2]=0;N[b+8>>2]=Q(d+c)*Q(.5);N[b+4>>2]=Q(f+e)*Q(.5);}function Wi(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=Fa-16|0;Fa=d;e=J[b+4>>2];b=J[b>>2];if(e-b>>2>>>0>c>>>0){J[d+8>>2]=J[b+(c<<2)>>2];b=xa(18964,d+8|0)|0;}else {b=1;}J[a>>2]=b;Fa=d+16|0;}function $g(a,b){a=a|0;b=b|0;var c=0;if(!(N[b>>2]==N[a+76>>2]&N[b+4>>2]==N[a+80>>2])){c=J[a+52>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;c=J[b+4>>2];J[a+76>>2]=J[b>>2];J[a+80>>2]=c;}}function bk(a,b,c){a=a|0;b=Q(b);c=c|0;var d=0;a:{if(J[a>>2]!=2){break a}d=!c;c=L[a+4>>1];if(!(d|c&2)){J[a+144>>2]=0;c=c|2;I[a+4>>1]=c;}if(!(c&2)){break a}N[a+84>>2]=N[a+84>>2]+b;}}function xi(a){a=a|0;var b=0,c=0;J[a>>2]=21200;if(K[a+8|0]){b=J[a+12>>2];if(!(H[23532]&1)){c=ba(1,20328)|0;H[23532]=1;J[5882]=c;}da(J[5882],b|0,2289,0);}fa(J[a+12>>2]);return a|0}function Jc(a,b){var c=0;a:{if(!b){break a}b=lb(b,18772);if(!b|J[b+8>>2]&(J[a+8>>2]^-1)){break a}if(!Za(J[a+12>>2],J[b+12>>2],0)){break a}c=Za(J[a+16>>2],J[b+16>>2],0);}return c}function Ni(a){a=a|0;var b=0,c=0;J[a>>2]=20316;if(K[a+4|0]){b=J[a+8>>2];if(!(H[23532]&1)){c=ba(1,20328)|0;H[23532]=1;J[5882]=c;}da(J[5882],b|0,2289,0);}fa(J[a+8>>2]);return a|0}function Ki(a){a=a|0;var b=0,c=0;J[a>>2]=20544;if(K[a+4|0]){b=J[a+8>>2];if(!(H[23532]&1)){c=ba(1,20328)|0;H[23532]=1;J[5882]=c;}da(J[5882],b|0,2289,0);}fa(J[a+8>>2]);return a|0}function Ai(a){a=a|0;var b=0,c=0;J[a>>2]=20812;if(K[a+4|0]){b=J[a+8>>2];if(!(H[23532]&1)){c=ba(1,20328)|0;H[23532]=1;J[5882]=c;}da(J[5882],b|0,2289,0);}fa(J[a+8>>2]);return a|0}function Yi(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0,f=0;e=Fa-16|0;Fa=e;f=J[a+4>>2];b=(f>>1)+b|0;a=J[a>>2];a=f&1?J[J[b>>2]+a>>2]:a;J[e+12>>2]=d;Ha[a|0](b,c,e+12|0);Fa=e+16|0;}function Cb(a){var b=0,c=0;b=J[5876];c=a+7&-8;a=b+c|0;a:{if(a>>>0<=b>>>0?c:0){break a}if(a>>>0>Ia()<<16>>>0){if(!(Aa(a|0)|0)){break a}}J[5876]=a;return b}J[6386]=48;return -1}function wi(a){a=a|0;var b=0,c=0;J[a>>2]=21200;if(K[a+8|0]){b=J[a+12>>2];if(!(H[23532]&1)){c=ba(1,20328)|0;H[23532]=1;J[5882]=c;}da(J[5882],b|0,2289,0);}fa(J[a+12>>2]);Wa(a);}function ub(a,b){a=a|0;b=b|0;var c=0;c=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){c=J[c+J[b>>2]>>2];}a=Ha[c|0](b)|0;b=Ra(8);c=J[a+4>>2];J[b>>2]=J[a>>2];J[b+4>>2]=c;return b|0}function yg(a,b){a=a|0;b=b|0;var c=0;if(K[a+137|0]!=(b|0)){c=J[a+48>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;c=J[a+52>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;H[a+137|0]=b;}}function vd(a,b){if(!(J[a>>2]|J[b>>2])){return 0}a=J[a+108>>2];if(a){while(1){if(!(K[J[a+4>>2]+61|0]|J[a>>2]!=(b|0))){return 0}a=J[a+12>>2];if(a){continue}break}}return 1}function gg(a,b){a=a|0;b=b|0;var c=0;if(K[a+100|0]!=(b|0)){c=J[a+48>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;c=J[a+52>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;H[a+100|0]=b;}}function Gf(a,b){a=a|0;b=b|0;var c=0;if(K[a+128|0]!=(b|0)){c=J[a+48>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;c=J[a+52>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;H[a+128|0]=b;}}function zi(a){a=a|0;var b=0,c=0;J[a>>2]=20812;if(K[a+4|0]){b=J[a+8>>2];if(!(H[23532]&1)){c=ba(1,20328)|0;H[23532]=1;J[5882]=c;}da(J[5882],b|0,2289,0);}fa(J[a+8>>2]);Wa(a);}function xg(a,b){a=a|0;b=Q(b);var c=0;if(N[a+132>>2]!=b){c=J[a+48>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;c=J[a+52>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;N[a+132>>2]=b;}}function wg(a,b){a=a|0;b=Q(b);var c=0;if(N[a+128>>2]!=b){c=J[a+48>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;c=J[a+52>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;N[a+128>>2]=b;}}function eg(a,b){a=a|0;b=Q(b);var c=0;if(N[a+108>>2]!=b){c=J[a+48>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;c=J[a+52>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;N[a+108>>2]=b;}}function dg(a,b){a=a|0;b=Q(b);var c=0;if(N[a+104>>2]!=b){c=J[a+48>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;c=J[a+52>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;N[a+104>>2]=b;}}function Mi(a){a=a|0;var b=0,c=0;J[a>>2]=20316;if(K[a+4|0]){b=J[a+8>>2];if(!(H[23532]&1)){c=ba(1,20328)|0;H[23532]=1;J[5882]=c;}da(J[5882],b|0,2289,0);}fa(J[a+8>>2]);Wa(a);}function Ji(a){a=a|0;var b=0,c=0;J[a>>2]=20544;if(K[a+4|0]){b=J[a+8>>2];if(!(H[23532]&1)){c=ba(1,20328)|0;H[23532]=1;J[5882]=c;}da(J[5882],b|0,2289,0);}fa(J[a+8>>2]);Wa(a);}function Ff(a,b){a=a|0;b=Q(b);var c=0;if(N[a+124>>2]!=b){c=J[a+48>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;c=J[a+52>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;N[a+124>>2]=b;}}function Ef(a,b){a=a|0;b=Q(b);var c=0;if(N[a+120>>2]!=b){c=J[a+48>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;c=J[a+52>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;N[a+120>>2]=b;}}function ch(a,b){a=a|0;b=Q(b);var c=0;if(N[a+76>>2]!=b){c=J[a+48>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;c=J[a+52>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;N[a+76>>2]=b;}}function Le(a,b){a=a|0;b=Q(b);var c=0,d=0;c=(C(b),v(2));d=c&2147483647;if(!(!d|c-1>>>0<8388607|d-8388608>>>0<2130706432&(c|0)>=0)){ya(4202,3777,300,1107);B();}N[a>>2]=b;}function lf(){var a=0;a=Ra(28);I[a+22>>1]=1;I[a+24>>1]=65535;J[a+12>>2]=0;J[a+16>>2]=0;J[a+8>>2]=1045220557;J[a>>2]=0;J[a+4>>2]=0;I[a+26>>1]=0;H[a+20|0]=0;return a|0}function $i(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=Fa-16|0;Fa=d;e=J[a+4>>2];b=(e>>1)+b|0;a=J[a>>2];a=e&1?J[J[b>>2]+a>>2]:a;J[d+12>>2]=c;Ha[a|0](b,d+12|0);Fa=d+16|0;}function Xk(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=Q(f);var g=0;g=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){g=J[g+J[b>>2]>>2];}return Q(Q(Ha[g|0](b,c,d,e,f)))}function Tj(a,b,c){a=a|0;b=b|0;c=c|0;var d=Q(0),e=Q(0),f=Q(0),g=Q(0);d=N[b+20>>2];e=N[c>>2];f=N[b+24>>2];g=N[c+4>>2];N[a+4>>2]=Q(d*e)+Q(f*g);N[a>>2]=Q(f*e)-Q(g*d);}function Rj(a,b,c){a=a|0;b=b|0;c=c|0;var d=Q(0),e=Q(0),f=Q(0),g=Q(0);d=N[b+24>>2];e=N[c+4>>2];f=N[b+20>>2];g=N[c>>2];N[a+4>>2]=Q(d*e)-Q(f*g);N[a>>2]=Q(d*g)+Q(f*e);}function Kc(a,b,c,d){var e=0,f=0;e=J[a+4>>2];f=J[a>>2];a=0;a:{if(!c){break a}a=e>>8;if(!(e&1)){break a}a=J[a+J[c>>2]>>2];}Ha[J[J[f>>2]+28>>2]](f,b,a+c|0,e&2?d:2);}function Ge(a,b,c){a=a|0;b=b|0;c=c|0;a=Ha[a|0](b,c)|0;b=Ra(16);c=J[a+12>>2];J[b+8>>2]=J[a+8>>2];J[b+12>>2]=c;c=J[a+4>>2];J[b>>2]=J[a>>2];J[b+4>>2]=c;return b|0}function Zi(a,b){a=a|0;b=b|0;var c=0,d=0;c=b;d=a- -64|0;b=J[a+48>>2];a=J[a+52>>2];Dd(c,d,J[b+8>>2]+12|0,N[J[b+12>>2]+8>>2],J[a+8>>2]+12|0,N[J[a+12>>2]+8>>2]);}function nb(a){var b=0,c=0;b=a*a;c=b*a;return Q(c*(b*b)*(b*2718311493989822e-21+-.00019839334836096632)+(c*(b*.008333329385889463+-.16666666641626524)+a))}function ng(a,b){a=a|0;b=b|0;N[a+68>>2]=N[a+68>>2]-N[b>>2];N[a+72>>2]=N[a+72>>2]-N[b+4>>2];N[a+76>>2]=N[a+76>>2]-N[b>>2];N[a+80>>2]=N[a+80>>2]-N[b+4>>2];}function xf(a,b,c){a=a|0;b=b|0;c=c|0;var d=0;d=Fa-16|0;Fa=d;Ha[a|0](d+8|0,b,c);a=Ra(8);b=J[d+12>>2];J[a>>2]=J[d+8>>2];J[a+4>>2]=b;Fa=d+16|0;return a|0}function Nk(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=Q(d);e=e|0;f=f|0;var g=0;g=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){g=J[g+J[b>>2]>>2];}Ha[g|0](b,c,d,e,f);}function Ak(a,b){a=a|0;b=b|0;var c=0;if(!(!(N[b+8>>2]<=N[a+8>>2])|(!(N[a>>2]<=N[b>>2])|!(N[a+4>>2]<=N[b+4>>2])))){c=N[b+12>>2]<=N[a+12>>2];}return c|0}function Ri(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;e=J[a>>2];d=J[a+4>>2]-e>>3;if(d>>>0<b>>>0){Hd(a,b-d|0,c);return}if(b>>>0<d>>>0){J[a+4>>2]=(b<<3)+e;}}function Ye(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;if(Za(a,J[b+8>>2],f)){ic(b,c,d,e);return}a=J[a+8>>2];Ha[J[J[a>>2]+20>>2]](a,b,c,d,e,f);}function Qk(a){a=a|0;var b=0;b=Ra(24);H[b+4|0]=0;J[b+8>>2]=J[a>>2];J[a>>2]=0;a=b+16|0;J[a>>2]=0;J[a+4>>2]=0;J[b>>2]=20780;J[b+12>>2]=a;return b|0}function ob(a){var b=0;a=a*a;b=a*a;return Q(a*b*(a*2439044879627741e-20+-.001388676377460993)+(b*.04166662332373906+(a*-.499999997251031+1)))}function wj(a,b){a=a|0;b=b|0;var c=0;c=Fa-16|0;Fa=c;Ha[a|0](c+8|0,b);a=Ra(8);b=J[c+12>>2];J[a>>2]=J[c+8>>2];J[a+4>>2]=b;Fa=c+16|0;return a|0}function Zg(){var a=0;a=Ra(32);J[a>>2]=0;J[a+4>>2]=0;J[a+24>>2]=0;J[a+28>>2]=0;J[a+16>>2]=0;J[a+20>>2]=0;J[a+8>>2]=0;J[a+12>>2]=0;return a|0}function Gd(a,b,c){a=a|0;b=b|0;c=c|0;var d=0;d=Fa-16|0;Fa=d;Ha[J[a>>2]](d+12|0,b,c);wa(J[d+12>>2]);a=J[d+12>>2];fa(a|0);Fa=d+16|0;return a|0}function uk(a,b,c,d,e){a=a|0;b=b|0;c=Q(c);d=d|0;e=e|0;var f=0;f=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){f=J[f+J[b>>2]>>2];}Ha[f|0](b,c,d,e);}function Ok(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=Q(d);e=e|0;var f=0;f=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){f=J[f+J[b>>2]>>2];}Ha[f|0](b,c,d,e);}function vk(a,b){a=a|0;b=b|0;var c=0,d=0;c=Fa-16|0;Fa=c;d=J[b+4>>2];J[c+8>>2]=J[b>>2];J[c+12>>2]=d;a=Ha[a|0](c+8|0)|0;Fa=c+16|0;return a|0}function $b(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var f=0;f=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){f=J[f+J[b>>2]>>2];}Ha[f|0](b,c,d,e);}function zk(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0;e=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){e=J[e+J[b>>2]>>2];}return Ha[e|0](b,c,d)|0}function hc(a,b,c,d,e,f){var g=0,h=0;g=J[a+4>>2];h=g>>8;a=J[a>>2];if(g&1){h=J[J[d>>2]+h>>2];}Ha[J[J[a>>2]+20>>2]](a,b,c,d+h|0,g&2?e:2,f);}function Ui(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0;e=Fa-16|0;Fa=e;a=J[a>>2];J[e+12>>2]=d;a=Ha[a|0](b,c,e+12|0)|0;Fa=e+16|0;return a|0}function Se(a,b){a=a|0;b=b|0;a=Ha[a|0](b)|0;b=Ra(6);I[b+4>>1]=L[a+4>>1];a=L[a>>1]|L[a+2>>1]<<16;I[b>>1]=a;I[b+2>>1]=a>>>16;return b|0}function jf(a,b,c,d,e,f,g,h,i,j,k){a=a|0;b=b|0;c=c|0;d=d|0;e=Q(e);f=Q(f);g=Q(g);h=h|0;i=i|0;j=j|0;k=k|0;Ha[a|0](b,c,d,e,f,g,h,i,j,k);}function Lb(a,b,c){a=a|0;b=b|0;c=Q(c);var d=0;d=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){d=J[d+J[b>>2]>>2];}return Q(Q(Ha[d|0](b,c)))}function Qb(a,b,c,d,e){var f=0,g=0;f=J[a+4>>2];g=f>>8;a=J[a>>2];if(f&1){g=J[J[c>>2]+g>>2];}Ha[J[J[a>>2]+24>>2]](a,b,c+g|0,f&2?d:2,e);}function Ra(a){var b=0;a=a>>>0<=1?1:a;a:{while(1){b=_a(a);if(b){break a}b=J[6558];if(b){Ha[b|0]();continue}break}ma();B();}return b}function Md(a,b,c,d){a=a|0;b=b|0;c=Q(c);d=Q(d);var e=0;e=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){e=J[e+J[b>>2]>>2];}Ha[e|0](b,c,d);}function vh(a,b,c){a=a|0;b=b|0;c=Q(c);var d=Q(0),e=Q(0);e=N[b+244>>2];d=N[b+156>>2];N[a>>2]=Q(d*N[b+240>>2])*c;N[a+4>>2]=Q(d*e)*c;}function ue(){var a=0;a=Ra(152);J[a+148>>2]=0;J[a+4>>2]=2;J[a+8>>2]=1008981770;J[a>>2]=12576;J[a+12>>2]=0;J[a+16>>2]=0;return a|0}function pg(a,b,c){a=a|0;b=b|0;c=Q(c);var d=Q(0),e=Q(0);e=N[b+140>>2];d=N[b+116>>2];N[a>>2]=Q(d*N[b+136>>2])*c;N[a+4>>2]=Q(d*e)*c;}function lk(a,b,c,d){a=a|0;b=b|0;c=c|0;d=Q(d);var e=0;e=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){e=J[e+J[b>>2]>>2];}Ha[e|0](b,c,d);}function ak(a,b,c,d){a=a|0;b=b|0;c=Q(c);d=d|0;var e=0;e=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){e=J[e+J[b>>2]>>2];}Ha[e|0](b,c,d);}function Gk(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0);c=N[b+12>>2];d=N[b+4>>2];N[a>>2]=Q(N[b>>2]+N[b+8>>2])*Q(.5);N[a+4>>2]=Q(d+c)*Q(.5);}function Fk(a,b){a=a|0;b=b|0;var c=Q(0),d=Q(0);c=N[b+12>>2];d=N[b+4>>2];N[a>>2]=Q(N[b+8>>2]-N[b>>2])*Q(.5);N[a+4>>2]=Q(c-d)*Q(.5);}function ac(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;var e=0;e=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){e=J[e+J[b>>2]>>2];}Ha[e|0](b,c,d);}function Zc(a){vb(J[a>>2],J[a+20>>2]);vb(J[a>>2],J[a+24>>2]);vb(J[a>>2],J[a+16>>2]);vb(J[a>>2],J[a+12>>2]);vb(J[a>>2],J[a+8>>2]);}function Lk(a){a=a|0;var b=0;b=Ra(16);J[b+4>>2]=0;J[b>>2]=12704;H[b+8|0]=0;J[b+12>>2]=J[a>>2];J[a>>2]=0;J[b>>2]=21156;return b|0}function Ob(a,b,c){a=a|0;b=b|0;c=c|0;var d=0;d=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){d=J[d+J[b>>2]>>2];}return Ha[d|0](b,c)|0}function ef(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if(Za(a,J[b+8>>2],0)){jc(b,c,d);return}a=J[a+8>>2];Ha[J[J[a>>2]+28>>2]](a,b,c,d);}function Yj(a){a=a|0;var b=Q(0),c=Q(0);b=N[a+28>>2];c=Q(b*b);b=N[a+32>>2];return Q(Q(Q(N[a+116>>2]*Q(c+Q(b*b)))+N[a+124>>2]))}function sb(a,b){a=a|0;b=b|0;var c=0;c=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){c=J[c+J[b>>2]>>2];}return Q(Q(Ha[c|0](b)))}function Uh(a,b,c,d,e){a=a|0;b=b|0;c=c|0;e=e|0;var f=0,g=0;b=Ya(e,148);f=yb(b,a,0,c,0),g=12756,J[f>>2]=g;return b|0}function Pg(a,b,c,d,e){a=a|0;b=b|0;c=c|0;e=e|0;var f=0,g=0;b=Ya(e,148);f=yb(b,a,0,c,0),g=13692,J[f>>2]=g;return b|0}function Mg(a,b,c,d,e){a=a|0;b=b|0;c=c|0;e=e|0;var f=0,g=0;b=Ya(e,148);f=yb(b,a,0,c,0),g=13752,J[f>>2]=g;return b|0}function Lh(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var f=0,g=0;e=Ya(e,148);f=yb(e,a,b,c,d),g=12876,J[f>>2]=g;return e|0}function Jh(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;var f=0,g=0;e=Ya(e,148);f=yb(e,a,b,c,d),g=12936,J[f>>2]=g;return e|0}function Ch(a,b,c,d,e){a=a|0;b=b|0;c=c|0;e=e|0;var f=0,g=0;b=Ya(e,148);f=yb(b,a,0,c,0),g=13152,J[f>>2]=g;return b|0}function Ah(a,b,c,d,e){a=a|0;b=b|0;c=c|0;e=e|0;var f=0,g=0;b=Ya(e,148);f=yb(b,a,0,c,0),g=13212,J[f>>2]=g;return b|0}function bc(a,b){a=a|0;b=b|0;var c=0;c=Fa-16|0;Fa=c;J[c+12>>2]=b;a=Ha[a|0](c+12|0)|0;fa(J[c+12>>2]);Fa=c+16|0;return a|0}function tb(a,b,c){a=a|0;b=b|0;c=Q(c);var d=0;d=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){d=J[d+J[b>>2]>>2];}Ha[d|0](b,c);}function ab(a,b,c){a=a|0;b=b|0;c=c|0;var d=0;d=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){d=J[d+J[b>>2]>>2];}Ha[d|0](b,c);}function We(a,b){a=a|0;b=b|0;var c=0;if(K[a+38|0]!=(b|0)){c=J[a+8>>2];J[c+144>>2]=0;I[c+4>>1]=L[c+4>>1]|2;H[a+38|0]=b;}}function kb(a,b){a=a|0;b=b|0;var c=0;c=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){c=J[c+J[b>>2]>>2];}return Ha[c|0](b)|0}function Eh(a,b,c){a=a|0;b=b|0;c=Q(c);var d=Q(0);d=N[b+116>>2];c=Q(N[b+100>>2]*c);N[a+4>>2]=c*N[b+120>>2];N[a>>2]=c*d;}function Wf(a,b,c){a=a|0;b=b|0;c=Q(c);var d=Q(0);d=N[b+104>>2];c=Q(N[b+92>>2]*c);N[a+4>>2]=c*N[b+108>>2];N[a>>2]=c*d;}function Fe(a,b){a=a|0;b=b|0;if(!(J[a+28>>2]>(b|0)&(b|0)>=0)){ya(1632,3777,346,6204);B();}return J[a+24>>2]+P(b,28)|0}function fb(a,b){a=a|0;b=b|0;var c=0;c=J[a>>2];a=Ra(8);b=b+c|0;c=J[b+4>>2];J[a>>2]=J[b>>2];J[a+4>>2]=c;return a|0}function je(a,b,c){a=a|0;b=b|0;c=c|0;a=Ha[a|0](b,c)|0;b=Ra(8);c=J[a+4>>2];J[b>>2]=J[a>>2];J[b+4>>2]=c;return b|0}function Vh(a,b){a=a|0;b=b|0;var c=0;c=L[a+4>>1];if(!(!(c&16)^b)){J[a+72>>2]=0;I[a+4>>1]=c&65519|(b?16:0);Jb(a);}}function ve(a,b){a=a|0;b=b|0;var c=0;a=Ha[a|0](b)|0;b=Ra(8);c=J[a+4>>2];J[b>>2]=J[a>>2];J[b+4>>2]=c;return b|0}function Zk(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=a,e=na((H[b+11|0]<0?J[b>>2]:b)|0,20228,J[c>>2])|0,J[d>>2]=e;}function Vk(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=a,e=na((H[b+11|0]<0?J[b>>2]:b)|0,20456,J[c>>2])|0,J[d>>2]=e;}function Pk(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=a,e=na((H[b+11|0]<0?J[b>>2]:b)|0,20688,J[c>>2])|0,J[d>>2]=e;}function Jk(a,b,c){a=a|0;b=b|0;c=c|0;var d=0,e=0;d=a,e=na((H[b+11|0]<0?J[b>>2]:b)|0,21088,J[c>>2])|0,J[d>>2]=e;}function Lj(a,b){a=a|0;b=b|0;var c=0;c=L[a+4>>1];if(b){I[a+4>>1]=c|4;return}J[a+144>>2]=0;I[a+4>>1]=c&65529|2;}function Bb(a,b,c){if(c){if((c|0)>=641){Wa(b);return}a=(K[c+23620|0]<<2)+a|0;J[b>>2]=J[a+12>>2];J[a+12>>2]=b;}}function qb(a,b){a=a|0;b=b|0;var c=0;c=J[a>>2];a=J[a+4>>2];b=(a>>1)+b|0;if(a&1){c=J[c+J[b>>2]>>2];}Ha[c|0](b);}function fk(a,b){a=a|0;b=Q(b);if(J[a>>2]){if(Q(b*b)>Q(0)){J[a+144>>2]=0;I[a+4>>1]=L[a+4>>1]|2;}N[a+72>>2]=b;}}function ze(){var a=0;a=Ra(20);J[a+4>>2]=0;J[a+8>>2]=0;J[a>>2]=12488;J[a+12>>2]=0;J[a+16>>2]=0;return a|0}function Pi(a,b,c){a=a|0;b=b|0;c=c|0;a=J[a>>2]+(b<<3)|0;b=J[c+4>>2];J[a>>2]=J[c>>2];J[a+4>>2]=b;return 1}function Fj(a){a=a|0;var b=Q(0),c=Q(0);b=N[J[a+48>>2]+20>>2];c=N[J[a+52>>2]+20>>2];N[a+140>>2]=b>c?b:c;}function fl(){var a=0;a=Ra(20);J[a>>2]=0;J[a+4>>2]=0;J[a+16>>2]=0;J[a+8>>2]=0;J[a+12>>2]=0;return a|0}function _k(a){a=a|0;var b=0;b=Ra(12);H[b+4|0]=0;J[b+8>>2]=J[a>>2];J[a>>2]=0;J[b>>2]=20296;return b|0}function Wk(a){a=a|0;var b=0;b=Ra(12);H[b+4|0]=0;J[b+8>>2]=J[a>>2];J[a>>2]=0;J[b>>2]=20524;return b|0}function Bj(){var a=0;a=Ra(20);J[a>>2]=0;J[a+4>>2]=0;H[a+16|0]=0;J[a+8>>2]=0;J[a+12>>2]=0;return a|0}function Me(a,b){a=a|0;b=b|0;var c=0;c=J[a+12>>2];return Ha[J[J[c>>2]+16>>2]](c,J[a+8>>2]+12|0,b)|0}function Rf(a,b,c){a=a|0;b=b|0;c=Q(c);var d=Q(0);d=N[b+108>>2];N[a>>2]=N[b+104>>2]*c;N[a+4>>2]=d*c;}function Ek(a){a=a|0;var b=Q(0);b=Q(Q(N[a+8>>2]-N[a>>2])+Q(N[a+12>>2]-N[a+4>>2]));return Q(Q(b+b))}function Tg(a,b,c){a=a|0;b=b|0;c=Q(c);var d=Q(0);d=N[b+100>>2];N[a>>2]=N[b+96>>2]*c;N[a+4>>2]=d*c;}function jh(a,b,c){a=a|0;b=b|0;c=Q(c);var d=Q(0);d=N[b+84>>2];N[a>>2]=N[b+80>>2]*c;N[a+4>>2]=d*c;}function id(a,b,c){a=a|0;b=b|0;c=Q(c);var d=Q(0);d=N[b+88>>2];N[a>>2]=N[b+84>>2]*c;N[a+4>>2]=d*c;}function of(){var a=0;a=J[6124];if(a){while(1){Ha[J[a>>2]]();a=J[a+4>>2];if(a){continue}break}}}function Xe(a,b,c,d,e,f){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;f=f|0;if(Za(a,J[b+8>>2],f)){ic(b,c,d,e);}}function zh(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;rd(b,J[J[a+48>>2]+12>>2],c,J[J[a+52>>2]+12>>2],d);}function qe(a,b,c,d,e,f,g){a=a|0;b=b|0;c=Q(c);d=Q(d);e=Q(e);f=Q(f);g=Q(g);Ha[a|0](b,c,d,e,f,g);}function Bh(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;sd(b,J[J[a+48>>2]+12>>2],c,J[J[a+52>>2]+12>>2],d);}function lh(a,b){a=a|0;b=b|0;var c=0;b=J[b+48>>2];c=J[b+16>>2];J[a>>2]=J[b+12>>2];J[a+4>>2]=c;}function kh(a,b){a=a|0;b=b|0;var c=0;b=J[b+52>>2];c=J[b+16>>2];J[a>>2]=J[b+12>>2];J[a+4>>2]=c;}function gb(a,b,c){a=a|0;b=b|0;c=c|0;a=J[a>>2]+b|0;b=J[c+4>>2];J[a>>2]=J[c>>2];J[a+4>>2]=b;}function Rg(a,b){a=a|0;b=b|0;N[a+76>>2]=N[a+76>>2]-N[b>>2];N[a+80>>2]=N[a+80>>2]-N[b+4>>2];}function sk(a,b){a=a|0;b=b|0;var c=0;c=J[b+4>>2];J[a+102964>>2]=J[b>>2];J[a+102968>>2]=c;}function rk(a,b){a=a|0;b=b|0;var c=0;c=J[b+102968>>2];J[a>>2]=J[b+102964>>2];J[a+4>>2]=c;}function jg(a){a=a|0;return Q(Q(Q(N[J[a+52>>2]+56>>2]-N[J[a+48>>2]+56>>2])-N[a+116>>2]))}function ce(){var a=0;a=Ra(16);J[a>>2]=0;J[a+4>>2]=0;J[a+8>>2]=0;J[a+12>>2]=0;return a|0}function sj(a,b){a=a|0;b=b|0;var c=0;c=J[b+48>>2];N[a+4>>2]=M[b+52>>2];N[a>>2]=c>>>0;}function Vg(a,b){a=a|0;b=b|0;var c=0;c=J[b+80>>2];J[a>>2]=J[b+76>>2];J[a+4>>2]=c;}function ek(a){a=a|0;N[a+136>>2]=Y(Q(N[J[a+48>>2]+16>>2]*N[J[a+52>>2]+16>>2]));}function cl(){var a=0;a=Ra(6);I[a+4>>1]=0;I[a>>1]=1;I[a+2>>1]=65535;return a|0}function Ve(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=d|0;e=e|0;return Ha[a|0](b,c,d,e)|0}function ff(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;if(Za(a,J[b+8>>2],0)){jc(b,c,d);}}function ll(a){var b=0;b=a&31;a=0-a&31;return (-1>>>b&-2)<<b|(-1<<a&-2)>>>a}
				function el(){var a=0;a=Ra(12);J[a>>2]=0;J[a+4>>2]=0;J[a+8>>2]=0;return a|0}function ec(){var a=0;a=Ra(12);J[a+8>>2]=0;J[a>>2]=0;J[a+4>>2]=0;return a|0}function dc(a){a=a|0;var b=0;if(a){b=J[a>>2];if(b){J[a+4>>2]=b;Wa(b);}Wa(a);}}function Vi(a,b,c){a=a|0;b=b|0;c=c|0;J[J[a>>2]+(b<<2)>>2]=J[c>>2];return 1}function ig(a){a=a|0;return Q(Q(N[J[a+52>>2]+72>>2]-N[J[a+48>>2]+72>>2]))}function Ae(a,b,c){a=a|0;b=b|0;c=c|0;return Ha[J[J[a>>2]+16>>2]](a,b,c)|0}function Oi(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return Ha[J[a>>2]](b,c,d)|0}function uh(a,b){a=a|0;b=Q(b);return Q(Q(Q(N[a+156>>2]*N[a+256>>2])*b))}function he(a,b,c,d,e){a=a|0;b=b|0;c=c|0;d=Q(d);e=Q(e);Ha[a|0](b,c,d,e);}function Be(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;return Ha[a|0](b,c,d)|0}function cd(a){vb(J[a+32>>2],J[a+40>>2]);vb(J[a+32>>2],J[a+36>>2]);}function zb(a,b){a=a|0;b=b|0;Ha[J[J[a>>2]+4>>2]](a)|0;Bb(b,a,148);}function ke(a,b){a=a|0;b=b|0;return Q(N[(J[a>>2]+(b<<3)|0)+4>>2])}function we(a,b,c){a=a|0;b=Q(b);c=Q(c);N[a+12>>2]=b;N[a+16>>2]=c;}function Bk(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;Ha[J[a>>2]](b,c,d);}function oi(a,b,c){a=a|0;b=b|0;c=c|0;return Q(Q(Ha[a|0](b,c)))}function Nj(a,b){a=a|0;b=b|0;I[a+4>>1]=L[a+4>>1]&65527|(b?8:0);}function ye(a,b,c,d){a=a|0;b=b|0;c=Q(c);d=Q(d);Ha[a|0](b,c,d);}function tg(a,b){a=a|0;b=b|0;return Q(N[((b<<2)+a|0)+24>>2])}function Eg(a,b){a=a|0;b=b|0;return Q(N[((b<<3)+a|0)+12>>2])}function xe(a,b){a=a|0;b=b|0;J[a+4>>2]=J[a+4>>2]&-5|(b?4:0);}function te(a,b,c,d){a=a|0;b=b|0;c=c|0;d=d|0;Ha[a|0](b,c,d);}function le(a,b){a=a|0;b=b|0;return Q(N[J[a>>2]+(b<<3)>>2])}function _f(a,b){a=a|0;b=b|0;return Q(N[((b<<2)+a|0)+8>>2])}function Yb(a,b){if(b){Yb(a,J[b>>2]);Yb(a,J[b+4>>2]);Wa(b);}}function Og(a,b){a=a|0;b=b|0;return Q(N[((b<<3)+a|0)+8>>2])}function wc(a,b,c){a=a|0;b=b|0;c=c|0;return Ha[a|0](b,c)|0}function kf(a,b,c,d){Ga=0;return 0}function vg(a,b){a=a|0;b=Q(b);return Q(Q(N[a+116>>2]*b))}function nc(a,b){a=a|0;b=Q(b);return Q(Q(N[a+112>>2]*b))}function Gg(a,b){a=a|0;b=Q(b);return Q(Q(N[a+108>>2]*b))}function ih(a,b){a=a|0;b=Q(b);return Q(Q(N[a+88>>2]*b))}function ib(a,b,c){a=a|0;b=b|0;c=Q(c);N[J[a>>2]+b>>2]=c;}function hd(a,b){a=a|0;b=Q(b);return Q(Q(N[a+92>>2]*b))}function fg(a,b){a=a|0;b=Q(b);return Q(Q(N[a+96>>2]*b))}function _h(a,b){a=a|0;b=b|0;J[a+4>>2]=J[a+4>>2]&(b^-1);}function Lc(a,b,c){a=a|0;b=b|0;return Za(a,b,0)|0}function hb(a,b){a=a|0;b=b|0;return Q(N[J[a>>2]+b>>2])}function be(a,b,c){a=a|0;b=b|0;c=c|0;I[J[a>>2]+b>>1]=c;}function _d(a,b,c){a=a|0;b=b|0;c=c|0;J[J[a>>2]+b>>2]=c;}function gd(a,b){a=a|0;b=b|0;return Q(N[(b<<2)+a>>2])}function Nb(a,b,c){a=a|0;b=b|0;c=c|0;H[J[a>>2]+b|0]=c;}function Ec(a,b,c){a=a|0;b=b|0;c=c|0;Ha[J[a>>2]](b,c);}function Ce(a){a=a|0;return Ha[J[J[a>>2]+12>>2]](a)|0}function ie(a,b){a=a|0;b=b|0;return J[a>>2]+(b<<3)|0}function Tk(a,b){a=a|0;b=b|0;return Q(Q(Ha[a|0](b)))}function Td(a,b){a=a|0;b=b|0;return Ha[J[a>>2]](b)|0}function il(a){if(a){return 31-S(a-1^a)|0}return 32}function hj(a){a=a|0;if(a){Ha[J[J[a>>2]+28>>2]](a);}}function bl(a,b){a=a|0;b=b|0;return L[J[a>>2]+b>>1]}function al(a,b){a=a|0;b=b|0;return I[J[a>>2]+b>>1]}function Zd(a,b){a=a|0;b=b|0;return J[J[a>>2]+b>>2]}function rb(a){a=a|0;if(a){Ha[J[J[a>>2]+4>>2]](a);}}function dl(a,b,c){a=a|0;b=b|0;c=Q(c);Ha[a|0](b,c);}function Mb(a,b){a=a|0;b=b|0;return K[J[a>>2]+b|0]}function $h(a,b){a=a|0;b=b|0;J[a+4>>2]=J[a+4>>2]|b;}function Sg(a,b){b=Q(b);return Q(Q(b*Q(0)))}function Pb(a,b,c){a=a|0;b=b|0;c=c|0;Ha[a|0](b,c);}function fc(a,b){a=a|0;b=b|0;return Ha[a|0](b)|0}function Xi(a){a=a|0;return J[a+4>>2]-J[a>>2]>>2}function Hc(a){a=a|0;return J[a+4>>2]-J[a>>2]>>3}function hi(a,b){a=a|0;b=b|0;return P(b,20)+a|0}function Ic(a,b,c,d){za(a|0,b|0,8,0,c|0,-1,d|0);}function af(a){a=a|0;return J[J[a+12>>2]+4>>2]}function Ij(a){a=a|0;return (K[a+4|0]&32)>>>5|0}function Hj(a){a=a|0;return (K[a+4|0]&16)>>>4|0}function fe(a){a=a|0;return (K[a+4|0]&2)>>>1|0}function Qh(a,b,c){return 0}function Mj(a){a=a|0;return (K[a+4|0]&8)>>>3|0}function Kj(a){a=a|0;return (K[a+4|0]&4)>>>2|0}function Cf(a,b){a=a|0;b=b|0;J[a+102940>>2]=b;}function Bf(a,b){a=a|0;b=b|0;J[a+102980>>2]=b;}function rc(a,b){return Q(Q(0))}function eh(a,b){a=a|0;b=Q(b);N[a+100>>2]=b;}function db(a){a=a|0;return J[J[a>>2]-4>>2]}function cc(a,b){a=a|0;b=b|0;Ha[J[a>>2]](b);}function ae(a,b){a=a|0;b=Q(b);N[a+136>>2]=b;}function Xd(a,b){a=a|0;b=Q(b);N[a+140>>2]=b;}function Pd(a,b){a=a|0;b=Q(b);N[a+104>>2]=b;}function Oj(a,b){a=a|0;b=Q(b);N[a+132>>2]=b;}function $k(a,b){a=a|0;b=Q(b);N[a+144>>2]=b;}function zc(a){a=a|0;return Q(N[a+124>>2])}function yc(a){a=a|0;return Q(N[a+120>>2])}function nj(a){a=a|0;return Q(N[a+128>>2])}function kj(a){a=a|0;return Q(N[a+108>>2])}function hh(a,b){a=a|0;b=Q(b);N[a+92>>2]=b;}function fh(a,b){a=a|0;b=Q(b);N[a+96>>2]=b;}function _g(a,b){a=a|0;b=Q(b);N[a+88>>2]=b;}function Yd(a){a=a|0;return Q(N[a+116>>2])}function Wd(a){a=a|0;return Q(N[a+132>>2])}function Ud(a){a=a|0;return Q(N[a+140>>2])}function Nd(a){a=a|0;return Q(N[a+100>>2])}function Kk(a){a=a|0;return Q(N[a+144>>2])}function Kd(a,b){a=a|0;b=Q(b);N[a+84>>2]=b;}function Je(a,b){a=a|0;b=Q(b);N[a+16>>2]=b;}function He(a,b){a=a|0;b=Q(b);N[a+20>>2]=b;}function Dc(a){a=a|0;return Q(N[a+104>>2])}function Cc(a,b){a=a|0;b=Q(b);N[a+68>>2]=b;}function Ac(a,b){a=a|0;b=Q(b);N[a+72>>2]=b;}function $d(a){a=a|0;return Q(N[a+136>>2])}function zj(a,b){a=a|0;b=b|0;J[a+12>>2]=b;}function tk(a){a=a|0;return K[a+102972|0]}function qk(a){a=a|0;return K[a+102989|0]}function qh(a){a=a|0;return Q(N[a+12>>2])}function jk(a){a=a|0;return Q(N[a+56>>2])}function gh(a){a=a|0;return Q(N[a+92>>2])}function fd(a){a=a|0;return Q(N[a+84>>2])}function bh(a){a=a|0;return Q(N[a+76>>2])}function ai(a){a=a|0;return Q(N[a+48>>2])}function _b(a){a=a|0;return Q(N[a+72>>2])}function Zh(a){a=a|0;return Q(N[a+52>>2])}function Yg(a){a=a|0;return Q(N[a+88>>2])}function Th(a){a=a|0;return Q(N[a+40>>2])}function Ne(a,b){a=a|0;b=b|0;J[a+40>>2]=b;}function Mh(a){a=a|0;return Q(N[a+44>>2])}function Ke(a){a=a|0;return Q(N[a+16>>2])}function Jd(a){a=a|0;return Q(N[a+96>>2])}function Ie(a){a=a|0;return Q(N[a+20>>2])}function De(a,b){a=a|0;b=Q(b);N[a+8>>2]=b;}function Bc(a){a=a|0;return Q(N[a+68>>2])}function od(a){a=a|0;return Q(N[a+4>>2])}function jd(a){a=a|0;return Q(N[a+8>>2])}function bi(a,b){a=a|0;b=b|0;J[a+4>>2]=b;}function Aj(a,b){a=a|0;b=b|0;J[a+8>>2]=b;}function nk(a,b){a=a|0;b=b|0;Ha[a|0](b);}function jb(a){a=a|0;return Ha[a|0]()|0}function Gj(a){a=a|0;return J[a+100>>2]}function Ej(a){a=a|0;return J[a+108>>2]}function zg(a){a=a|0;return K[a+137|0]}function yi(a){a=a|0;return J[a+60>>2]}function sc(a){a=a|0;return Q(N[a>>2])}function kc(a){a=a|0;return J[a+12>>2]}function hg(a){a=a|0;return K[a+100|0]}function gl(a){a=a|0;J[a+4>>2]=J[a>>2];}function cg(a){a=a|0;return K[a+112|0]}function Qf(a){a=a|0;return J[a+16>>2]}function Oe(a){a=a|0;return J[a+40>>2]}function Ld(a){a=a|0;return J[a+48>>2]}function Id(a){a=a|0;return J[a+52>>2]}function Hi(a){a=a|0;return J[a+56>>2]}function Hf(a){a=a|0;return K[a+128|0]}function Dj(a){a=a|0;return J[a+88>>2]}function Cg(a){a=a|0;return K[a+136|0]}function yj(a){a=a|0;return K[a+61|0]}function oc(a,b){return 1}function lc(a){a=a|0;return J[a+8>>2]}function Ue(a){a=a|0;return K[a+38|0]}function Gb(a){a=a|0;return J[a+4>>2]}function Fd(a){a=a|0;return a- -64|0}function Ee(a,b){a=a|0;b=b|0;md(a,b);}function Ed(a,b,c){}function mc(a){a=a|0;return J[a>>2]}function ik(a){a=a|0;return a+44|0}function hk(a){a=a|0;return a+28|0}function gc(a){a=a|0;return a+12|0}function cj(a){a=a|0;return a+92|0}function Zb(a){a=a|0;return a+68|0}function Rd(a){a=a|0;return a+80|0}function Qe(a){a=a|0;return a+32|0}function Qd(a){a=a|0;return a+88|0}function Od(a){a=a|0;return a+84|0}function Kb(a){a=a|0;return a+76|0}function xk(a){return 21472}function xj(a){return 22212}function uj(a){return 22328}function rj(a){return 22456}function pk(a){return 21588}function pj(a){return 22584}function mk(a){return 21564}function mj(a){return 22752}function jj(a){return 22928}function gj(a){return 23068}function ej(a){return 23184}function bj(a){return 19844}function Ti(a){return 19968}function Ik(a){return 19648}function Cj(a){return 21660}function $a(a){a=a|0;if(a){Wa(a);}}function oh(a){Sa(10085,0);}function Qg(a){Sa(10132,0);}function Va(a){a=a|0;return a|0}function Mk(a){a=a|0;H[a+8|0]=1;}function Gc(a){a=a|0;H[a+4|0]=1;}function vc(a){return 1}function mf(a){return 0}function de(){return Ra(16)|0}function xc(a,b){}function ee(){return Ra(8)|0}function Xa(a){a=a|0;Wa(a);}function Eb(a){B();}function Fb(){ma();B();}function mb(a){}
				// EMSCRIPTEN_END_FUNCS
				e=K;p();var Ha=c([null,oi,gd,xf,Re,Pb,xe,fc,fe,dl,$k,Tk,Kk,ae,$d,nk,ek,Xd,Ud,Fj,wj,sj,fc,Ld,Id,Pb,Zi,Fd,Hi,yi,wc,hi,ai,Zh,Th,Mh,sc,od,jd,qh,jb,Zg,Og,Eg,tg,sc,od,$a,gd,_f,fc,Qf,mc,Gb,lc,kc,lf,$a,jf,gf,af,kc,We,Ue,Pb,Te,Se,Qe,Pe,lc,Gb,Oe,Ne,wc,Me,Le,sc,Ke,Je,Ie,He,Ge,Fe,Pb,Ee,jd,De,Gb,Ce,Be,Ae,ze,rb,ye,we,ve,gc,ue,rb,te,se,re,qe,pe,wc,oe,ne,ec,dc,me,Hc,le,ke,je,ie,mc,he,hl,gl,$a,ee,ib,hb,$a,ee,ib,hb,$a,de,gb,fb,gb,fb,$a,de,ib,hb,$a,fl,gb,fb,ib,hb,$a,el,gb,fb,ib,hb,$a,ce,ib,hb,gb,fb,$a,cl,be,bl,be,al,rb,db,Ob,rb,Va,Va,db,Gc,cc,_k,bc,Zk,Yk,rb,db,Xk,rb,Va,Va,db,Gc,cc,Wk,bc,Vk,rb,db,ab,ac,Uk,ab,Sk,Rk,Ob,rb,Va,Va,db,Gc,cc,Qk,bc,Pk,rb,db,bi,ab,Gb,kb,$h,_h,$b,Ok,Nk,$b,ab,rb,Va,Va,db,Mk,cc,Lk,bc,Jk,$a,Ik,ce,jb,Hk,kb,Gk,Fc,Fk,Ek,sb,Dk,Ec,Ck,Bk,Ak,Ob,ii,zk,gb,fb,yk,xk,wk,vk,Cf,ab,Bf,ab,sf,qb,Af,Ob,zf,ab,yf,Ob,Wc,ab,vf,uk,uf,ac,tf,$b,wf,ab,tk,kb,sk,ab,rk,Fc,qk,rf,mb,pk,ok,jb,_d,Zd,gb,fb,ib,hb,Nb,Mb,mb,mk,Xh,lk,gc,kk,gc,ub,jk,sb,ik,hk,gk,ab,Fd,fk,tb,_b,dk,$b,ck,ac,bk,ak,$j,_j,Zj,Yd,Yj,Xj,ab,Wj,Ec,Jb,qb,Vj,Uj,Tj,Sj,Rj,Qj,Pj,Wd,Oj,$d,ae,Ud,Xd,Yh,ab,mc,kb,Nj,ab,Mj,kb,Lj,Kj,Jj,fe,Wh,Ij,Vh,Hj,Gj,Td,Ej,Dj,kb,ud,$a,Cj,Bj,jb,_d,Zd,Aj,Ec,lc,Td,zj,kc,Nb,Mb,mb,db,Gb,kb,Ld,kb,Id,Fc,Sd,Lb,yj,kb,qb,$a,Va,Va,xj,vj,jb,gb,fb,ib,hb,mb,Va,Va,db,Rd,ub,Qd,Pd,tb,Dc,sb,Cc,Bc,Ac,_b,qb,$a,Va,Va,uj,tj,jb,gb,fb,ib,hb,mb,Va,Va,db,dh,ab,Zb,ub,ch,tb,bh,sb,hh,gh,fh,Jd,eh,Nd,qb,$a,Va,Va,rj,qj,jb,gb,fb,ib,hb,mb,Va,Va,db,$g,ab,Kb,ub,Pd,tb,Dc,sb,Kd,fd,_g,Yg,qb,$a,Va,Va,pj,oj,jb,gb,fb,ib,hb,Nb,Mb,mb,Va,Va,db,Zb,ub,Kb,Od,Nd,sb,Fg,Dg,Cg,kb,Bg,ab,yc,zc,Ag,Md,zg,yg,xg,tb,Wd,wg,nj,vg,Lb,qb,$a,Va,Va,mj,lj,jb,gb,fb,ib,hb,Nb,Mb,mb,Va,Va,db,Zb,ub,Kb,Yd,sb,jg,ig,cg,kb,bg,ab,yc,zc,ag,Md,hg,gg,eg,tb,kj,dg,Dc,fg,Lb,qb,$a,Va,Va,jj,ij,jb,gb,fb,ib,hb,hj,Va,Va,db,Zb,ub,Kb,Sd,Lb,Kd,tb,fd,sb,qb,$a,Va,Va,gj,fj,jb,gb,fb,ib,hb,mb,Va,Va,db,Rd,ub,Qd,Jd,sb,Cc,tb,Bc,Ac,_b,qb,$a,Va,Va,ej,dj,jb,gb,fb,Nb,Mb,ib,hb,mb,Va,Va,db,Kb,ub,Od,cj,If,sb,Hf,kb,Gf,ab,Ff,tb,zc,Ef,yc,nc,Lb,Cc,Bc,Ac,_b,qb,dc,bj,ec,jb,aj,$i,_i,Yi,Xi,kb,Wi,Gd,Vi,Ui,dc,Ti,ec,jb,Si,ab,Ri,ac,Hc,kb,Qi,Gd,Pi,Oi,Vd,Ni,Mi,Li,Eb,Fb,Ki,Ji,Ii,Eb,Gi,Fi,Ei,Di,Ci,Bi,Ai,zi,xc,xc,Ed,Ed,xi,wi,vi,ui,ti,si,ri,qi,pi,Eb,Va,Xa,ni,vc,mi,li,ki,ji,Xa,gi,vc,fi,ei,di,ci,Va,Eb,Sh,Va,Xa,Xa,Rh,vc,Qh,Ph,Oh,Nh,Kh,Xa,Ih,Xa,Pg,zb,Uh,Ch,Lh,zb,zb,Mg,zb,zb,zb,Ah,zb,Jh,Eb,Va,Xa,qd,pd,Eh,rc,Dh,xc,Va,Xa,Hh,Gh,Fh,Bh,Xa,zh,Xa,ld,kd,vh,uh,th,Xa,yh,xh,wh,Tb,Sb,id,hd,ph,Xa,sh,rh,oc,oh,Eb,lh,kh,jh,ih,ah,Xa,nh,mh,oc,Vg,Ug,Tg,Sg,Qg,Rg,Xa,Xg,Wg,oc,Ng,Xa,Lg,Xa,Tb,Sb,Hg,Gg,ug,Xa,Kg,Jg,Ig,ld,kd,pg,rc,og,ng,Xa,sg,rg,qg,Tb,Sb,id,hd,$f,Xa,mg,lg,kg,Tb,Sb,Wf,rc,Vf,Xa,Zf,Yf,Xf,qd,pd,Rf,nc,Pf,Xa,Uf,Tf,Sf,Lf,Kf,Jf,nc,Df,Xa,Of,Nf,Mf,Va,Xa,qf,Vc,mf,nf,kf,Oc,Nc,Va,Xa,mb,mb,Lc,Xa,Lc,Xa,hf,Xe,_e,ff,Xa,Ye,$e,ef,Xa,Ze,bf,df,Xa,cf]);function Ia(){return G.byteLength/65536|0}function Na(Oa){Oa=Oa|0;var Ja=Ia()|0;var Ka=Ja+Oa|0;if(Ja<Ka&&Ka<65536){var La=new ArrayBuffer(P(Ka,65536));var Ma=new Int8Array(La);Ma.set(H);H=new Int8Array(La);I=new Int16Array(La);J=new Int32Array(La);K=new Uint8Array(La);L=new Uint16Array(La);M=new Uint32Array(La);N=new Float32Array(La);O=new Float64Array(La);G=La;F.buffer=G;e=K;}return Ja}return {"J":ge,"K":Ha,"L":_a,"M":Wa,"N":pf,"O":of,"P":Ve}}return Pa(Qa)}
				// EMSCRIPTEN_END_ASM


				)(info);},instantiate:function(binary,info){return {then:function(ok){var module=new WebAssembly.Module(binary);ok({"instance":new WebAssembly.Instance(module,info)});}}},RuntimeError:Error};wasmBinary=[];if(typeof WebAssembly!="object"){abort("no native wasm support detected");}var wasmMemory;var ABORT=false;function assert(condition,text){if(!condition){abort(text);}}var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateMemoryViews(){var b=wasmMemory.buffer;Module["HEAP8"]=HEAP8=new Int8Array(b);Module["HEAP16"]=HEAP16=new Int16Array(b);Module["HEAP32"]=HEAP32=new Int32Array(b);Module["HEAPU8"]=HEAPU8=new Uint8Array(b);Module["HEAPU16"]=HEAPU16=new Uint16Array(b);Module["HEAPU32"]=HEAPU32=new Uint32Array(b);Module["HEAPF32"]=HEAPF32=new Float32Array(b);Module["HEAPF64"]=HEAPF64=new Float64Array(b);}var INITIAL_MEMORY=Module["INITIAL_MEMORY"]||16777216;assert(INITIAL_MEMORY>=65536,"INITIAL_MEMORY should be larger than STACK_SIZE, was "+INITIAL_MEMORY+"! (STACK_SIZE="+65536+")");if(Module["wasmMemory"]){wasmMemory=Module["wasmMemory"];}else {wasmMemory=new WebAssembly.Memory({"initial":INITIAL_MEMORY/65536,"maximum":2147483648/65536});}updateMemoryViews();INITIAL_MEMORY=wasmMemory.buffer.byteLength;var wasmTable;var __ATPRERUN__=[];var __ATINIT__=[];var __ATPOSTRUN__=[];function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift());}}callRuntimeCallbacks(__ATPRERUN__);}function initRuntime(){callRuntimeCallbacks(__ATINIT__);}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift());}}callRuntimeCallbacks(__ATPOSTRUN__);}function addOnPreRun(cb){__ATPRERUN__.unshift(cb);}function addOnInit(cb){__ATINIT__.unshift(cb);}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb);}var runDependencies=0;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies);}}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"]){Module["monitorRunDependencies"](runDependencies);}if(runDependencies==0){if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback();}}}function abort(what){if(Module["onAbort"]){Module["onAbort"](what);}what="Aborted("+what+")";err(what);ABORT=true;what+=". Build with -sASSERTIONS for more info.";var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}var dataURIPrefix="data:application/octet-stream;base64,";function isDataURI(filename){return filename.startsWith(dataURIPrefix)}var wasmBinaryFile;wasmBinaryFile="<<< WASM_BINARY_FILE >>>";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile);}function getBinary(file){try{if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}var binary=tryParseAsDataURI(file);if(binary){return binary}if(readBinary);throw "both async and sync fetching of the wasm failed"}catch(err){abort(err);}}function getBinaryPromise(binaryFile){if(!wasmBinary&&(ENVIRONMENT_IS_WEB)){if(typeof fetch=="function"){return fetch(binaryFile,{credentials:"same-origin"}).then(response=>{if(!response["ok"]){throw "failed to load wasm binary file at '"+binaryFile+"'"}return response["arrayBuffer"]()}).catch(()=>getBinary(binaryFile))}}return Promise.resolve().then(()=>getBinary(binaryFile))}function instantiateArrayBuffer(binaryFile,imports,receiver){return getBinaryPromise(binaryFile).then(binary=>{return WebAssembly.instantiate(binary,imports)}).then(instance=>{return instance}).then(receiver,reason=>{err("failed to asynchronously prepare wasm: "+reason);abort(reason);})}function instantiateAsync(binary,binaryFile,imports,callback){if(!binary&&typeof WebAssembly.instantiateStreaming=="function"&&!isDataURI(binaryFile)&&typeof fetch=="function"){return fetch(binaryFile,{credentials:"same-origin"}).then(response=>{var result=WebAssembly.instantiateStreaming(response,imports);return result.then(callback,function(reason){err("wasm streaming compile failed: "+reason);err("falling back to ArrayBuffer instantiation");return instantiateArrayBuffer(binaryFile,imports,callback)})})}else {return instantiateArrayBuffer(binaryFile,imports,callback)}}function createWasm(){var info={"a":wasmImports};function receiveInstance(instance,module){var exports=instance.exports;Module["asm"]=exports;wasmTable=Module["asm"]["K"];addOnInit(Module["asm"]["J"]);removeRunDependency();return exports}addRunDependency();function receiveInstantiationResult(result){receiveInstance(result["instance"]);}if(Module["instantiateWasm"]){try{return Module["instantiateWasm"](info,receiveInstance)}catch(e){err("Module.instantiateWasm callback failed with error: "+e);readyPromiseReject(e);}}instantiateAsync(wasmBinary,wasmBinaryFile,info,receiveInstantiationResult).catch(readyPromiseReject);return {}}function callRuntimeCallbacks(callbacks){while(callbacks.length>0){callbacks.shift()(Module);}}var UTF8Decoder=typeof TextDecoder!="undefined"?new TextDecoder("utf8"):undefined;function UTF8ArrayToString(heapOrArray,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heapOrArray[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder){return UTF8Decoder.decode(heapOrArray.subarray(idx,endPtr))}var str="";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2;}else {u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63;}if(u0<65536){str+=String.fromCharCode(u0);}else {var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023);}}return str}function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):""}function ___assert_fail(condition,filename,line,func){abort(`Assertion failed: ${UTF8ToString(condition)}, at: `+[filename?UTF8ToString(filename):"unknown filename",line,func?UTF8ToString(func):"unknown function"]);}var char_0=48;var char_9=57;function makeLegalFunctionName(name){if(undefined===name){return "_unknown"}name=name.replace(/[^a-zA-Z0-9_]/g,"$");var f=name.charCodeAt(0);if(f>=char_0&&f<=char_9){return `_${name}`}return name}function createNamedFunction(name,body){name=makeLegalFunctionName(name);return {[name]:function(){return body.apply(this,arguments)}}[name]}function HandleAllocator(){this.allocated=[undefined];this.freelist=[];this.get=function(id){return this.allocated[id]};this.has=function(id){return this.allocated[id]!==undefined};this.allocate=function(handle){var id=this.freelist.pop()||this.allocated.length;this.allocated[id]=handle;return id};this.free=function(id){this.allocated[id]=undefined;this.freelist.push(id);};}var emval_handles=new HandleAllocator;function extendError(baseErrorType,errorName){var errorClass=createNamedFunction(errorName,function(message){this.name=errorName;this.message=message;var stack=new Error(message).stack;if(stack!==undefined){this.stack=this.toString()+"\n"+stack.replace(/^Error(:[^\n]*)?\n/,"");}});errorClass.prototype=Object.create(baseErrorType.prototype);errorClass.prototype.constructor=errorClass;errorClass.prototype.toString=function(){if(this.message===undefined){return this.name}else {return `${this.name}: ${this.message}`}};return errorClass}var BindingError=undefined;function throwBindingError(message){throw new BindingError(message)}function count_emval_handles(){var count=0;for(var i=emval_handles.reserved;i<emval_handles.allocated.length;++i){if(emval_handles.allocated[i]!==undefined){++count;}}return count}function init_emval(){emval_handles.allocated.push({value:undefined},{value:null},{value:true},{value:false});emval_handles.reserved=emval_handles.allocated.length;Module["count_emval_handles"]=count_emval_handles;}var Emval={toValue:handle=>{if(!handle){throwBindingError("Cannot use deleted val. handle = "+handle);}return emval_handles.get(handle).value},toHandle:value=>{switch(value){case undefined:return 1;case null:return 2;case true:return 3;case false:return 4;default:{return emval_handles.allocate({refcount:1,value:value})}}}};var PureVirtualError=undefined;function embind_init_charCodes(){var codes=new Array(256);for(var i=0;i<256;++i){codes[i]=String.fromCharCode(i);}embind_charCodes=codes;}var embind_charCodes=undefined;function readLatin1String(ptr){var ret="";var c=ptr;while(HEAPU8[c]){ret+=embind_charCodes[HEAPU8[c++]];}return ret}function getInheritedInstanceCount(){return Object.keys(registeredInstances).length}function getLiveInheritedInstances(){var rv=[];for(var k in registeredInstances){if(registeredInstances.hasOwnProperty(k)){rv.push(registeredInstances[k]);}}return rv}var deletionQueue=[];function flushPendingDeletes(){while(deletionQueue.length){var obj=deletionQueue.pop();obj.$$.deleteScheduled=false;obj["delete"]();}}var delayFunction=undefined;function setDelayFunction(fn){delayFunction=fn;if(deletionQueue.length&&delayFunction){delayFunction(flushPendingDeletes);}}function init_embind(){Module["getInheritedInstanceCount"]=getInheritedInstanceCount;Module["getLiveInheritedInstances"]=getLiveInheritedInstances;Module["flushPendingDeletes"]=flushPendingDeletes;Module["setDelayFunction"]=setDelayFunction;}var registeredInstances={};function getBasestPointer(class_,ptr){if(ptr===undefined){throwBindingError("ptr should not be undefined");}while(class_.baseClass){ptr=class_.upcast(ptr);class_=class_.baseClass;}return ptr}function registerInheritedInstance(class_,ptr,instance){ptr=getBasestPointer(class_,ptr);if(registeredInstances.hasOwnProperty(ptr)){throwBindingError(`Tried to register registered instance: ${ptr}`);}else {registeredInstances[ptr]=instance;}}var registeredTypes={};function getTypeName(type){var ptr=___getTypeName(type);var rv=readLatin1String(ptr);_free(ptr);return rv}function requireRegisteredType(rawType,humanName){var impl=registeredTypes[rawType];if(undefined===impl){throwBindingError(humanName+" has unknown type "+getTypeName(rawType));}return impl}function unregisterInheritedInstance(class_,ptr){ptr=getBasestPointer(class_,ptr);if(registeredInstances.hasOwnProperty(ptr)){delete registeredInstances[ptr];}else {throwBindingError(`Tried to unregister unregistered instance: ${ptr}`);}}function detachFinalizer(handle){}var finalizationRegistry=false;function runDestructor($$){if($$.smartPtr){$$.smartPtrType.rawDestructor($$.smartPtr);}else {$$.ptrType.registeredClass.rawDestructor($$.ptr);}}function releaseClassHandle($$){$$.count.value-=1;var toDelete=0===$$.count.value;if(toDelete){runDestructor($$);}}function downcastPointer(ptr,ptrClass,desiredClass){if(ptrClass===desiredClass){return ptr}if(undefined===desiredClass.baseClass){return null}var rv=downcastPointer(ptr,ptrClass,desiredClass.baseClass);if(rv===null){return null}return desiredClass.downcast(rv)}var registeredPointers={};function getInheritedInstance(class_,ptr){ptr=getBasestPointer(class_,ptr);return registeredInstances[ptr]}var InternalError=undefined;function throwInternalError(message){throw new InternalError(message)}function makeClassHandle(prototype,record){if(!record.ptrType||!record.ptr){throwInternalError("makeClassHandle requires ptr and ptrType");}var hasSmartPtrType=!!record.smartPtrType;var hasSmartPtr=!!record.smartPtr;if(hasSmartPtrType!==hasSmartPtr){throwInternalError("Both smartPtrType and smartPtr must be specified");}record.count={value:1};return attachFinalizer(Object.create(prototype,{$$:{value:record}}))}function RegisteredPointer_fromWireType(ptr){var rawPointer=this.getPointee(ptr);if(!rawPointer){this.destructor(ptr);return null}var registeredInstance=getInheritedInstance(this.registeredClass,rawPointer);if(undefined!==registeredInstance){if(0===registeredInstance.$$.count.value){registeredInstance.$$.ptr=rawPointer;registeredInstance.$$.smartPtr=ptr;return registeredInstance["clone"]()}else {var rv=registeredInstance["clone"]();this.destructor(ptr);return rv}}function makeDefaultHandle(){if(this.isSmartPointer){return makeClassHandle(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:rawPointer,smartPtrType:this,smartPtr:ptr})}else {return makeClassHandle(this.registeredClass.instancePrototype,{ptrType:this,ptr:ptr})}}var actualType=this.registeredClass.getActualType(rawPointer);var registeredPointerRecord=registeredPointers[actualType];if(!registeredPointerRecord){return makeDefaultHandle.call(this)}var toType;if(this.isConst){toType=registeredPointerRecord.constPointerType;}else {toType=registeredPointerRecord.pointerType;}var dp=downcastPointer(rawPointer,this.registeredClass,toType.registeredClass);if(dp===null){return makeDefaultHandle.call(this)}if(this.isSmartPointer){return makeClassHandle(toType.registeredClass.instancePrototype,{ptrType:toType,ptr:dp,smartPtrType:this,smartPtr:ptr})}else {return makeClassHandle(toType.registeredClass.instancePrototype,{ptrType:toType,ptr:dp})}}function attachFinalizer(handle){if("undefined"===typeof FinalizationRegistry){attachFinalizer=handle=>handle;return handle}finalizationRegistry=new FinalizationRegistry(info=>{releaseClassHandle(info.$$);});attachFinalizer=handle=>{var $$=handle.$$;var hasSmartPtr=!!$$.smartPtr;if(hasSmartPtr){var info={$$:$$};finalizationRegistry.register(handle,info,handle);}return handle};detachFinalizer=handle=>finalizationRegistry.unregister(handle);return attachFinalizer(handle)}function __embind_create_inheriting_constructor(constructorName,wrapperType,properties){constructorName=readLatin1String(constructorName);wrapperType=requireRegisteredType(wrapperType,"wrapper");properties=Emval.toValue(properties);var arraySlice=[].slice;var registeredClass=wrapperType.registeredClass;var wrapperPrototype=registeredClass.instancePrototype;var baseClass=registeredClass.baseClass;var baseClassPrototype=baseClass.instancePrototype;var baseConstructor=registeredClass.baseClass.constructor;var ctor=createNamedFunction(constructorName,function(){registeredClass.baseClass.pureVirtualFunctions.forEach(function(name){if(this[name]===baseClassPrototype[name]){throw new PureVirtualError(`Pure virtual function ${name} must be implemented in JavaScript`)}}.bind(this));Object.defineProperty(this,"__parent",{value:wrapperPrototype});this["__construct"].apply(this,arraySlice.call(arguments));});wrapperPrototype["__construct"]=function __construct(){if(this===wrapperPrototype){throwBindingError("Pass correct 'this' to __construct");}var inner=baseConstructor["implement"].apply(undefined,[this].concat(arraySlice.call(arguments)));detachFinalizer(inner);var $$=inner.$$;inner["notifyOnDestruction"]();$$.preservePointerOnDelete=true;Object.defineProperties(this,{$$:{value:$$}});attachFinalizer(this);registerInheritedInstance(registeredClass,$$.ptr,this);};wrapperPrototype["__destruct"]=function __destruct(){if(this===wrapperPrototype){throwBindingError("Pass correct 'this' to __destruct");}detachFinalizer(this);unregisterInheritedInstance(registeredClass,this.$$.ptr);};ctor.prototype=Object.create(wrapperPrototype);for(var p in properties){ctor.prototype[p]=properties[p];}return Emval.toHandle(ctor)}var structRegistrations={};function runDestructors(destructors){while(destructors.length){var ptr=destructors.pop();var del=destructors.pop();del(ptr);}}function simpleReadValueFromPointer(pointer){return this["fromWireType"](HEAP32[pointer>>2])}var awaitingDependencies={};var typeDependencies={};function whenDependentTypesAreResolved(myTypes,dependentTypes,getTypeConverters){myTypes.forEach(function(type){typeDependencies[type]=dependentTypes;});function onComplete(typeConverters){var myTypeConverters=getTypeConverters(typeConverters);if(myTypeConverters.length!==myTypes.length){throwInternalError("Mismatched type converter count");}for(var i=0;i<myTypes.length;++i){registerType(myTypes[i],myTypeConverters[i]);}}var typeConverters=new Array(dependentTypes.length);var unregisteredTypes=[];var registered=0;dependentTypes.forEach((dt,i)=>{if(registeredTypes.hasOwnProperty(dt)){typeConverters[i]=registeredTypes[dt];}else {unregisteredTypes.push(dt);if(!awaitingDependencies.hasOwnProperty(dt)){awaitingDependencies[dt]=[];}awaitingDependencies[dt].push(()=>{typeConverters[i]=registeredTypes[dt];++registered;if(registered===unregisteredTypes.length){onComplete(typeConverters);}});}});if(0===unregisteredTypes.length){onComplete(typeConverters);}}function __embind_finalize_value_object(structType){var reg=structRegistrations[structType];delete structRegistrations[structType];var rawConstructor=reg.rawConstructor;var rawDestructor=reg.rawDestructor;var fieldRecords=reg.fields;var fieldTypes=fieldRecords.map(field=>field.getterReturnType).concat(fieldRecords.map(field=>field.setterArgumentType));whenDependentTypesAreResolved([structType],fieldTypes,fieldTypes=>{var fields={};fieldRecords.forEach((field,i)=>{var fieldName=field.fieldName;var getterReturnType=fieldTypes[i];var getter=field.getter;var getterContext=field.getterContext;var setterArgumentType=fieldTypes[i+fieldRecords.length];var setter=field.setter;var setterContext=field.setterContext;fields[fieldName]={read:ptr=>{return getterReturnType["fromWireType"](getter(getterContext,ptr))},write:(ptr,o)=>{var destructors=[];setter(setterContext,ptr,setterArgumentType["toWireType"](destructors,o));runDestructors(destructors);}};});return [{name:reg.name,"fromWireType":function(ptr){var rv={};for(var i in fields){rv[i]=fields[i].read(ptr);}rawDestructor(ptr);return rv},"toWireType":function(destructors,o){for(var fieldName in fields){if(!(fieldName in o)){throw new TypeError(`Missing field: "${fieldName}"`)}}var ptr=rawConstructor();for(fieldName in fields){fields[fieldName].write(ptr,o[fieldName]);}if(destructors!==null){destructors.push(rawDestructor,ptr);}return ptr},"argPackAdvance":8,"readValueFromPointer":simpleReadValueFromPointer,destructorFunction:rawDestructor}]});}function __embind_register_bigint(primitiveType,name,size,minRange,maxRange){}function getShiftFromSize(size){switch(size){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError(`Unknown type size: ${size}`)}}function registerType(rawType,registeredInstance,options={}){if(!("argPackAdvance"in registeredInstance)){throw new TypeError("registerType registeredInstance requires argPackAdvance")}var name=registeredInstance.name;if(!rawType){throwBindingError(`type "${name}" must have a positive integer typeid pointer`);}if(registeredTypes.hasOwnProperty(rawType)){if(options.ignoreDuplicateRegistrations){return}else {throwBindingError(`Cannot register type '${name}' twice`);}}registeredTypes[rawType]=registeredInstance;delete typeDependencies[rawType];if(awaitingDependencies.hasOwnProperty(rawType)){var callbacks=awaitingDependencies[rawType];delete awaitingDependencies[rawType];callbacks.forEach(cb=>cb());}}function __embind_register_bool(rawType,name,size,trueValue,falseValue){var shift=getShiftFromSize(size);name=readLatin1String(name);registerType(rawType,{name:name,"fromWireType":function(wt){return !!wt},"toWireType":function(destructors,o){return o?trueValue:falseValue},"argPackAdvance":8,"readValueFromPointer":function(pointer){var heap;if(size===1){heap=HEAP8;}else if(size===2){heap=HEAP16;}else if(size===4){heap=HEAP32;}else {throw new TypeError("Unknown boolean type size: "+name)}return this["fromWireType"](heap[pointer>>shift])},destructorFunction:null});}function ClassHandle_isAliasOf(other){if(!(this instanceof ClassHandle)){return false}if(!(other instanceof ClassHandle)){return false}var leftClass=this.$$.ptrType.registeredClass;var left=this.$$.ptr;var rightClass=other.$$.ptrType.registeredClass;var right=other.$$.ptr;while(leftClass.baseClass){left=leftClass.upcast(left);leftClass=leftClass.baseClass;}while(rightClass.baseClass){right=rightClass.upcast(right);rightClass=rightClass.baseClass;}return leftClass===rightClass&&left===right}function shallowCopyInternalPointer(o){return {count:o.count,deleteScheduled:o.deleteScheduled,preservePointerOnDelete:o.preservePointerOnDelete,ptr:o.ptr,ptrType:o.ptrType,smartPtr:o.smartPtr,smartPtrType:o.smartPtrType}}function throwInstanceAlreadyDeleted(obj){function getInstanceTypeName(handle){return handle.$$.ptrType.registeredClass.name}throwBindingError(getInstanceTypeName(obj)+" instance already deleted");}function ClassHandle_clone(){if(!this.$$.ptr){throwInstanceAlreadyDeleted(this);}if(this.$$.preservePointerOnDelete){this.$$.count.value+=1;return this}else {var clone=attachFinalizer(Object.create(Object.getPrototypeOf(this),{$$:{value:shallowCopyInternalPointer(this.$$)}}));clone.$$.count.value+=1;clone.$$.deleteScheduled=false;return clone}}function ClassHandle_delete(){if(!this.$$.ptr){throwInstanceAlreadyDeleted(this);}if(this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete){throwBindingError("Object already scheduled for deletion");}detachFinalizer(this);releaseClassHandle(this.$$);if(!this.$$.preservePointerOnDelete){this.$$.smartPtr=undefined;this.$$.ptr=undefined;}}function ClassHandle_isDeleted(){return !this.$$.ptr}function ClassHandle_deleteLater(){if(!this.$$.ptr){throwInstanceAlreadyDeleted(this);}if(this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete){throwBindingError("Object already scheduled for deletion");}deletionQueue.push(this);if(deletionQueue.length===1&&delayFunction){delayFunction(flushPendingDeletes);}this.$$.deleteScheduled=true;return this}function init_ClassHandle(){ClassHandle.prototype["isAliasOf"]=ClassHandle_isAliasOf;ClassHandle.prototype["clone"]=ClassHandle_clone;ClassHandle.prototype["delete"]=ClassHandle_delete;ClassHandle.prototype["isDeleted"]=ClassHandle_isDeleted;ClassHandle.prototype["deleteLater"]=ClassHandle_deleteLater;}function ClassHandle(){}function ensureOverloadTable(proto,methodName,humanName){if(undefined===proto[methodName].overloadTable){var prevFunc=proto[methodName];proto[methodName]=function(){if(!proto[methodName].overloadTable.hasOwnProperty(arguments.length)){throwBindingError(`Function '${humanName}' called with an invalid number of arguments (${arguments.length}) - expects one of (${proto[methodName].overloadTable})!`);}return proto[methodName].overloadTable[arguments.length].apply(this,arguments)};proto[methodName].overloadTable=[];proto[methodName].overloadTable[prevFunc.argCount]=prevFunc;}}function exposePublicSymbol(name,value,numArguments){if(Module.hasOwnProperty(name)){if(undefined===numArguments||undefined!==Module[name].overloadTable&&undefined!==Module[name].overloadTable[numArguments]){throwBindingError(`Cannot register public name '${name}' twice`);}ensureOverloadTable(Module,name,name);if(Module.hasOwnProperty(numArguments)){throwBindingError(`Cannot register multiple overloads of a function with the same number of arguments (${numArguments})!`);}Module[name].overloadTable[numArguments]=value;}else {Module[name]=value;if(undefined!==numArguments){Module[name].numArguments=numArguments;}}}function RegisteredClass(name,constructor,instancePrototype,rawDestructor,baseClass,getActualType,upcast,downcast){this.name=name;this.constructor=constructor;this.instancePrototype=instancePrototype;this.rawDestructor=rawDestructor;this.baseClass=baseClass;this.getActualType=getActualType;this.upcast=upcast;this.downcast=downcast;this.pureVirtualFunctions=[];}function upcastPointer(ptr,ptrClass,desiredClass){while(ptrClass!==desiredClass){if(!ptrClass.upcast){throwBindingError(`Expected null or instance of ${desiredClass.name}, got an instance of ${ptrClass.name}`);}ptr=ptrClass.upcast(ptr);ptrClass=ptrClass.baseClass;}return ptr}function constNoSmartPtrRawPointerToWireType(destructors,handle){if(handle===null){if(this.isReference){throwBindingError(`null is not a valid ${this.name}`);}return 0}if(!handle.$$){throwBindingError(`Cannot pass "${embindRepr(handle)}" as a ${this.name}`);}if(!handle.$$.ptr){throwBindingError(`Cannot pass deleted object as a pointer of type ${this.name}`);}var handleClass=handle.$$.ptrType.registeredClass;var ptr=upcastPointer(handle.$$.ptr,handleClass,this.registeredClass);return ptr}function genericPointerToWireType(destructors,handle){var ptr;if(handle===null){if(this.isReference){throwBindingError(`null is not a valid ${this.name}`);}if(this.isSmartPointer){ptr=this.rawConstructor();if(destructors!==null){destructors.push(this.rawDestructor,ptr);}return ptr}else {return 0}}if(!handle.$$){throwBindingError(`Cannot pass "${embindRepr(handle)}" as a ${this.name}`);}if(!handle.$$.ptr){throwBindingError(`Cannot pass deleted object as a pointer of type ${this.name}`);}if(!this.isConst&&handle.$$.ptrType.isConst){throwBindingError(`Cannot convert argument of type ${handle.$$.smartPtrType?handle.$$.smartPtrType.name:handle.$$.ptrType.name} to parameter type ${this.name}`);}var handleClass=handle.$$.ptrType.registeredClass;ptr=upcastPointer(handle.$$.ptr,handleClass,this.registeredClass);if(this.isSmartPointer){if(undefined===handle.$$.smartPtr){throwBindingError("Passing raw pointer to smart pointer is illegal");}switch(this.sharingPolicy){case 0:if(handle.$$.smartPtrType===this){ptr=handle.$$.smartPtr;}else {throwBindingError(`Cannot convert argument of type ${handle.$$.smartPtrType?handle.$$.smartPtrType.name:handle.$$.ptrType.name} to parameter type ${this.name}`);}break;case 1:ptr=handle.$$.smartPtr;break;case 2:if(handle.$$.smartPtrType===this){ptr=handle.$$.smartPtr;}else {var clonedHandle=handle["clone"]();ptr=this.rawShare(ptr,Emval.toHandle(function(){clonedHandle["delete"]();}));if(destructors!==null){destructors.push(this.rawDestructor,ptr);}}break;default:throwBindingError("Unsupporting sharing policy");}}return ptr}function nonConstNoSmartPtrRawPointerToWireType(destructors,handle){if(handle===null){if(this.isReference){throwBindingError(`null is not a valid ${this.name}`);}return 0}if(!handle.$$){throwBindingError(`Cannot pass "${embindRepr(handle)}" as a ${this.name}`);}if(!handle.$$.ptr){throwBindingError(`Cannot pass deleted object as a pointer of type ${this.name}`);}if(handle.$$.ptrType.isConst){throwBindingError(`Cannot convert argument of type ${handle.$$.ptrType.name} to parameter type ${this.name}`);}var handleClass=handle.$$.ptrType.registeredClass;var ptr=upcastPointer(handle.$$.ptr,handleClass,this.registeredClass);return ptr}function RegisteredPointer_getPointee(ptr){if(this.rawGetPointee){ptr=this.rawGetPointee(ptr);}return ptr}function RegisteredPointer_destructor(ptr){if(this.rawDestructor){this.rawDestructor(ptr);}}function RegisteredPointer_deleteObject(handle){if(handle!==null){handle["delete"]();}}function init_RegisteredPointer(){RegisteredPointer.prototype.getPointee=RegisteredPointer_getPointee;RegisteredPointer.prototype.destructor=RegisteredPointer_destructor;RegisteredPointer.prototype["argPackAdvance"]=8;RegisteredPointer.prototype["readValueFromPointer"]=simpleReadValueFromPointer;RegisteredPointer.prototype["deleteObject"]=RegisteredPointer_deleteObject;RegisteredPointer.prototype["fromWireType"]=RegisteredPointer_fromWireType;}function RegisteredPointer(name,registeredClass,isReference,isConst,isSmartPointer,pointeeType,sharingPolicy,rawGetPointee,rawConstructor,rawShare,rawDestructor){this.name=name;this.registeredClass=registeredClass;this.isReference=isReference;this.isConst=isConst;this.isSmartPointer=isSmartPointer;this.pointeeType=pointeeType;this.sharingPolicy=sharingPolicy;this.rawGetPointee=rawGetPointee;this.rawConstructor=rawConstructor;this.rawShare=rawShare;this.rawDestructor=rawDestructor;if(!isSmartPointer&&registeredClass.baseClass===undefined){if(isConst){this["toWireType"]=constNoSmartPtrRawPointerToWireType;this.destructorFunction=null;}else {this["toWireType"]=nonConstNoSmartPtrRawPointerToWireType;this.destructorFunction=null;}}else {this["toWireType"]=genericPointerToWireType;}}function replacePublicSymbol(name,value,numArguments){if(!Module.hasOwnProperty(name)){throwInternalError("Replacing nonexistant public symbol");}if(undefined!==Module[name].overloadTable&&undefined!==numArguments){Module[name].overloadTable[numArguments]=value;}else {Module[name]=value;Module[name].argCount=numArguments;}}function dynCallLegacy(sig,ptr,args){var f=Module["dynCall_"+sig];return args&&args.length?f.apply(null,[ptr].concat(args)):f.call(null,ptr)}var wasmTableMirror=[];function getWasmTableEntry(funcPtr){var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr);}return func}function dynCall(sig,ptr,args){if(sig.includes("j")){return dynCallLegacy(sig,ptr,args)}var rtn=getWasmTableEntry(ptr).apply(null,args);return rtn}function getDynCaller(sig,ptr){var argCache=[];return function(){argCache.length=0;Object.assign(argCache,arguments);return dynCall(sig,ptr,argCache)}}function embind__requireFunction(signature,rawFunction){signature=readLatin1String(signature);function makeDynCaller(){if(signature.includes("j")){return getDynCaller(signature,rawFunction)}return getWasmTableEntry(rawFunction)}var fp=makeDynCaller();if(typeof fp!="function"){throwBindingError(`unknown function pointer with signature ${signature}: ${rawFunction}`);}return fp}var UnboundTypeError=undefined;function throwUnboundTypeError(message,types){var unboundTypes=[];var seen={};function visit(type){if(seen[type]){return}if(registeredTypes[type]){return}if(typeDependencies[type]){typeDependencies[type].forEach(visit);return}unboundTypes.push(type);seen[type]=true;}types.forEach(visit);throw new UnboundTypeError(`${message}: `+unboundTypes.map(getTypeName).join([", "]))}function __embind_register_class(rawType,rawPointerType,rawConstPointerType,baseClassRawType,getActualTypeSignature,getActualType,upcastSignature,upcast,downcastSignature,downcast,name,destructorSignature,rawDestructor){name=readLatin1String(name);getActualType=embind__requireFunction(getActualTypeSignature,getActualType);if(upcast){upcast=embind__requireFunction(upcastSignature,upcast);}if(downcast){downcast=embind__requireFunction(downcastSignature,downcast);}rawDestructor=embind__requireFunction(destructorSignature,rawDestructor);var legalFunctionName=makeLegalFunctionName(name);exposePublicSymbol(legalFunctionName,function(){throwUnboundTypeError(`Cannot construct ${name} due to unbound types`,[baseClassRawType]);});whenDependentTypesAreResolved([rawType,rawPointerType,rawConstPointerType],baseClassRawType?[baseClassRawType]:[],function(base){base=base[0];var baseClass;var basePrototype;if(baseClassRawType){baseClass=base.registeredClass;basePrototype=baseClass.instancePrototype;}else {basePrototype=ClassHandle.prototype;}var constructor=createNamedFunction(legalFunctionName,function(){if(Object.getPrototypeOf(this)!==instancePrototype){throw new BindingError("Use 'new' to construct "+name)}if(undefined===registeredClass.constructor_body){throw new BindingError(name+" has no accessible constructor")}var body=registeredClass.constructor_body[arguments.length];if(undefined===body){throw new BindingError(`Tried to invoke ctor of ${name} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(registeredClass.constructor_body).toString()}) parameters instead!`)}return body.apply(this,arguments)});var instancePrototype=Object.create(basePrototype,{constructor:{value:constructor}});constructor.prototype=instancePrototype;var registeredClass=new RegisteredClass(name,constructor,instancePrototype,rawDestructor,baseClass,getActualType,upcast,downcast);if(registeredClass.baseClass){if(registeredClass.baseClass.__derivedClasses===undefined){registeredClass.baseClass.__derivedClasses=[];}registeredClass.baseClass.__derivedClasses.push(registeredClass);}var referenceConverter=new RegisteredPointer(name,registeredClass,true,false,false);var pointerConverter=new RegisteredPointer(name+"*",registeredClass,false,false,false);var constPointerConverter=new RegisteredPointer(name+" const*",registeredClass,false,true,false);registeredPointers[rawType]={pointerType:pointerConverter,constPointerType:constPointerConverter};replacePublicSymbol(legalFunctionName,constructor);return [referenceConverter,pointerConverter,constPointerConverter]});}function craftInvokerFunction(humanName,argTypes,classType,cppInvokerFunc,cppTargetFunc,isAsync){var argCount=argTypes.length;if(argCount<2){throwBindingError("argTypes array size mismatch! Must at least get return value and 'this' types!");}var isClassMethodFunc=argTypes[1]!==null&&classType!==null;var needsDestructorStack=false;for(var i=1;i<argTypes.length;++i){if(argTypes[i]!==null&&argTypes[i].destructorFunction===undefined){needsDestructorStack=true;break}}var returns=argTypes[0].name!=="void";var expectedArgCount=argCount-2;var argsWired=new Array(expectedArgCount);var invokerFuncArgs=[];var destructors=[];return function(){if(arguments.length!==expectedArgCount){throwBindingError(`function ${humanName} called with ${arguments.length} arguments, expected ${expectedArgCount} args!`);}destructors.length=0;var thisWired;invokerFuncArgs.length=isClassMethodFunc?2:1;invokerFuncArgs[0]=cppTargetFunc;if(isClassMethodFunc){thisWired=argTypes[1]["toWireType"](destructors,this);invokerFuncArgs[1]=thisWired;}for(var i=0;i<expectedArgCount;++i){argsWired[i]=argTypes[i+2]["toWireType"](destructors,arguments[i]);invokerFuncArgs.push(argsWired[i]);}var rv=cppInvokerFunc.apply(null,invokerFuncArgs);function onDone(rv){if(needsDestructorStack){runDestructors(destructors);}else {for(var i=isClassMethodFunc?1:2;i<argTypes.length;i++){var param=i===1?thisWired:argsWired[i-2];if(argTypes[i].destructorFunction!==null){argTypes[i].destructorFunction(param);}}}if(returns){return argTypes[0]["fromWireType"](rv)}}return onDone(rv)}}function heap32VectorToArray(count,firstElement){var array=[];for(var i=0;i<count;i++){array.push(HEAPU32[firstElement+i*4>>2]);}return array}function __embind_register_class_class_function(rawClassType,methodName,argCount,rawArgTypesAddr,invokerSignature,rawInvoker,fn,isAsync){var rawArgTypes=heap32VectorToArray(argCount,rawArgTypesAddr);methodName=readLatin1String(methodName);rawInvoker=embind__requireFunction(invokerSignature,rawInvoker);whenDependentTypesAreResolved([],[rawClassType],function(classType){classType=classType[0];var humanName=`${classType.name}.${methodName}`;function unboundTypesHandler(){throwUnboundTypeError(`Cannot call ${humanName} due to unbound types`,rawArgTypes);}if(methodName.startsWith("@@")){methodName=Symbol[methodName.substring(2)];}var proto=classType.registeredClass.constructor;if(undefined===proto[methodName]){unboundTypesHandler.argCount=argCount-1;proto[methodName]=unboundTypesHandler;}else {ensureOverloadTable(proto,methodName,humanName);proto[methodName].overloadTable[argCount-1]=unboundTypesHandler;}whenDependentTypesAreResolved([],rawArgTypes,function(argTypes){var invokerArgsArray=[argTypes[0],null].concat(argTypes.slice(1));var func=craftInvokerFunction(humanName,invokerArgsArray,null,rawInvoker,fn);if(undefined===proto[methodName].overloadTable){func.argCount=argCount-1;proto[methodName]=func;}else {proto[methodName].overloadTable[argCount-1]=func;}if(classType.registeredClass.__derivedClasses){for(const derivedClass of classType.registeredClass.__derivedClasses){if(!derivedClass.constructor.hasOwnProperty(methodName)){derivedClass.constructor[methodName]=func;}}}return []});return []});}function __embind_register_class_constructor(rawClassType,argCount,rawArgTypesAddr,invokerSignature,invoker,rawConstructor){assert(argCount>0);var rawArgTypes=heap32VectorToArray(argCount,rawArgTypesAddr);invoker=embind__requireFunction(invokerSignature,invoker);whenDependentTypesAreResolved([],[rawClassType],function(classType){classType=classType[0];var humanName=`constructor ${classType.name}`;if(undefined===classType.registeredClass.constructor_body){classType.registeredClass.constructor_body=[];}if(undefined!==classType.registeredClass.constructor_body[argCount-1]){throw new BindingError(`Cannot register multiple constructors with identical number of parameters (${argCount-1}) for class '${classType.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`)}classType.registeredClass.constructor_body[argCount-1]=()=>{throwUnboundTypeError(`Cannot construct ${classType.name} due to unbound types`,rawArgTypes);};whenDependentTypesAreResolved([],rawArgTypes,function(argTypes){argTypes.splice(1,0,null);classType.registeredClass.constructor_body[argCount-1]=craftInvokerFunction(humanName,argTypes,null,invoker,rawConstructor);return []});return []});}function __embind_register_class_function(rawClassType,methodName,argCount,rawArgTypesAddr,invokerSignature,rawInvoker,context,isPureVirtual,isAsync){var rawArgTypes=heap32VectorToArray(argCount,rawArgTypesAddr);methodName=readLatin1String(methodName);rawInvoker=embind__requireFunction(invokerSignature,rawInvoker);whenDependentTypesAreResolved([],[rawClassType],function(classType){classType=classType[0];var humanName=`${classType.name}.${methodName}`;if(methodName.startsWith("@@")){methodName=Symbol[methodName.substring(2)];}if(isPureVirtual){classType.registeredClass.pureVirtualFunctions.push(methodName);}function unboundTypesHandler(){throwUnboundTypeError(`Cannot call ${humanName} due to unbound types`,rawArgTypes);}var proto=classType.registeredClass.instancePrototype;var method=proto[methodName];if(undefined===method||undefined===method.overloadTable&&method.className!==classType.name&&method.argCount===argCount-2){unboundTypesHandler.argCount=argCount-2;unboundTypesHandler.className=classType.name;proto[methodName]=unboundTypesHandler;}else {ensureOverloadTable(proto,methodName,humanName);proto[methodName].overloadTable[argCount-2]=unboundTypesHandler;}whenDependentTypesAreResolved([],rawArgTypes,function(argTypes){var memberFunction=craftInvokerFunction(humanName,argTypes,classType,rawInvoker,context);if(undefined===proto[methodName].overloadTable){memberFunction.argCount=argCount-2;proto[methodName]=memberFunction;}else {proto[methodName].overloadTable[argCount-2]=memberFunction;}return []});return []});}function validateThis(this_,classType,humanName){if(!(this_ instanceof Object)){throwBindingError(`${humanName} with invalid "this": ${this_}`);}if(!(this_ instanceof classType.registeredClass.constructor)){throwBindingError(`${humanName} incompatible with "this" of type ${this_.constructor.name}`);}if(!this_.$$.ptr){throwBindingError(`cannot call emscripten binding method ${humanName} on deleted object`);}return upcastPointer(this_.$$.ptr,this_.$$.ptrType.registeredClass,classType.registeredClass)}function __embind_register_class_property(classType,fieldName,getterReturnType,getterSignature,getter,getterContext,setterArgumentType,setterSignature,setter,setterContext){fieldName=readLatin1String(fieldName);getter=embind__requireFunction(getterSignature,getter);whenDependentTypesAreResolved([],[classType],function(classType){classType=classType[0];var humanName=`${classType.name}.${fieldName}`;var desc={get:function(){throwUnboundTypeError(`Cannot access ${humanName} due to unbound types`,[getterReturnType,setterArgumentType]);},enumerable:true,configurable:true};if(setter){desc.set=()=>{throwUnboundTypeError(`Cannot access ${humanName} due to unbound types`,[getterReturnType,setterArgumentType]);};}else {desc.set=v=>{throwBindingError(humanName+" is a read-only property");};}Object.defineProperty(classType.registeredClass.instancePrototype,fieldName,desc);whenDependentTypesAreResolved([],setter?[getterReturnType,setterArgumentType]:[getterReturnType],function(types){var getterReturnType=types[0];var desc={get:function(){var ptr=validateThis(this,classType,humanName+" getter");return getterReturnType["fromWireType"](getter(getterContext,ptr))},enumerable:true};if(setter){setter=embind__requireFunction(setterSignature,setter);var setterArgumentType=types[1];desc.set=function(v){var ptr=validateThis(this,classType,humanName+" setter");var destructors=[];setter(setterContext,ptr,setterArgumentType["toWireType"](destructors,v));runDestructors(destructors);};}Object.defineProperty(classType.registeredClass.instancePrototype,fieldName,desc);return []});return []});}function __embind_register_constant(name,type,value){name=readLatin1String(name);whenDependentTypesAreResolved([],[type],function(type){type=type[0];Module[name]=type["fromWireType"](value);return []});}function __emval_decref(handle){if(handle>=emval_handles.reserved&&0===--emval_handles.get(handle).refcount){emval_handles.free(handle);}}function __embind_register_emval(rawType,name){name=readLatin1String(name);registerType(rawType,{name:name,"fromWireType":function(handle){var rv=Emval.toValue(handle);__emval_decref(handle);return rv},"toWireType":function(destructors,value){return Emval.toHandle(value)},"argPackAdvance":8,"readValueFromPointer":simpleReadValueFromPointer,destructorFunction:null});}function enumReadValueFromPointer(name,shift,signed){switch(shift){case 0:return function(pointer){var heap=signed?HEAP8:HEAPU8;return this["fromWireType"](heap[pointer])};case 1:return function(pointer){var heap=signed?HEAP16:HEAPU16;return this["fromWireType"](heap[pointer>>1])};case 2:return function(pointer){var heap=signed?HEAP32:HEAPU32;return this["fromWireType"](heap[pointer>>2])};default:throw new TypeError("Unknown integer type: "+name)}}function __embind_register_enum(rawType,name,size,isSigned){var shift=getShiftFromSize(size);name=readLatin1String(name);function ctor(){}ctor.values={};registerType(rawType,{name:name,constructor:ctor,"fromWireType":function(c){return this.constructor.values[c]},"toWireType":function(destructors,c){return c.value},"argPackAdvance":8,"readValueFromPointer":enumReadValueFromPointer(name,shift,isSigned),destructorFunction:null});exposePublicSymbol(name,ctor);}function __embind_register_enum_value(rawEnumType,name,enumValue){var enumType=requireRegisteredType(rawEnumType,"enum");name=readLatin1String(name);var Enum=enumType.constructor;var Value=Object.create(enumType.constructor.prototype,{value:{value:enumValue},constructor:{value:createNamedFunction(`${enumType.name}_${name}`,function(){})}});Enum.values[enumValue]=Value;Enum[name]=Value;}function embindRepr(v){if(v===null){return "null"}var t=typeof v;if(t==="object"||t==="array"||t==="function"){return v.toString()}else {return ""+v}}function floatReadValueFromPointer(name,shift){switch(shift){case 2:return function(pointer){return this["fromWireType"](HEAPF32[pointer>>2])};case 3:return function(pointer){return this["fromWireType"](HEAPF64[pointer>>3])};default:throw new TypeError("Unknown float type: "+name)}}function __embind_register_float(rawType,name,size){var shift=getShiftFromSize(size);name=readLatin1String(name);registerType(rawType,{name:name,"fromWireType":function(value){return value},"toWireType":function(destructors,value){return value},"argPackAdvance":8,"readValueFromPointer":floatReadValueFromPointer(name,shift),destructorFunction:null});}function __embind_register_function(name,argCount,rawArgTypesAddr,signature,rawInvoker,fn,isAsync){var argTypes=heap32VectorToArray(argCount,rawArgTypesAddr);name=readLatin1String(name);rawInvoker=embind__requireFunction(signature,rawInvoker);exposePublicSymbol(name,function(){throwUnboundTypeError(`Cannot call ${name} due to unbound types`,argTypes);},argCount-1);whenDependentTypesAreResolved([],argTypes,function(argTypes){var invokerArgsArray=[argTypes[0],null].concat(argTypes.slice(1));replacePublicSymbol(name,craftInvokerFunction(name,invokerArgsArray,null,rawInvoker,fn),argCount-1);return []});}function integerReadValueFromPointer(name,shift,signed){switch(shift){case 0:return signed?function readS8FromPointer(pointer){return HEAP8[pointer]}:function readU8FromPointer(pointer){return HEAPU8[pointer]};case 1:return signed?function readS16FromPointer(pointer){return HEAP16[pointer>>1]}:function readU16FromPointer(pointer){return HEAPU16[pointer>>1]};case 2:return signed?function readS32FromPointer(pointer){return HEAP32[pointer>>2]}:function readU32FromPointer(pointer){return HEAPU32[pointer>>2]};default:throw new TypeError("Unknown integer type: "+name)}}function __embind_register_integer(primitiveType,name,size,minRange,maxRange){name=readLatin1String(name);var shift=getShiftFromSize(size);var fromWireType=value=>value;if(minRange===0){var bitshift=32-8*size;fromWireType=value=>value<<bitshift>>>bitshift;}var isUnsignedType=name.includes("unsigned");var checkAssertions=(value,toTypeName)=>{};var toWireType;if(isUnsignedType){toWireType=function(destructors,value){checkAssertions(value,this.name);return value>>>0};}else {toWireType=function(destructors,value){checkAssertions(value,this.name);return value};}registerType(primitiveType,{name:name,"fromWireType":fromWireType,"toWireType":toWireType,"argPackAdvance":8,"readValueFromPointer":integerReadValueFromPointer(name,shift,minRange!==0),destructorFunction:null});}function __embind_register_memory_view(rawType,dataTypeIndex,name){var typeMapping=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array];var TA=typeMapping[dataTypeIndex];function decodeMemoryView(handle){handle=handle>>2;var heap=HEAPU32;var size=heap[handle];var data=heap[handle+1];return new TA(heap.buffer,data,size)}name=readLatin1String(name);registerType(rawType,{name:name,"fromWireType":decodeMemoryView,"argPackAdvance":8,"readValueFromPointer":decodeMemoryView},{ignoreDuplicateRegistrations:true});}function stringToUTF8Array(str,heap,outIdx,maxBytesToWrite){if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023;}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u;}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63;}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63;}else {if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63;}}heap[outIdx]=0;return outIdx-startIdx}function stringToUTF8(str,outPtr,maxBytesToWrite){return stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite)}function lengthBytesUTF8(str){var len=0;for(var i=0;i<str.length;++i){var c=str.charCodeAt(i);if(c<=127){len++;}else if(c<=2047){len+=2;}else if(c>=55296&&c<=57343){len+=4;++i;}else {len+=3;}}return len}function __embind_register_std_string(rawType,name){name=readLatin1String(name);var stdStringIsUTF8=name==="std::string";registerType(rawType,{name:name,"fromWireType":function(value){var length=HEAPU32[value>>2];var payload=value+4;var str;if(stdStringIsUTF8){var decodeStartPtr=payload;for(var i=0;i<=length;++i){var currentBytePtr=payload+i;if(i==length||HEAPU8[currentBytePtr]==0){var maxRead=currentBytePtr-decodeStartPtr;var stringSegment=UTF8ToString(decodeStartPtr,maxRead);if(str===undefined){str=stringSegment;}else {str+=String.fromCharCode(0);str+=stringSegment;}decodeStartPtr=currentBytePtr+1;}}}else {var a=new Array(length);for(var i=0;i<length;++i){a[i]=String.fromCharCode(HEAPU8[payload+i]);}str=a.join("");}_free(value);return str},"toWireType":function(destructors,value){if(value instanceof ArrayBuffer){value=new Uint8Array(value);}var length;var valueIsOfTypeString=typeof value=="string";if(!(valueIsOfTypeString||value instanceof Uint8Array||value instanceof Uint8ClampedArray||value instanceof Int8Array)){throwBindingError("Cannot pass non-string to std::string");}if(stdStringIsUTF8&&valueIsOfTypeString){length=lengthBytesUTF8(value);}else {length=value.length;}var base=_malloc(4+length+1);var ptr=base+4;HEAPU32[base>>2]=length;if(stdStringIsUTF8&&valueIsOfTypeString){stringToUTF8(value,ptr,length+1);}else {if(valueIsOfTypeString){for(var i=0;i<length;++i){var charCode=value.charCodeAt(i);if(charCode>255){_free(ptr);throwBindingError("String has UTF-16 code units that do not fit in 8 bits");}HEAPU8[ptr+i]=charCode;}}else {for(var i=0;i<length;++i){HEAPU8[ptr+i]=value[i];}}}if(destructors!==null){destructors.push(_free,base);}return base},"argPackAdvance":8,"readValueFromPointer":simpleReadValueFromPointer,destructorFunction:function(ptr){_free(ptr);}});}var UTF16Decoder=typeof TextDecoder!="undefined"?new TextDecoder("utf-16le"):undefined;function UTF16ToString(ptr,maxBytesToRead){var endPtr=ptr;var idx=endPtr>>1;var maxIdx=idx+maxBytesToRead/2;while(!(idx>=maxIdx)&&HEAPU16[idx])++idx;endPtr=idx<<1;if(endPtr-ptr>32&&UTF16Decoder)return UTF16Decoder.decode(HEAPU8.subarray(ptr,endPtr));var str="";for(var i=0;!(i>=maxBytesToRead/2);++i){var codeUnit=HEAP16[ptr+i*2>>1];if(codeUnit==0)break;str+=String.fromCharCode(codeUnit);}return str}function stringToUTF16(str,outPtr,maxBytesToWrite){if(maxBytesToWrite===undefined){maxBytesToWrite=2147483647;}if(maxBytesToWrite<2)return 0;maxBytesToWrite-=2;var startPtr=outPtr;var numCharsToWrite=maxBytesToWrite<str.length*2?maxBytesToWrite/2:str.length;for(var i=0;i<numCharsToWrite;++i){var codeUnit=str.charCodeAt(i);HEAP16[outPtr>>1]=codeUnit;outPtr+=2;}HEAP16[outPtr>>1]=0;return outPtr-startPtr}function lengthBytesUTF16(str){return str.length*2}function UTF32ToString(ptr,maxBytesToRead){var i=0;var str="";while(!(i>=maxBytesToRead/4)){var utf32=HEAP32[ptr+i*4>>2];if(utf32==0)break;++i;if(utf32>=65536){var ch=utf32-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023);}else {str+=String.fromCharCode(utf32);}}return str}function stringToUTF32(str,outPtr,maxBytesToWrite){if(maxBytesToWrite===undefined){maxBytesToWrite=2147483647;}if(maxBytesToWrite<4)return 0;var startPtr=outPtr;var endPtr=startPtr+maxBytesToWrite-4;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343){var trailSurrogate=str.charCodeAt(++i);codeUnit=65536+((codeUnit&1023)<<10)|trailSurrogate&1023;}HEAP32[outPtr>>2]=codeUnit;outPtr+=4;if(outPtr+4>endPtr)break}HEAP32[outPtr>>2]=0;return outPtr-startPtr}function lengthBytesUTF32(str){var len=0;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343)++i;len+=4;}return len}function __embind_register_std_wstring(rawType,charSize,name){name=readLatin1String(name);var decodeString,encodeString,getHeap,lengthBytesUTF,shift;if(charSize===2){decodeString=UTF16ToString;encodeString=stringToUTF16;lengthBytesUTF=lengthBytesUTF16;getHeap=()=>HEAPU16;shift=1;}else if(charSize===4){decodeString=UTF32ToString;encodeString=stringToUTF32;lengthBytesUTF=lengthBytesUTF32;getHeap=()=>HEAPU32;shift=2;}registerType(rawType,{name:name,"fromWireType":function(value){var length=HEAPU32[value>>2];var HEAP=getHeap();var str;var decodeStartPtr=value+4;for(var i=0;i<=length;++i){var currentBytePtr=value+4+i*charSize;if(i==length||HEAP[currentBytePtr>>shift]==0){var maxReadBytes=currentBytePtr-decodeStartPtr;var stringSegment=decodeString(decodeStartPtr,maxReadBytes);if(str===undefined){str=stringSegment;}else {str+=String.fromCharCode(0);str+=stringSegment;}decodeStartPtr=currentBytePtr+charSize;}}_free(value);return str},"toWireType":function(destructors,value){if(!(typeof value=="string")){throwBindingError(`Cannot pass non-string to C++ string type ${name}`);}var length=lengthBytesUTF(value);var ptr=_malloc(4+length+charSize);HEAPU32[ptr>>2]=length>>shift;encodeString(value,ptr+4,length+charSize);if(destructors!==null){destructors.push(_free,ptr);}return ptr},"argPackAdvance":8,"readValueFromPointer":simpleReadValueFromPointer,destructorFunction:function(ptr){_free(ptr);}});}function __embind_register_value_object(rawType,name,constructorSignature,rawConstructor,destructorSignature,rawDestructor){structRegistrations[rawType]={name:readLatin1String(name),rawConstructor:embind__requireFunction(constructorSignature,rawConstructor),rawDestructor:embind__requireFunction(destructorSignature,rawDestructor),fields:[]};}function __embind_register_value_object_field(structType,fieldName,getterReturnType,getterSignature,getter,getterContext,setterArgumentType,setterSignature,setter,setterContext){structRegistrations[structType].fields.push({fieldName:readLatin1String(fieldName),getterReturnType:getterReturnType,getter:embind__requireFunction(getterSignature,getter),getterContext:getterContext,setterArgumentType:setterArgumentType,setter:embind__requireFunction(setterSignature,setter),setterContext:setterContext});}function __embind_register_void(rawType,name){name=readLatin1String(name);registerType(rawType,{isVoid:true,name:name,"argPackAdvance":0,"fromWireType":function(){return undefined},"toWireType":function(destructors,o){return undefined}});}function emval_allocateDestructors(destructorsRef){var destructors=[];HEAPU32[destructorsRef>>2]=Emval.toHandle(destructors);return destructors}var emval_symbols={};function getStringOrSymbol(address){var symbol=emval_symbols[address];if(symbol===undefined){return readLatin1String(address)}return symbol}var emval_methodCallers=[];function __emval_call_method(caller,handle,methodName,destructorsRef,args){caller=emval_methodCallers[caller];handle=Emval.toValue(handle);methodName=getStringOrSymbol(methodName);return caller(handle,methodName,emval_allocateDestructors(destructorsRef),args)}function __emval_call_void_method(caller,handle,methodName,args){caller=emval_methodCallers[caller];handle=Emval.toValue(handle);methodName=getStringOrSymbol(methodName);caller(handle,methodName,null,args);}function emval_addMethodCaller(caller){var id=emval_methodCallers.length;emval_methodCallers.push(caller);return id}function emval_lookupTypes(argCount,argTypes){var a=new Array(argCount);for(var i=0;i<argCount;++i){a[i]=requireRegisteredType(HEAPU32[argTypes+i*4>>2],"parameter "+i);}return a}var emval_registeredMethods=[];function __emval_get_method_caller(argCount,argTypes){var types=emval_lookupTypes(argCount,argTypes);var retType=types[0];var signatureName=retType.name+"_$"+types.slice(1).map(function(t){return t.name}).join("_")+"$";var returnId=emval_registeredMethods[signatureName];if(returnId!==undefined){return returnId}var argN=new Array(argCount-1);var invokerFunction=(handle,name,destructors,args)=>{var offset=0;for(var i=0;i<argCount-1;++i){argN[i]=types[i+1]["readValueFromPointer"](args+offset);offset+=types[i+1]["argPackAdvance"];}var rv=handle[name].apply(handle,argN);for(var i=0;i<argCount-1;++i){if(types[i+1].deleteObject){types[i+1].deleteObject(argN[i]);}}if(!retType.isVoid){return retType["toWireType"](destructors,rv)}};returnId=emval_addMethodCaller(invokerFunction);emval_registeredMethods[signatureName]=returnId;return returnId}function __emval_incref(handle){if(handle>4){emval_handles.get(handle).refcount+=1;}}function __emval_run_destructors(handle){var destructors=Emval.toValue(handle);runDestructors(destructors);__emval_decref(handle);}function __emval_take_value(type,arg){type=requireRegisteredType(type,"_emval_take_value");var v=type["readValueFromPointer"](arg);return Emval.toHandle(v)}function _abort(){abort("");}function _emscripten_memcpy_big(dest,src,num){HEAPU8.copyWithin(dest,src,src+num);}function getHeapMax(){return 2147483648}function emscripten_realloc_buffer(size){var b=wasmMemory.buffer;var pages=size-b.byteLength+65535>>>16;try{wasmMemory.grow(pages);updateMemoryViews();return 1}catch(e){}}function _emscripten_resize_heap(requestedSize){var oldSize=HEAPU8.length;requestedSize=requestedSize>>>0;var maxHeapSize=getHeapMax();if(requestedSize>maxHeapSize){return false}var alignUp=(x,multiple)=>x+(multiple-x%multiple)%multiple;for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=emscripten_realloc_buffer(newSize);if(replacement){return true}}return false}var printCharBuffers=[null,[],[]];function printChar(stream,curr){var buffer=printCharBuffers[stream];if(curr===0||curr===10){(stream===1?out:err)(UTF8ArrayToString(buffer,0));buffer.length=0;}else {buffer.push(curr);}}function _fd_write(fd,iov,iovcnt,pnum){var num=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;for(var j=0;j<len;j++){printChar(fd,HEAPU8[ptr+j]);}num+=len;}HEAPU32[pnum>>2]=num;return 0}BindingError=Module["BindingError"]=extendError(Error,"BindingError");init_emval();PureVirtualError=Module["PureVirtualError"]=extendError(Error,"PureVirtualError");embind_init_charCodes();init_embind();InternalError=Module["InternalError"]=extendError(Error,"InternalError");init_ClassHandle();init_RegisteredPointer();UnboundTypeError=Module["UnboundTypeError"]=extendError(Error,"UnboundTypeError");var decodeBase64=typeof atob=="function"?atob:function(input){var keyStr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";var output="";var chr1,chr2,chr3;var enc1,enc2,enc3,enc4;var i=0;input=input.replace(/[^A-Za-z0-9\+\/\=]/g,"");do{enc1=keyStr.indexOf(input.charAt(i++));enc2=keyStr.indexOf(input.charAt(i++));enc3=keyStr.indexOf(input.charAt(i++));enc4=keyStr.indexOf(input.charAt(i++));chr1=enc1<<2|enc2>>4;chr2=(enc2&15)<<4|enc3>>2;chr3=(enc3&3)<<6|enc4;output=output+String.fromCharCode(chr1);if(enc3!==64){output=output+String.fromCharCode(chr2);}if(enc4!==64){output=output+String.fromCharCode(chr3);}}while(i<input.length);return output};function intArrayFromBase64(s){try{var decoded=decodeBase64(s);var bytes=new Uint8Array(decoded.length);for(var i=0;i<decoded.length;++i){bytes[i]=decoded.charCodeAt(i);}return bytes}catch(_){throw new Error("Converting base64 string to bytes failed.")}}function tryParseAsDataURI(filename){if(!isDataURI(filename)){return}return intArrayFromBase64(filename.slice(dataURIPrefix.length))}var wasmImports={"C":___assert_fail,"r":__embind_create_inheriting_constructor,"n":__embind_finalize_value_object,"D":__embind_register_bigint,"H":__embind_register_bool,"e":__embind_register_class,"m":__embind_register_class_class_function,"k":__embind_register_class_constructor,"b":__embind_register_class_function,"d":__embind_register_class_property,"s":__embind_register_constant,"G":__embind_register_emval,"z":__embind_register_enum,"p":__embind_register_enum_value,"w":__embind_register_float,"c":__embind_register_function,"l":__embind_register_integer,"i":__embind_register_memory_view,"v":__embind_register_std_string,"t":__embind_register_std_wstring,"o":__embind_register_value_object,"g":__embind_register_value_object_field,"I":__embind_register_void,"y":__emval_call_method,"h":__emval_call_void_method,"j":__emval_decref,"f":__emval_get_method_caller,"A":__emval_incref,"x":__emval_run_destructors,"B":__emval_take_value,"q":_abort,"F":_emscripten_memcpy_big,"E":_emscripten_resize_heap,"u":_fd_write,"a":wasmMemory};createWasm();var _malloc=function(){return (_malloc=Module["asm"]["L"]).apply(null,arguments)};var _free=function(){return (_free=Module["asm"]["M"]).apply(null,arguments)};var ___getTypeName=function(){return (___getTypeName=Module["asm"]["N"]).apply(null,arguments)};Module["__embind_initialize_bindings"]=function(){return (Module["__embind_initialize_bindings"]=Module["asm"]["O"]).apply(null,arguments)};Module["dynCall_jiji"]=function(){return (Module["dynCall_jiji"]=Module["asm"]["P"]).apply(null,arguments)};var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller;};function run(){if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module["calledRun"]=true;if(ABORT)return;initRuntime();readyPromiseResolve(Module);if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();postRun();}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("");},1);doRun();},1);}else {doRun();}}if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()();}}run();


				  return BOX2D.ready
				}

				);
				})();
				module.exports = BOX2D; 
			} (box2d_release_asm$2));

			var box2d_release_asmExports = box2d_release_asm$2.exports;
			var box2d_release_asm = /*@__PURE__*/getDefaultExportFromCjs(box2d_release_asmExports);

			var box2d_release_asm$1 = /*#__PURE__*/Object.freeze({
				__proto__: null,
				default: box2d_release_asm
			});
			exports("b", box2d_release_asm$1);

		})
	};
}));
//# sourceMappingURL=box2d.release.asm-mtykYuJO.js.map
