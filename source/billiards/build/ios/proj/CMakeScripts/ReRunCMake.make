# Generated by CMake, DO NOT EDIT

TARGETS:= 
empty:= 
space:= $(empty) $(empty)
spaceplus:= $(empty)\ $(empty)

TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/CMakeLists.txt))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/cmake/predefine.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/CMakeLists.txt))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/cmake/CocosExternalConfig.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/ios/CMakeLists.txt))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/CMakeLists.txt))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/SocketRocket/CMakeLists.txt))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/boost.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/templates/cmake/apple.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/templates/cmake/common.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeASMInformation.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeCInformation.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeCXXInformation.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeCommonLanguageInclude.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeGenericSystem.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeInitializeConfigs.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeLanguageInformation.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeSystemSpecificInformation.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/CMakeSystemSpecificInitialize.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Compiler/AppleClang-C.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Compiler/AppleClang-CXX.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Compiler/CMakeCommonCompilerMacros.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Compiler/Clang-ASM.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Compiler/Clang.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Compiler/GNU.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Platform/Apple-AppleClang-C.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Platform/Apple-AppleClang-CXX.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Platform/Apple-Clang-ASM.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Platform/Apple-Clang-C.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Platform/Apple-Clang-CXX.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Platform/Apple-Clang.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Platform/Darwin-Initialize.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Platform/Darwin.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Platform/UnixPaths.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Platform/iOS-Initialize.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/share/cmake-3.24/Modules/Platform/iOS.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/CMakeFiles/3.24.3/CMakeASMCompiler.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/CMakeFiles/3.24.3/CMakeCCompiler.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/CMakeFiles/3.24.3/CMakeCXXCompiler.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/CMakeFiles/3.24.3/CMakeSystem.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/cfg.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/native/engine/common/CMakeLists.txt))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/native/engine/common/localCfg.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/native/engine/ios/CMakeLists.txt))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/native/engine/ios/Post-service.cmake))
TARGETS += $(subst $(space),$(spaceplus),$(wildcard /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/native/engine/ios/Pre-service.cmake))

/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/CMakeFiles/cmake.check_cache: $(TARGETS)
	/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/tools/cmake/bin/cmake -H/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/native/engine/ios -B/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj
