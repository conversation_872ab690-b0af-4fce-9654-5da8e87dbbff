// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXAggregateTarget section */
		95BE28626AE14F3D8701395B /* ZERO_CHECK */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = BB70A4D122614CC196E3E879 /* Build configuration list for PBXAggregateTarget "ZERO_CHECK" */;
			buildPhases = (
				8E1615DB12FAD784F78A8BB0 /* Generate CMakeFiles/ZERO_CHECK */,
			);
			dependencies = (
			);
			name = ZERO_CHECK;
			productName = ZERO_CHECK;
		};
		ED3539B8EA5B4D789A77D4BF /* ALL_BUILD */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 287E1F0F82AA436C8EAAFD90 /* Build configuration list for PBXAggregateTarget "ALL_BUILD" */;
			buildPhases = (
				DD95FDB8C654424B4F4C6AE8 /* Generate CMakeFiles/ALL_BUILD */,
			);
			dependencies = (
				2DFAF443F8EE4A3B9BCDD69B /* PBXTargetDependency */,
				C72C165712F04BCB9C4B8E61 /* PBXTargetDependency */,
			);
			name = ALL_BUILD;
			productName = ALL_BUILD;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		0BAAED3BB1D645178EB85A68 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */ = {isa = PBXBuildFile; fileRef = 1BD1215A8F554D30A08DFFC0 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */; };
		29CA3A113B43483490C62DCE /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */ = {isa = PBXBuildFile; fileRef = 58F8E12F52D4492EBF08636F /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */; };
		6230C2B63B6744A3B88FCF47 /* /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */ = {isa = PBXBuildFile; fileRef = 5B01DC4F54AB44319B9A2B10 /* /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */; };
		71334063616B40188008F2C9 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */ = {isa = PBXBuildFile; fileRef = CE468E4BE58241C3A2941507 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */; };
		9F57270101B64808A43823A0 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */ = {isa = PBXBuildFile; fileRef = 125D8A99F85D42BBAF827BAB /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */; };
		BD37BA60B7EF469F9E0C207C /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */ = {isa = PBXBuildFile; fileRef = 74F83EB3FF074D6D9A905E58 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */; };
		E3722F4E765744CDAD497B85 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */ = {isa = PBXBuildFile; fileRef = 3D2FC0B807F74B7396D940C9 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */; };
		E628605D3A4D4772AB9AE7CA /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */ = {isa = PBXBuildFile; fileRef = 00C0D4A9D3C149F891719A53 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */; };
		FA360692C4D9404EB195600A /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */ = {isa = PBXBuildFile; fileRef = BEF09D30241F470CBFF661F9 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */; };
/* End PBXBuildFile section */

/* Begin PBXBuildStyle section */
		350250DEC2A24B7FA53BAC16 /* Debug */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = Debug;
		};
		631FB557175D4192B6401F3C /* RelWithDebInfo */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = RelWithDebInfo;
		};
		9561D159202C49C98CB33719 /* MinSizeRel */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = MinSizeRel;
		};
		F4B436CADD6A45419CE9EE57 /* Release */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = Release;
		};
/* End PBXBuildStyle section */

/* Begin PBXContainerItemProxy section */
		4414BD256A9B465395880A10 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9C2DBDD6080A468BA4FFBD0C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 95BE28626AE14F3D8701395B;
			remoteInfo = ZERO_CHECK;
		};
		BF600633E737459AA633EFF3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9C2DBDD6080A468BA4FFBD0C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 95BE28626AE14F3D8701395B;
			remoteInfo = ZERO_CHECK;
		};
		E4E4FC504D0F4619AD781EF4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9C2DBDD6080A468BA4FFBD0C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B3539C7458184894A8F35189;
			remoteInfo = boost_container;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00C0D4A9D3C149F891719A53 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */ = {isa = PBXFileReference; explicitFileType = sourcecode.c.c; fileEncoding = 4; name = alloc_lib.c; path = alloc_lib.c; sourceTree = SOURCE_ROOT; };
		125D8A99F85D42BBAF827BAB /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = monotonic_buffer_resource.cpp; path = monotonic_buffer_resource.cpp; sourceTree = SOURCE_ROOT; };
		1BD1215A8F554D30A08DFFC0 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = global_resource.cpp; path = global_resource.cpp; sourceTree = SOURCE_ROOT; };
		3D2FC0B807F74B7396D940C9 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = unsynchronized_pool_resource.cpp; path = unsynchronized_pool_resource.cpp; sourceTree = SOURCE_ROOT; };
		58F8E12F52D4492EBF08636F /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */ = {isa = PBXFileReference; explicitFileType = sourcecode.text; fileEncoding = 4; name = CMakeLists.txt; path = CMakeLists.txt; sourceTree = SOURCE_ROOT; };
		5B01DC4F54AB44319B9A2B10 /* /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */ = {isa = PBXFileReference; explicitFileType = sourcecode.text.plist; fileEncoding = 4; name = Info.plist; path = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist; sourceTree = "<absolute>"; };
		74F83EB3FF074D6D9A905E58 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = dlmalloc.cpp; path = dlmalloc.cpp; sourceTree = SOURCE_ROOT; };
		BEF09D30241F470CBFF661F9 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = pool_resource.cpp; path = pool_resource.cpp; sourceTree = SOURCE_ROOT; };
		CE468E4BE58241C3A2941507 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = synchronized_pool_resource.cpp; path = synchronized_pool_resource.cpp; sourceTree = SOURCE_ROOT; };
		D93269D41F344C2EBF6C0E55 /* boost_container */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libboost_container.a; sourceTree = BUILT_PRODUCTS_DIR; };
		ECB89BC25FBB4926A17496BB /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */ = {isa = PBXFileReference; explicitFileType = sourcecode.text; fileEncoding = 4; name = CMakeLists.txt; path = CMakeLists.txt; sourceTree = SOURCE_ROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		7E10B4990D884A3C8D6252B9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		28B7287525D549FBA0692956 /* CMake Rules */ = {
			isa = PBXGroup;
			children = (
			);
			name = "CMake Rules";
			sourceTree = "<group>";
		};
		5D5A22718A224CABB4EC8D72 /* ALL_BUILD */ = {
			isa = PBXGroup;
			children = (
				28B7287525D549FBA0692956 /* CMake Rules */,
				ECB89BC25FBB4926A17496BB /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */,
			);
			name = ALL_BUILD;
			sourceTree = "<group>";
		};
		5E250A5A0B93417DB9A40B94 /* Products */ = {
			isa = PBXGroup;
			children = (
				D93269D41F344C2EBF6C0E55 /* boost_container */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		766553FAD12C4CA493CE4C8B /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		86D1E940D5F64343A61C364C /* Resources */ = {
			isa = PBXGroup;
			children = (
				5B01DC4F54AB44319B9A2B10 /* /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		A49789BC746F4F09B2A7DC83 /* Source Files */ = {
			isa = PBXGroup;
			children = (
				00C0D4A9D3C149F891719A53 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */,
				74F83EB3FF074D6D9A905E58 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */,
				1BD1215A8F554D30A08DFFC0 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */,
				125D8A99F85D42BBAF827BAB /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */,
				BEF09D30241F470CBFF661F9 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */,
				CE468E4BE58241C3A2941507 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */,
				3D2FC0B807F74B7396D940C9 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */,
			);
			name = "Source Files";
			sourceTree = "<group>";
		};
		BB4AD3ADA7CA471A9096D214 /* Utils */ = {
			isa = PBXGroup;
			children = (
				D5FC881D519B40A5829B6A0C /* boost_container */,
			);
			name = Utils;
			sourceTree = "<group>";
		};
		D5FC881D519B40A5829B6A0C /* boost_container */ = {
			isa = PBXGroup;
			children = (
				A49789BC746F4F09B2A7DC83 /* Source Files */,
				86D1E940D5F64343A61C364C /* Resources */,
				58F8E12F52D4492EBF08636F /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */,
			);
			name = boost_container;
			sourceTree = "<group>";
		};
		E11225868F48440AA3E0A350 = {
			isa = PBXGroup;
			children = (
				BB4AD3ADA7CA471A9096D214 /* Utils */,
				5D5A22718A224CABB4EC8D72 /* ALL_BUILD */,
				5E250A5A0B93417DB9A40B94 /* Products */,
				766553FAD12C4CA493CE4C8B /* Frameworks */,
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		B3539C7458184894A8F35189 /* boost_container */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F4B2A8F51FF24E0280D48A30 /* Build configuration list for PBXNativeTarget "boost_container" */;
			buildPhases = (
				6103FF56475448828DF48B0C /* Sources */,
				7E10B4990D884A3C8D6252B9 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				94FB7DAA937443EABCDD4E3A /* PBXTargetDependency */,
			);
			name = boost_container;
			productName = boost_container;
			productReference = D93269D41F344C2EBF6C0E55 /* boost_container */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		9C2DBDD6080A468BA4FFBD0C /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1540;
			};
			buildConfigurationList = 85F38A8012B7456BA72034E0 /* Build configuration list for PBXProject "boost_container" */;
			buildSettings = {
			};
			buildStyles = (
				350250DEC2A24B7FA53BAC16 /* Debug */,
				F4B436CADD6A45419CE9EE57 /* Release */,
				9561D159202C49C98CB33719 /* MinSizeRel */,
				631FB557175D4192B6401F3C /* RelWithDebInfo */,
			);
			compatibilityVersion = "Xcode 3.2";
			hasScannedForEncodings = 0;
			mainGroup = E11225868F48440AA3E0A350;
			projectDirPath = "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container";
			projectRoot = "";
			targets = (
				ED3539B8EA5B4D789A77D4BF /* ALL_BUILD */,
				95BE28626AE14F3D8701395B /* ZERO_CHECK */,
				B3539C7458184894A8F35189 /* boost_container */,
			);
		};
/* End PBXProject section */

/* Begin PBXShellScriptBuildPhase section */
		3C0110FC8322DE7E50886E21 = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# shell script goes here
exit 0";
			showEnvVarsInLog = 0;
		};
		8E1615DB12FAD784F78A8BB0 /* Generate CMakeFiles/ZERO_CHECK */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Generate CMakeFiles/ZERO_CHECK";
			outputPaths = (
/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/ZERO_CHECK			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e
if test \"$CONFIGURATION\" = \"Debug\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
if test \"$CONFIGURATION\" = \"Release\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
if test \"$CONFIGURATION\" = \"MinSizeRel\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
if test \"$CONFIGURATION\" = \"RelWithDebInfo\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
";
			showEnvVarsInLog = 0;
		};
		9B50530E35CC49C82C2FACCF = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# shell script goes here
exit 0";
			showEnvVarsInLog = 0;
		};
		DD95FDB8C654424B4F4C6AE8 /* Generate CMakeFiles/ALL_BUILD */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Generate CMakeFiles/ALL_BUILD";
			outputPaths = (
/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/ALL_BUILD			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e
if test \"$CONFIGURATION\" = \"Debug\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
if test \"$CONFIGURATION\" = \"Release\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
if test \"$CONFIGURATION\" = \"MinSizeRel\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
if test \"$CONFIGURATION\" = \"RelWithDebInfo\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		6103FF56475448828DF48B0C /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E628605D3A4D4772AB9AE7CA /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */,
				BD37BA60B7EF469F9E0C207C /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */,
				0BAAED3BB1D645178EB85A68 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */,
				9F57270101B64808A43823A0 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */,
				FA360692C4D9404EB195600A /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */,
				71334063616B40188008F2C9 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */,
				E3722F4E765744CDAD497B85 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		2DFAF443F8EE4A3B9BCDD69B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B3539C7458184894A8F35189 /* boost_container */;
			targetProxy = E4E4FC504D0F4619AD781EF4 /* PBXContainerItemProxy */;
		};
		94FB7DAA937443EABCDD4E3A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 95BE28626AE14F3D8701395B /* ZERO_CHECK */;
			targetProxy = BF600633E737459AA633EFF3 /* PBXContainerItemProxy */;
		};
		C72C165712F04BCB9C4B8E61 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 95BE28626AE14F3D8701395B /* ZERO_CHECK */;
			targetProxy = 4414BD256A9B465395880A10 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		18F2767DA72A4A2C87C448F8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Release;
		};
		1C30398235C84E3DA45B99BC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Debug;
		};
		1D365DDA6A2A4EC09604EA64 /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/archives/RelWithDebInfo;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 2;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("       -DNDEBUG '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "       -DNDEBUG '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = RelWithDebInfo;
		};
		44247EB200A5438EA0FF60C7 /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/build;
			};
			name = RelWithDebInfo;
		};
		57BBCA0A93B34389A07C5E20 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/archives/Debug;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("   '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "   '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Debug;
		};
		8DB6DA6804EB4706923AD1D6 /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/build;
			};
			name = MinSizeRel;
		};
		958EDC13CCC04D568362F141 /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/archives/MinSizeRel;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = s;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("    -DNDEBUG '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "    -DNDEBUG '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = MinSizeRel;
		};
		96D1F5C9572547C9ACAD11A4 /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = RelWithDebInfo;
		};
		988E702C12174AAC82F25C8E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Debug;
		};
		A6CA6B3B880F4937A15528B7 /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = MinSizeRel;
		};
		BAE5FCAA12344E08A6DD36C1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/build;
			};
			name = Debug;
		};
		D355BBD9AAB74E1E83EAB895 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/build;
			};
			name = Release;
		};
		F66F8FA92FE84E5C8A262B4F /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = MinSizeRel;
		};
		FCBF88BEC58448DCB1FAA544 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Release;
		};
		FD334DB57F984E32B2EAE123 /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = RelWithDebInfo;
		};
		FD382112E9384F13B7DF98C8 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/archives/Release;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("    -DNDEBUG '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "    -DNDEBUG '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		287E1F0F82AA436C8EAAFD90 /* Build configuration list for PBXAggregateTarget "ALL_BUILD" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1C30398235C84E3DA45B99BC /* Debug */,
				FCBF88BEC58448DCB1FAA544 /* Release */,
				F66F8FA92FE84E5C8A262B4F /* MinSizeRel */,
				96D1F5C9572547C9ACAD11A4 /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		85F38A8012B7456BA72034E0 /* Build configuration list for PBXProject "boost_container" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BAE5FCAA12344E08A6DD36C1 /* Debug */,
				D355BBD9AAB74E1E83EAB895 /* Release */,
				8DB6DA6804EB4706923AD1D6 /* MinSizeRel */,
				44247EB200A5438EA0FF60C7 /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		BB70A4D122614CC196E3E879 /* Build configuration list for PBXAggregateTarget "ZERO_CHECK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				988E702C12174AAC82F25C8E /* Debug */,
				18F2767DA72A4A2C87C448F8 /* Release */,
				A6CA6B3B880F4937A15528B7 /* MinSizeRel */,
				FD334DB57F984E32B2EAE123 /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		F4B2A8F51FF24E0280D48A30 /* Build configuration list for PBXNativeTarget "boost_container" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				57BBCA0A93B34389A07C5E20 /* Debug */,
				FD382112E9384F13B7DF98C8 /* Release */,
				958EDC13CCC04D568362F141 /* MinSizeRel */,
				1D365DDA6A2A4EC09604EA64 /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 9C2DBDD6080A468BA4FFBD0C /* Project object */;
}
