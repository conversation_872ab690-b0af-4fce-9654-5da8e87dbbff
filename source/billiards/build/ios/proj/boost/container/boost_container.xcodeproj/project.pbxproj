// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXAggregateTarget section */
		95BE28626AE14F3D8701395B /* ZERO_CHECK */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 5B2A1C0E85D8405C91AEEBF4 /* Build configuration list for PBXAggregateTarget "ZERO_CHECK" */;
			buildPhases = (
				8E1615DB12FAD784F78A8BB0 /* Generate CMakeFiles/ZERO_CHECK */,
			);
			dependencies = (
			);
			name = ZERO_CHECK;
			productName = ZERO_CHECK;
		};
		ED3539B8EA5B4D789A77D4BF /* ALL_BUILD */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 8CEB7E99E6D740199F43F6C6 /* Build configuration list for PBXAggregateTarget "ALL_BUILD" */;
			buildPhases = (
				DD95FDB8C654424B4F4C6AE8 /* Generate CMakeFiles/ALL_BUILD */,
			);
			dependencies = (
				519310E5695E48C9817C73F7 /* PBXTargetDependency */,
				33EDD5B9B2774D05BBE999CA /* PBXTargetDependency */,
			);
			name = ALL_BUILD;
			productName = ALL_BUILD;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		211737E6B9954E0A8CBEF96E /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */ = {isa = PBXBuildFile; fileRef = BD0D143EDD5B450A89132161 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */; };
		2544B14119BE4AC280820F57 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */ = {isa = PBXBuildFile; fileRef = 5DD1A3F64CD749DEAA8B1BA2 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */; };
		505C4AF294484C4380CF1561 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */ = {isa = PBXBuildFile; fileRef = 69A7AD1FA54943889BB1F6B7 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */; };
		810B0D2A1BE44E9BBA39C280 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */ = {isa = PBXBuildFile; fileRef = 97D2A90C5CF441D6B12BA947 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */; };
		81C6D6D86754446B8D79F102 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */ = {isa = PBXBuildFile; fileRef = C9B47F57F1CE4114A6D6F125 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */; };
		91975A6D13494D44ABB22943 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */ = {isa = PBXBuildFile; fileRef = D42378ED3161411DA45A3CD6 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */; };
		9C74406077584E348C04718A /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */ = {isa = PBXBuildFile; fileRef = C5D3CBC300D74539A2A88F78 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */; };
		CA02E42860EF433EBD6F5C97 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */ = {isa = PBXBuildFile; fileRef = 4BA6C6FFE6C7419B8F684E51 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */; };
		E714349BBF974B7BB07E2C6D /* /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */ = {isa = PBXBuildFile; fileRef = B81E5BBB3807430388BDCAF2 /* /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */; };
/* End PBXBuildFile section */

/* Begin PBXBuildStyle section */
		194DE9D33EC84BB2A9D5E3A0 /* Release */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = Release;
		};
		41CEE4F4D8C54190A4CC8EF1 /* Debug */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = Debug;
		};
		5D6D1E57FBD648AFB847AEF0 /* RelWithDebInfo */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = RelWithDebInfo;
		};
		F182BB63F2634E03AFE4FBC3 /* MinSizeRel */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = MinSizeRel;
		};
/* End PBXBuildStyle section */

/* Begin PBXContainerItemProxy section */
		3EB6E4322D8145A2BACD0A18 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9C2DBDD6080A468BA4FFBD0C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 95BE28626AE14F3D8701395B;
			remoteInfo = ZERO_CHECK;
		};
		8C82D1539180471B8F337654 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9C2DBDD6080A468BA4FFBD0C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 95BE28626AE14F3D8701395B;
			remoteInfo = ZERO_CHECK;
		};
		CB912ED8C46F4DA6849737B3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9C2DBDD6080A468BA4FFBD0C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B3539C7458184894A8F35189;
			remoteInfo = boost_container;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		4BA6C6FFE6C7419B8F684E51 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = pool_resource.cpp; path = pool_resource.cpp; sourceTree = SOURCE_ROOT; };
		5DD1A3F64CD749DEAA8B1BA2 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = unsynchronized_pool_resource.cpp; path = unsynchronized_pool_resource.cpp; sourceTree = SOURCE_ROOT; };
		69A7AD1FA54943889BB1F6B7 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */ = {isa = PBXFileReference; explicitFileType = sourcecode.text; fileEncoding = 4; name = CMakeLists.txt; path = CMakeLists.txt; sourceTree = SOURCE_ROOT; };
		97D2A90C5CF441D6B12BA947 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = synchronized_pool_resource.cpp; path = synchronized_pool_resource.cpp; sourceTree = SOURCE_ROOT; };
		B81E5BBB3807430388BDCAF2 /* /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */ = {isa = PBXFileReference; explicitFileType = sourcecode.text.plist; fileEncoding = 4; name = Info.plist; path = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist; sourceTree = "<absolute>"; };
		BD0D143EDD5B450A89132161 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */ = {isa = PBXFileReference; explicitFileType = sourcecode.c.c; fileEncoding = 4; name = alloc_lib.c; path = alloc_lib.c; sourceTree = SOURCE_ROOT; };
		C5D3CBC300D74539A2A88F78 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = global_resource.cpp; path = global_resource.cpp; sourceTree = SOURCE_ROOT; };
		C9B47F57F1CE4114A6D6F125 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = monotonic_buffer_resource.cpp; path = monotonic_buffer_resource.cpp; sourceTree = SOURCE_ROOT; };
		D42378ED3161411DA45A3CD6 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = dlmalloc.cpp; path = dlmalloc.cpp; sourceTree = SOURCE_ROOT; };
		FF3330856DCA4F1A833C1C70 /* boost_container */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libboost_container.a; sourceTree = BUILT_PRODUCTS_DIR; };
		FFCE35EF593F400CB02F1C92 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */ = {isa = PBXFileReference; explicitFileType = sourcecode.text; fileEncoding = 4; name = CMakeLists.txt; path = CMakeLists.txt; sourceTree = SOURCE_ROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		397B4F58DB72417FA63F4F17 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		23DEFA84F2DF43CF8241FA23 /* Utils */ = {
			isa = PBXGroup;
			children = (
				4C6BB4C0BD6248708B7B028A /* boost_container */,
			);
			name = Utils;
			sourceTree = "<group>";
		};
		4C6BB4C0BD6248708B7B028A /* boost_container */ = {
			isa = PBXGroup;
			children = (
				5A02E7AE48AF4767AB9A1E0B /* Source Files */,
				A7DB8609BD384A7BBF968268 /* Resources */,
				69A7AD1FA54943889BB1F6B7 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */,
			);
			name = boost_container;
			sourceTree = "<group>";
		};
		5A02E7AE48AF4767AB9A1E0B /* Source Files */ = {
			isa = PBXGroup;
			children = (
				BD0D143EDD5B450A89132161 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */,
				D42378ED3161411DA45A3CD6 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */,
				C5D3CBC300D74539A2A88F78 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */,
				C9B47F57F1CE4114A6D6F125 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */,
				4BA6C6FFE6C7419B8F684E51 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */,
				97D2A90C5CF441D6B12BA947 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */,
				5DD1A3F64CD749DEAA8B1BA2 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */,
			);
			name = "Source Files";
			sourceTree = "<group>";
		};
		A7DB8609BD384A7BBF968268 /* Resources */ = {
			isa = PBXGroup;
			children = (
				B81E5BBB3807430388BDCAF2 /* /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		AD217F027A7242B5BD01AB39 = {
			isa = PBXGroup;
			children = (
				23DEFA84F2DF43CF8241FA23 /* Utils */,
				B758E57DB870476184C7D377 /* ALL_BUILD */,
				DF598E689C72468F996CAC93 /* Products */,
				C8A0FA39DF87426AB24858A1 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		B75234C72A144718819051FA /* CMake Rules */ = {
			isa = PBXGroup;
			children = (
			);
			name = "CMake Rules";
			sourceTree = "<group>";
		};
		B758E57DB870476184C7D377 /* ALL_BUILD */ = {
			isa = PBXGroup;
			children = (
				B75234C72A144718819051FA /* CMake Rules */,
				FFCE35EF593F400CB02F1C92 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */,
			);
			name = ALL_BUILD;
			sourceTree = "<group>";
		};
		C8A0FA39DF87426AB24858A1 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		DF598E689C72468F996CAC93 /* Products */ = {
			isa = PBXGroup;
			children = (
				FF3330856DCA4F1A833C1C70 /* boost_container */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		B3539C7458184894A8F35189 /* boost_container */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9ADA9E7E6A764017934895F3 /* Build configuration list for PBXNativeTarget "boost_container" */;
			buildPhases = (
				F70FDC6ED0EB4EDB874C7FC6 /* Sources */,
				397B4F58DB72417FA63F4F17 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				3534458B34D647EBB3039721 /* PBXTargetDependency */,
			);
			name = boost_container;
			productName = boost_container;
			productReference = FF3330856DCA4F1A833C1C70 /* boost_container */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		9C2DBDD6080A468BA4FFBD0C /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1540;
			};
			buildConfigurationList = A41C05FD01F84BEA95258A32 /* Build configuration list for PBXProject "boost_container" */;
			buildSettings = {
			};
			buildStyles = (
				41CEE4F4D8C54190A4CC8EF1 /* Debug */,
				194DE9D33EC84BB2A9D5E3A0 /* Release */,
				F182BB63F2634E03AFE4FBC3 /* MinSizeRel */,
				5D6D1E57FBD648AFB847AEF0 /* RelWithDebInfo */,
			);
			compatibilityVersion = "Xcode 3.2";
			hasScannedForEncodings = 0;
			mainGroup = AD217F027A7242B5BD01AB39;
			projectDirPath = "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container";
			projectRoot = "";
			targets = (
				ED3539B8EA5B4D789A77D4BF /* ALL_BUILD */,
				95BE28626AE14F3D8701395B /* ZERO_CHECK */,
				B3539C7458184894A8F35189 /* boost_container */,
			);
		};
/* End PBXProject section */

/* Begin PBXShellScriptBuildPhase section */
		3C0110FC8322DE7E50886E21 = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# shell script goes here
exit 0";
			showEnvVarsInLog = 0;
		};
		8E1615DB12FAD784F78A8BB0 /* Generate CMakeFiles/ZERO_CHECK */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Generate CMakeFiles/ZERO_CHECK";
			outputPaths = (
/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/ZERO_CHECK			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e
if test \"$CONFIGURATION\" = \"Debug\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
if test \"$CONFIGURATION\" = \"Release\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
if test \"$CONFIGURATION\" = \"MinSizeRel\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
if test \"$CONFIGURATION\" = \"RelWithDebInfo\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
";
			showEnvVarsInLog = 0;
		};
		9B50530E35CC49C82C2FACCF = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# shell script goes here
exit 0";
			showEnvVarsInLog = 0;
		};
		DD95FDB8C654424B4F4C6AE8 /* Generate CMakeFiles/ALL_BUILD */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Generate CMakeFiles/ALL_BUILD";
			outputPaths = (
/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/ALL_BUILD			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e
if test \"$CONFIGURATION\" = \"Debug\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
if test \"$CONFIGURATION\" = \"Release\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
if test \"$CONFIGURATION\" = \"MinSizeRel\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
if test \"$CONFIGURATION\" = \"RelWithDebInfo\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		F70FDC6ED0EB4EDB874C7FC6 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				211737E6B9954E0A8CBEF96E /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */,
				91975A6D13494D44ABB22943 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */,
				9C74406077584E348C04718A /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */,
				81C6D6D86754446B8D79F102 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */,
				CA02E42860EF433EBD6F5C97 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */,
				810B0D2A1BE44E9BBA39C280 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */,
				2544B14119BE4AC280820F57 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		33EDD5B9B2774D05BBE999CA /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B3539C7458184894A8F35189 /* boost_container */;
			targetProxy = CB912ED8C46F4DA6849737B3 /* PBXContainerItemProxy */;
		};
		3534458B34D647EBB3039721 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 95BE28626AE14F3D8701395B /* ZERO_CHECK */;
			targetProxy = 3EB6E4322D8145A2BACD0A18 /* PBXContainerItemProxy */;
		};
		519310E5695E48C9817C73F7 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 95BE28626AE14F3D8701395B /* ZERO_CHECK */;
			targetProxy = 8C82D1539180471B8F337654 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		0A28F81B229045DDBE49270D /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = MinSizeRel;
		};
		121B4F1B9D3B47D2AE21EDDF /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = RelWithDebInfo;
		};
		296F35D4F64B4C20A70CC584 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/archives/Release;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("    -DNDEBUG '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "    -DNDEBUG '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Release;
		};
		31450630857B448FB3A60172 /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/build;
			};
			name = RelWithDebInfo;
		};
		489FF7F8DA934BBEA6404230 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/build;
			};
			name = Release;
		};
		4C6AC741F55847E893D1EDC5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Debug;
		};
		61A661D47ADF42F8A7F92261 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Debug;
		};
		63882375C17B47E391FFACE3 /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/build;
			};
			name = MinSizeRel;
		};
		6D52F59310B64BDAB8B7297B /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = RelWithDebInfo;
		};
		8E9047DF6CB74697A1C8A34B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Release;
		};
		905B33C8541F494EA3CAF502 /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = MinSizeRel;
		};
		A303882D85D94935BB13BFA9 /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/archives/MinSizeRel;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = s;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("    -DNDEBUG '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "    -DNDEBUG '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = MinSizeRel;
		};
		A9133A957951412EA99176A3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/archives/Debug;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("   '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "   '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Debug;
		};
		C55CBAF474FC4A83ABB4A1B9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Release;
		};
		F1ED8A14B7D24A0CB8721BC4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/build;
			};
			name = Debug;
		};
		F40DAAD634BE40048AD45F95 /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/archives/RelWithDebInfo;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 2;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("       -DNDEBUG '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "       -DNDEBUG '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = RelWithDebInfo;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		5B2A1C0E85D8405C91AEEBF4 /* Build configuration list for PBXAggregateTarget "ZERO_CHECK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				61A661D47ADF42F8A7F92261 /* Debug */,
				C55CBAF474FC4A83ABB4A1B9 /* Release */,
				0A28F81B229045DDBE49270D /* MinSizeRel */,
				6D52F59310B64BDAB8B7297B /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		8CEB7E99E6D740199F43F6C6 /* Build configuration list for PBXAggregateTarget "ALL_BUILD" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4C6AC741F55847E893D1EDC5 /* Debug */,
				8E9047DF6CB74697A1C8A34B /* Release */,
				905B33C8541F494EA3CAF502 /* MinSizeRel */,
				121B4F1B9D3B47D2AE21EDDF /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		9ADA9E7E6A764017934895F3 /* Build configuration list for PBXNativeTarget "boost_container" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A9133A957951412EA99176A3 /* Debug */,
				296F35D4F64B4C20A70CC584 /* Release */,
				A303882D85D94935BB13BFA9 /* MinSizeRel */,
				F40DAAD634BE40048AD45F95 /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		A41C05FD01F84BEA95258A32 /* Build configuration list for PBXProject "boost_container" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F1ED8A14B7D24A0CB8721BC4 /* Debug */,
				489FF7F8DA934BBEA6404230 /* Release */,
				63882375C17B47E391FFACE3 /* MinSizeRel */,
				31450630857B448FB3A60172 /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 9C2DBDD6080A468BA4FFBD0C /* Project object */;
}
