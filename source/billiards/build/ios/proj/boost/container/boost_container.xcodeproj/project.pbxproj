// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXAggregateTarget section */
		95BE28626AE14F3D8701395B /* ZERO_CHECK */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = B2A6495486B24B8991078E77 /* Build configuration list for PBXAggregateTarget "ZERO_CHECK" */;
			buildPhases = (
				8E1615DB12FAD784F78A8BB0 /* Generate CMakeFiles/ZERO_CHECK */,
			);
			dependencies = (
			);
			name = ZERO_CHECK;
			productName = ZERO_CHECK;
		};
		ED3539B8EA5B4D789A77D4BF /* ALL_BUILD */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = CA0F84C0103E4A5E973A4162 /* Build configuration list for PBXAggregateTarget "ALL_BUILD" */;
			buildPhases = (
				DD95FDB8C654424B4F4C6AE8 /* Generate CMakeFiles/ALL_BUILD */,
			);
			dependencies = (
				5882CA3BE2D94571B8A51848 /* PBXTargetDependency */,
				286EDC5BEF2C442289E1D489 /* PBXTargetDependency */,
			);
			name = ALL_BUILD;
			productName = ALL_BUILD;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		12FA59F3FB334186A6E5EBB7 /* /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */ = {isa = PBXBuildFile; fileRef = 322860C315E64C7C993179E6 /* /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */; };
		287FCD3DE9264B0AB9F00C9B /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */ = {isa = PBXBuildFile; fileRef = C733C583753B4F499EDC4170 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */; };
		4689924184FB48B78518B251 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */ = {isa = PBXBuildFile; fileRef = A31A4873F112450E92A174A2 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */; };
		53E6223846D14CA7A9277D2C /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */ = {isa = PBXBuildFile; fileRef = 70EDEC7E56444657B90F69B1 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */; };
		7B76E5C241B94558A0DB9F41 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */ = {isa = PBXBuildFile; fileRef = C673CBD18F504F0C9D44AAFB /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */; };
		CF3EE79B869E4569B2E13035 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */ = {isa = PBXBuildFile; fileRef = 93999A15173B43148DF23FC7 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */; };
		D37D34209B864A61983DAC75 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */ = {isa = PBXBuildFile; fileRef = D6F096AA56FE4037AD8E4999 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */; };
		F0BE235BAE564DF193F2B1D1 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */ = {isa = PBXBuildFile; fileRef = 2E7A3D0D2E034201A907707C /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */; };
		F3A3E707DE5948829A1C8C1E /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */ = {isa = PBXBuildFile; fileRef = D1E6C2B791F24032BE190DEE /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */; };
/* End PBXBuildFile section */

/* Begin PBXBuildStyle section */
		ACFFADCC230B40C8B6395058 /* RelWithDebInfo */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = RelWithDebInfo;
		};
		B28D444BA3434F78962F4475 /* Release */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = Release;
		};
		B587B36C07514E419AFCE37C /* Debug */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = Debug;
		};
		F31268CD1F1645548F30C554 /* MinSizeRel */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = MinSizeRel;
		};
/* End PBXBuildStyle section */

/* Begin PBXContainerItemProxy section */
		AE3BC90F169B4BDE9ECF8B1C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9C2DBDD6080A468BA4FFBD0C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B3539C7458184894A8F35189;
			remoteInfo = boost_container;
		};
		BA562BD3688547B997F84B17 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9C2DBDD6080A468BA4FFBD0C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 95BE28626AE14F3D8701395B;
			remoteInfo = ZERO_CHECK;
		};
		D277BBEED9F540E2BE1C5446 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9C2DBDD6080A468BA4FFBD0C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 95BE28626AE14F3D8701395B;
			remoteInfo = ZERO_CHECK;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		2E7A3D0D2E034201A907707C /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = monotonic_buffer_resource.cpp; path = monotonic_buffer_resource.cpp; sourceTree = SOURCE_ROOT; };
		322860C315E64C7C993179E6 /* /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */ = {isa = PBXFileReference; explicitFileType = sourcecode.text.plist; fileEncoding = 4; name = Info.plist; path = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist; sourceTree = "<absolute>"; };
		70EDEC7E56444657B90F69B1 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = global_resource.cpp; path = global_resource.cpp; sourceTree = SOURCE_ROOT; };
		93999A15173B43148DF23FC7 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = unsynchronized_pool_resource.cpp; path = unsynchronized_pool_resource.cpp; sourceTree = SOURCE_ROOT; };
		A31A4873F112450E92A174A2 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = pool_resource.cpp; path = pool_resource.cpp; sourceTree = SOURCE_ROOT; };
		A5642C5FB4F44339B6B4DF7F /* boost_container */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libboost_container.a; sourceTree = BUILT_PRODUCTS_DIR; };
		AF413C47C8CB429EB2828DD3 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */ = {isa = PBXFileReference; explicitFileType = sourcecode.text; fileEncoding = 4; name = CMakeLists.txt; path = CMakeLists.txt; sourceTree = SOURCE_ROOT; };
		C673CBD18F504F0C9D44AAFB /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = dlmalloc.cpp; path = dlmalloc.cpp; sourceTree = SOURCE_ROOT; };
		C733C583753B4F499EDC4170 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = synchronized_pool_resource.cpp; path = synchronized_pool_resource.cpp; sourceTree = SOURCE_ROOT; };
		D1E6C2B791F24032BE190DEE /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */ = {isa = PBXFileReference; explicitFileType = sourcecode.c.c; fileEncoding = 4; name = alloc_lib.c; path = alloc_lib.c; sourceTree = SOURCE_ROOT; };
		D6F096AA56FE4037AD8E4999 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */ = {isa = PBXFileReference; explicitFileType = sourcecode.text; fileEncoding = 4; name = CMakeLists.txt; path = CMakeLists.txt; sourceTree = SOURCE_ROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		C2DB5EE2E47546E3A07266F5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		373721FF47DB4CA09FE3CA83 = {
			isa = PBXGroup;
			children = (
				9E0AFD288FDB4D4D94CD34C9 /* Utils */,
				3A0F5A4AC6544F6EAA3E1992 /* ALL_BUILD */,
				49CDE34977444630BC4BF4F2 /* Products */,
				91D8F7ED98384027AB0DB700 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		3A0F5A4AC6544F6EAA3E1992 /* ALL_BUILD */ = {
			isa = PBXGroup;
			children = (
				AA750FAFD4094DC3BEFDDFBA /* CMake Rules */,
				AF413C47C8CB429EB2828DD3 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */,
			);
			name = ALL_BUILD;
			sourceTree = "<group>";
		};
		49CDE34977444630BC4BF4F2 /* Products */ = {
			isa = PBXGroup;
			children = (
				A5642C5FB4F44339B6B4DF7F /* boost_container */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		66341BD40FC44EF99F6086C1 /* Resources */ = {
			isa = PBXGroup;
			children = (
				322860C315E64C7C993179E6 /* /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		91D8F7ED98384027AB0DB700 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		9E0AFD288FDB4D4D94CD34C9 /* Utils */ = {
			isa = PBXGroup;
			children = (
				A39FDBFC5E464001BDC2B924 /* boost_container */,
			);
			name = Utils;
			sourceTree = "<group>";
		};
		A39FDBFC5E464001BDC2B924 /* boost_container */ = {
			isa = PBXGroup;
			children = (
				FB5575D6920D4898AC891E27 /* Source Files */,
				66341BD40FC44EF99F6086C1 /* Resources */,
				D6F096AA56FE4037AD8E4999 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */,
			);
			name = boost_container;
			sourceTree = "<group>";
		};
		AA750FAFD4094DC3BEFDDFBA /* CMake Rules */ = {
			isa = PBXGroup;
			children = (
			);
			name = "CMake Rules";
			sourceTree = "<group>";
		};
		FB5575D6920D4898AC891E27 /* Source Files */ = {
			isa = PBXGroup;
			children = (
				D1E6C2B791F24032BE190DEE /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */,
				C673CBD18F504F0C9D44AAFB /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */,
				70EDEC7E56444657B90F69B1 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */,
				2E7A3D0D2E034201A907707C /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */,
				A31A4873F112450E92A174A2 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */,
				C733C583753B4F499EDC4170 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */,
				93999A15173B43148DF23FC7 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */,
			);
			name = "Source Files";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		B3539C7458184894A8F35189 /* boost_container */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E63A43C7460D45E581FFCD31 /* Build configuration list for PBXNativeTarget "boost_container" */;
			buildPhases = (
				46C7DAC3AB544E8DA62A7DB8 /* Sources */,
				C2DB5EE2E47546E3A07266F5 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				922EDFB715D94610B110ECA1 /* PBXTargetDependency */,
			);
			name = boost_container;
			productName = boost_container;
			productReference = A5642C5FB4F44339B6B4DF7F /* boost_container */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		9C2DBDD6080A468BA4FFBD0C /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1540;
			};
			buildConfigurationList = 7E2474BFB14D4DB5A370531F /* Build configuration list for PBXProject "boost_container" */;
			buildSettings = {
			};
			buildStyles = (
				B587B36C07514E419AFCE37C /* Debug */,
				B28D444BA3434F78962F4475 /* Release */,
				F31268CD1F1645548F30C554 /* MinSizeRel */,
				ACFFADCC230B40C8B6395058 /* RelWithDebInfo */,
			);
			compatibilityVersion = "Xcode 3.2";
			hasScannedForEncodings = 0;
			mainGroup = 373721FF47DB4CA09FE3CA83;
			projectDirPath = "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container";
			projectRoot = "";
			targets = (
				ED3539B8EA5B4D789A77D4BF /* ALL_BUILD */,
				95BE28626AE14F3D8701395B /* ZERO_CHECK */,
				B3539C7458184894A8F35189 /* boost_container */,
			);
		};
/* End PBXProject section */

/* Begin PBXShellScriptBuildPhase section */
		3C0110FC8322DE7E50886E21 = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# shell script goes here
exit 0";
			showEnvVarsInLog = 0;
		};
		8E1615DB12FAD784F78A8BB0 /* Generate CMakeFiles/ZERO_CHECK */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Generate CMakeFiles/ZERO_CHECK";
			outputPaths = (
/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/ZERO_CHECK			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e
if test \"$CONFIGURATION\" = \"Debug\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
if test \"$CONFIGURATION\" = \"Release\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
if test \"$CONFIGURATION\" = \"MinSizeRel\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
if test \"$CONFIGURATION\" = \"RelWithDebInfo\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
";
			showEnvVarsInLog = 0;
		};
		9B50530E35CC49C82C2FACCF = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# shell script goes here
exit 0";
			showEnvVarsInLog = 0;
		};
		DD95FDB8C654424B4F4C6AE8 /* Generate CMakeFiles/ALL_BUILD */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Generate CMakeFiles/ALL_BUILD";
			outputPaths = (
/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/ALL_BUILD			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e
if test \"$CONFIGURATION\" = \"Debug\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
if test \"$CONFIGURATION\" = \"Release\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
if test \"$CONFIGURATION\" = \"MinSizeRel\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
if test \"$CONFIGURATION\" = \"RelWithDebInfo\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		46C7DAC3AB544E8DA62A7DB8 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F3A3E707DE5948829A1C8C1E /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */,
				7B76E5C241B94558A0DB9F41 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */,
				53E6223846D14CA7A9277D2C /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */,
				F0BE235BAE564DF193F2B1D1 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */,
				4689924184FB48B78518B251 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */,
				287FCD3DE9264B0AB9F00C9B /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */,
				CF3EE79B869E4569B2E13035 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		286EDC5BEF2C442289E1D489 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 95BE28626AE14F3D8701395B /* ZERO_CHECK */;
			targetProxy = BA562BD3688547B997F84B17 /* PBXContainerItemProxy */;
		};
		5882CA3BE2D94571B8A51848 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B3539C7458184894A8F35189 /* boost_container */;
			targetProxy = AE3BC90F169B4BDE9ECF8B1C /* PBXContainerItemProxy */;
		};
		922EDFB715D94610B110ECA1 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 95BE28626AE14F3D8701395B /* ZERO_CHECK */;
			targetProxy = D277BBEED9F540E2BE1C5446 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		04D8ABC7D34842BE849B1778 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Debug;
		};
		093453FC7D994DF89F87CF32 /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/build;
			};
			name = MinSizeRel;
		};
		156CD7E8802B48CD9152335E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/archives/Release;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("    -DNDEBUG '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "    -DNDEBUG '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Release;
		};
		3229EA39436D4701B566D8FE /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/build;
			};
			name = RelWithDebInfo;
		};
		3DA70A73B56049BEA025779E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Debug;
		};
		610D0FA8AE4D4EDCB9E90E5B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/build;
			};
			name = Debug;
		};
		697CFB9A8E234F32AA393DC5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/build;
			};
			name = Release;
		};
		8B048430FB6D41C7A78FE92E /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = RelWithDebInfo;
		};
		8EDDEB2D481046C7B7A1E7E9 /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = MinSizeRel;
		};
		A56C5E6169584948BC27EBA6 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Release;
		};
		C8D8F0531DCB46399E52E140 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/archives/Debug;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("   '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "   '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Debug;
		};
		CB7A9C8740A04D638E8B39FE /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/archives/RelWithDebInfo;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 2;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("       -DNDEBUG '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "       -DNDEBUG '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = RelWithDebInfo;
		};
		D31DE72ACB044783AF617868 /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = MinSizeRel;
		};
		EAE06D9AAEE54A539627C69C /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = RelWithDebInfo;
		};
		F466A3703A9F4E66BFE3D298 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Release;
		};
		F7492C5F3B0E4B008153580A /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/archives/MinSizeRel;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = s;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("    -DNDEBUG '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "    -DNDEBUG '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = MinSizeRel;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7E2474BFB14D4DB5A370531F /* Build configuration list for PBXProject "boost_container" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				610D0FA8AE4D4EDCB9E90E5B /* Debug */,
				697CFB9A8E234F32AA393DC5 /* Release */,
				093453FC7D994DF89F87CF32 /* MinSizeRel */,
				3229EA39436D4701B566D8FE /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		B2A6495486B24B8991078E77 /* Build configuration list for PBXAggregateTarget "ZERO_CHECK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3DA70A73B56049BEA025779E /* Debug */,
				A56C5E6169584948BC27EBA6 /* Release */,
				D31DE72ACB044783AF617868 /* MinSizeRel */,
				EAE06D9AAEE54A539627C69C /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		CA0F84C0103E4A5E973A4162 /* Build configuration list for PBXAggregateTarget "ALL_BUILD" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				04D8ABC7D34842BE849B1778 /* Debug */,
				F466A3703A9F4E66BFE3D298 /* Release */,
				8EDDEB2D481046C7B7A1E7E9 /* MinSizeRel */,
				8B048430FB6D41C7A78FE92E /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		E63A43C7460D45E581FFCD31 /* Build configuration list for PBXNativeTarget "boost_container" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C8D8F0531DCB46399E52E140 /* Debug */,
				156CD7E8802B48CD9152335E /* Release */,
				F7492C5F3B0E4B008153580A /* MinSizeRel */,
				CB7A9C8740A04D638E8B39FE /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 9C2DBDD6080A468BA4FFBD0C /* Project object */;
}
