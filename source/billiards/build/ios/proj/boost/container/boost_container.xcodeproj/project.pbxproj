// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXAggregateTarget section */
		95BE28626AE14F3D8701395B /* ZERO_CHECK */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = 08C4426181A84055BFCF6D96 /* Build configuration list for PBXAggregateTarget "ZERO_CHECK" */;
			buildPhases = (
				8E1615DB12FAD784F78A8BB0 /* Generate CMakeFiles/ZERO_CHECK */,
			);
			dependencies = (
			);
			name = ZERO_CHECK;
			productName = ZERO_CHECK;
		};
		ED3539B8EA5B4D789A77D4BF /* ALL_BUILD */ = {
			isa = PBXAggregateTarget;
			buildConfigurationList = D6F36EC70D3E4B088E4AF958 /* Build configuration list for PBXAggregateTarget "ALL_BUILD" */;
			buildPhases = (
				DD95FDB8C654424B4F4C6AE8 /* Generate CMakeFiles/ALL_BUILD */,
			);
			dependencies = (
				4222C1064137443695BF7D96 /* PBXTargetDependency */,
				AC79CB8DC61C4CE1B9CB7414 /* PBXTargetDependency */,
			);
			name = ALL_BUILD;
			productName = ALL_BUILD;
		};
/* End PBXAggregateTarget section */

/* Begin PBXBuildFile section */
		160A97C5BB4F4E698ACF4C78 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */ = {isa = PBXBuildFile; fileRef = 12AECC5801DB4E768880E57D /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */; };
		227FDEF334244D0E8A04B251 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */ = {isa = PBXBuildFile; fileRef = 4472461414034C21986F1553 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */; };
		3B800179DA2F4BD2BF98F47D /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */ = {isa = PBXBuildFile; fileRef = E57897F0C48C4249AB99D015 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */; };
		7C582851CD9D40CABF672D5F /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */ = {isa = PBXBuildFile; fileRef = 159E3B85ADB5408A99CFD529 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */; };
		85AD419F3D9A4E60BDC98DEF /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */ = {isa = PBXBuildFile; fileRef = BA557C5DC0DF45E2A9465E1E /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */; };
		B93A0B707C7F471886AE6EEC /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */ = {isa = PBXBuildFile; fileRef = 2D8DBFC7E2814908AEB495EF /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */; };
		E607071DE4B942C7A4A0A8A4 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */ = {isa = PBXBuildFile; fileRef = AC5FC1B99D7E41E8A1EA7E5B /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */; };
		EC06140F979B42A290C8B047 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */ = {isa = PBXBuildFile; fileRef = 7264CD5EEA9C435E99FEE40D /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */; };
		F2A023AEFDA141B4BE48AC1C /* /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */ = {isa = PBXBuildFile; fileRef = 99D40320CC134D5FBA3EC3C9 /* /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */; };
/* End PBXBuildFile section */

/* Begin PBXBuildStyle section */
		2722503818C14378AF928FB7 /* Debug */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = Debug;
		};
		319F3608510443CF82C0EED2 /* RelWithDebInfo */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = RelWithDebInfo;
		};
		8837FBF1416040978F033411 /* Release */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = Release;
		};
		C432C78ACDCB4331A15CD3C7 /* MinSizeRel */ = {
			isa = PBXBuildStyle;
			buildSettings = {
				COPY_PHASE_STRIP = NO;
			};
			name = MinSizeRel;
		};
/* End PBXBuildStyle section */

/* Begin PBXContainerItemProxy section */
		0B9B48562E104CF38A70A37E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9C2DBDD6080A468BA4FFBD0C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 95BE28626AE14F3D8701395B;
			remoteInfo = ZERO_CHECK;
		};
		4FCE4A1FCD4A406BB4493491 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9C2DBDD6080A468BA4FFBD0C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 95BE28626AE14F3D8701395B;
			remoteInfo = ZERO_CHECK;
		};
		BCD89D16A4174F2288E411FF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 9C2DBDD6080A468BA4FFBD0C /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B3539C7458184894A8F35189;
			remoteInfo = boost_container;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		12AECC5801DB4E768880E57D /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */ = {isa = PBXFileReference; explicitFileType = sourcecode.c.c; fileEncoding = 4; name = alloc_lib.c; path = alloc_lib.c; sourceTree = SOURCE_ROOT; };
		159E3B85ADB5408A99CFD529 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = dlmalloc.cpp; path = dlmalloc.cpp; sourceTree = SOURCE_ROOT; };
		2D8DBFC7E2814908AEB495EF /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = unsynchronized_pool_resource.cpp; path = unsynchronized_pool_resource.cpp; sourceTree = SOURCE_ROOT; };
		4472461414034C21986F1553 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = synchronized_pool_resource.cpp; path = synchronized_pool_resource.cpp; sourceTree = SOURCE_ROOT; };
		7264CD5EEA9C435E99FEE40D /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = global_resource.cpp; path = global_resource.cpp; sourceTree = SOURCE_ROOT; };
		90F9038852BA4E40A7C27BFA /* boost_container */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libboost_container.a; sourceTree = BUILT_PRODUCTS_DIR; };
		99D40320CC134D5FBA3EC3C9 /* /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */ = {isa = PBXFileReference; explicitFileType = sourcecode.text.plist; fileEncoding = 4; name = Info.plist; path = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist; sourceTree = "<absolute>"; };
		AC5FC1B99D7E41E8A1EA7E5B /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = pool_resource.cpp; path = pool_resource.cpp; sourceTree = SOURCE_ROOT; };
		BA557C5DC0DF45E2A9465E1E /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */ = {isa = PBXFileReference; explicitFileType = sourcecode.text; fileEncoding = 4; name = CMakeLists.txt; path = CMakeLists.txt; sourceTree = SOURCE_ROOT; };
		C284D88DE71A495CBC0BA3B7 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */ = {isa = PBXFileReference; explicitFileType = sourcecode.text; fileEncoding = 4; name = CMakeLists.txt; path = CMakeLists.txt; sourceTree = SOURCE_ROOT; };
		E57897F0C48C4249AB99D015 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */ = {isa = PBXFileReference; explicitFileType = sourcecode.cpp.cpp; fileEncoding = 4; name = monotonic_buffer_resource.cpp; path = monotonic_buffer_resource.cpp; sourceTree = SOURCE_ROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		BECFBEC0AED34FD1BD0926E6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		073F85FB56B542559AFF3657 /* Resources */ = {
			isa = PBXGroup;
			children = (
				99D40320CC134D5FBA3EC3C9 /* /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/boost_container.dir/Info.plist */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		336D6B09E81A48B28604A5E1 = {
			isa = PBXGroup;
			children = (
				6BB83D7B4C744F60B14DEA10 /* Utils */,
				C2811D00C65F46DCA0DCED0E /* ALL_BUILD */,
				9D75A0897B9D41B3BDF5DF6E /* Products */,
				57EE553BB6B94C5F93E5D0A6 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		3E6E0520DD76465BA5B759E6 /* Source Files */ = {
			isa = PBXGroup;
			children = (
				12AECC5801DB4E768880E57D /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */,
				159E3B85ADB5408A99CFD529 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */,
				7264CD5EEA9C435E99FEE40D /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */,
				E57897F0C48C4249AB99D015 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */,
				AC5FC1B99D7E41E8A1EA7E5B /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */,
				4472461414034C21986F1553 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */,
				2D8DBFC7E2814908AEB495EF /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */,
			);
			name = "Source Files";
			sourceTree = "<group>";
		};
		57EE553BB6B94C5F93E5D0A6 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		6B08F950E56441A4A4397946 /* boost_container */ = {
			isa = PBXGroup;
			children = (
				3E6E0520DD76465BA5B759E6 /* Source Files */,
				073F85FB56B542559AFF3657 /* Resources */,
				BA557C5DC0DF45E2A9465E1E /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */,
			);
			name = boost_container;
			sourceTree = "<group>";
		};
		6BB83D7B4C744F60B14DEA10 /* Utils */ = {
			isa = PBXGroup;
			children = (
				6B08F950E56441A4A4397946 /* boost_container */,
			);
			name = Utils;
			sourceTree = "<group>";
		};
		9D75A0897B9D41B3BDF5DF6E /* Products */ = {
			isa = PBXGroup;
			children = (
				90F9038852BA4E40A7C27BFA /* boost_container */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		C2811D00C65F46DCA0DCED0E /* ALL_BUILD */ = {
			isa = PBXGroup;
			children = (
				DC2838E023704EA8897D3069 /* CMake Rules */,
				C284D88DE71A495CBC0BA3B7 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/CMakeLists.txt */,
			);
			name = ALL_BUILD;
			sourceTree = "<group>";
		};
		DC2838E023704EA8897D3069 /* CMake Rules */ = {
			isa = PBXGroup;
			children = (
			);
			name = "CMake Rules";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		B3539C7458184894A8F35189 /* boost_container */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 98EFE2E954204691A5B2AC94 /* Build configuration list for PBXNativeTarget "boost_container" */;
			buildPhases = (
				0FEEDD5E21C549F6A6AADDFD /* Sources */,
				BECFBEC0AED34FD1BD0926E6 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				F1CD63EBC6C04D38AFFF25C2 /* PBXTargetDependency */,
			);
			name = boost_container;
			productName = boost_container;
			productReference = 90F9038852BA4E40A7C27BFA /* boost_container */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		9C2DBDD6080A468BA4FFBD0C /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1540;
			};
			buildConfigurationList = 86FF61DB9C684A23B940FFEC /* Build configuration list for PBXProject "boost_container" */;
			buildSettings = {
			};
			buildStyles = (
				2722503818C14378AF928FB7 /* Debug */,
				8837FBF1416040978F033411 /* Release */,
				C432C78ACDCB4331A15CD3C7 /* MinSizeRel */,
				319F3608510443CF82C0EED2 /* RelWithDebInfo */,
			);
			compatibilityVersion = "Xcode 3.2";
			hasScannedForEncodings = 0;
			mainGroup = 336D6B09E81A48B28604A5E1;
			projectDirPath = "/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container";
			projectRoot = "";
			targets = (
				ED3539B8EA5B4D789A77D4BF /* ALL_BUILD */,
				95BE28626AE14F3D8701395B /* ZERO_CHECK */,
				B3539C7458184894A8F35189 /* boost_container */,
			);
		};
/* End PBXProject section */

/* Begin PBXShellScriptBuildPhase section */
		3C0110FC8322DE7E50886E21 = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# shell script goes here
exit 0";
			showEnvVarsInLog = 0;
		};
		8E1615DB12FAD784F78A8BB0 /* Generate CMakeFiles/ZERO_CHECK */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Generate CMakeFiles/ZERO_CHECK";
			outputPaths = (
/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/ZERO_CHECK			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e
if test \"$CONFIGURATION\" = \"Debug\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
if test \"$CONFIGURATION\" = \"Release\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
if test \"$CONFIGURATION\" = \"MinSizeRel\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
if test \"$CONFIGURATION\" = \"RelWithDebInfo\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  make -f /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeScripts/ReRunCMake.make
fi
";
			showEnvVarsInLog = 0;
		};
		9B50530E35CC49C82C2FACCF = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# shell script goes here
exit 0";
			showEnvVarsInLog = 0;
		};
		DD95FDB8C654424B4F4C6AE8 /* Generate CMakeFiles/ALL_BUILD */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Generate CMakeFiles/ALL_BUILD";
			outputPaths = (
/Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/CMakeFiles/ALL_BUILD			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e
if test \"$CONFIGURATION\" = \"Debug\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
if test \"$CONFIGURATION\" = \"Release\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
if test \"$CONFIGURATION\" = \"MinSizeRel\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
if test \"$CONFIGURATION\" = \"RelWithDebInfo\"; then :
  cd /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container
  echo Build\\ all\\ projects
fi
";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		0FEEDD5E21C549F6A6AADDFD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				160A97C5BB4F4E698ACF4C78 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/alloc_lib.c */,
				7C582851CD9D40CABF672D5F /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/dlmalloc.cpp */,
				EC06140F979B42A290C8B047 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/global_resource.cpp */,
				3B800179DA2F4BD2BF98F47D /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/monotonic_buffer_resource.cpp */,
				E607071DE4B942C7A4A0A8A4 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/pool_resource.cpp */,
				227FDEF334244D0E8A04B251 /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/synchronized_pool_resource.cpp */,
				B93A0B707C7F471886AE6EEC /* /Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources/boost-source/container/unsynchronized_pool_resource.cpp */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		4222C1064137443695BF7D96 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 95BE28626AE14F3D8701395B /* ZERO_CHECK */;
			targetProxy = 0B9B48562E104CF38A70A37E /* PBXContainerItemProxy */;
		};
		AC79CB8DC61C4CE1B9CB7414 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B3539C7458184894A8F35189 /* boost_container */;
			targetProxy = BCD89D16A4174F2288E411FF /* PBXContainerItemProxy */;
		};
		F1CD63EBC6C04D38AFFF25C2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 95BE28626AE14F3D8701395B /* ZERO_CHECK */;
			targetProxy = 4FCE4A1FCD4A406BB4493491 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		15D671E60F8849CC92CA99DB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Release;
		};
		22ADAC8D17B549F1BD10533D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/archives/Debug;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("   '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "   '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Debug;
		};
		2A96DECC13B84DDA863D2B2C /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/build;
			};
			name = RelWithDebInfo;
		};
		2AA83033FB6947A3A63BCE63 /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = RelWithDebInfo;
		};
		30AA1D8718564C7D883AFC11 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Debug;
		};
		4C296F7BD0844E7F936E232E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Debug;
		};
		5AB51AF07A2A4FC5BB56E824 /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/archives/RelWithDebInfo;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 2;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("       -DNDEBUG '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "       -DNDEBUG '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = RelWithDebInfo;
		};
		6BD15AFA6E7B48009938F0C4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/archives/Release;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("    -DNDEBUG '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "    -DNDEBUG '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Release;
		};
		99DA2D1BCB2847DCA7C5A5DE /* RelWithDebInfo */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = RelWithDebInfo;
		};
		A532DDA4D19D4828AFF62560 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/build;
			};
			name = Release;
		};
		AB4C8DBDBB5843A2A6CC639A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/build;
			};
			name = Debug;
		};
		ADC0B51AF7754864918724D1 /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = MinSizeRel;
		};
		B66BD42137224D2BA9287BB0 /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/build;
			};
			name = MinSizeRel;
		};
		CBEBD5FFD39C40ED8151211C /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ZERO_CHECK;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = MinSizeRel;
		};
		DDE0BF8003D249308ECA10BA /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = "";
				OTHER_LDFLAGS = ("","$(inherited)");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = ALL_BUILD;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = Release;
		};
		E824C20221F34637BEA843C6 /* MinSizeRel */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container/archives/MinSizeRel;
				EXECUTABLE_PREFIX = lib;
				EXECUTABLE_SUFFIX = .a;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_INLINES_ARE_PRIVATE_EXTERN = NO;
				GCC_OPTIMIZATION_LEVEL = s;
				GCC_PREPROCESSOR_DEFINITIONS = ("'CMAKE_INTDIR=\"$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)\"'",BOOST_CONTAINER_NO_LIB,BOOST_CONTAINER_STATIC_LINK,BOOST_UUID_FORCE_AUTO_LINK,"'CC_PLATFORM_WINDOWS=2'","'CC_PLATFORM_MACOS=4'","'CC_PLATFORM_IOS=1'","'CC_PLATFORM_MAC_OSX=4'","'CC_PLATFORM_MAC_IOS=1'","'CC_PLATFORM_ANDROID=3'","'CC_PLATFORM_OHOS=5'","'CC_PLATFORM_LINUX=6'","'CC_PLATFORM_QNX=7'","'CC_PLATFORM_NX=8'","'CC_PLATFORM_OPENHARMONY=10'","'CC_PLATFORM_EMSCRIPTEN=9'","'CC_PLATFORM=1'",BOOST_NO_CXX98_FUNCTION_BASE,"'CC_NETMODE_CLIENT=0'","'CC_NETMODE_LISTEN_SERVER=1'","'CC_NETMODE_HOST_SERVER=2'","'CC_NETMODE=0'","'BOOST_ALL_NO_LIB=1'","$(inherited)");
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				HEADER_SEARCH_PATHS = (/Applications/Cocos/Creator/3.8.5/CocosCreator.app/Contents/Resources/resources/3d/engine/native/external/sources,"$(inherited)");
				INSTALL_PATH = "";
				LIBRARY_STYLE = STATIC;
				OTHER_CFLAGS = ("    -DNDEBUG '-std=gnu99' ","$(inherited)");
				OTHER_CPLUSPLUSFLAGS = "    -DNDEBUG '-std=c++17' ";
				OTHER_LIBTOOLFLAGS = ("");
				OTHER_REZFLAGS = "";
				PRODUCT_NAME = boost_container;
				SECTORDER_FLAGS = "";
				SYMROOT = /Users/<USER>/work/game/LudoDomino/ludo_group/ludo_h5/web_games/source/billiards/build/ios/proj/boost/container;
				USE_HEADERMAP = NO;
				WARNING_CFLAGS = ("$(inherited)");
			};
			name = MinSizeRel;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		08C4426181A84055BFCF6D96 /* Build configuration list for PBXAggregateTarget "ZERO_CHECK" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				4C296F7BD0844E7F936E232E /* Debug */,
				15D671E60F8849CC92CA99DB /* Release */,
				CBEBD5FFD39C40ED8151211C /* MinSizeRel */,
				99DA2D1BCB2847DCA7C5A5DE /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		86FF61DB9C684A23B940FFEC /* Build configuration list for PBXProject "boost_container" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AB4C8DBDBB5843A2A6CC639A /* Debug */,
				A532DDA4D19D4828AFF62560 /* Release */,
				B66BD42137224D2BA9287BB0 /* MinSizeRel */,
				2A96DECC13B84DDA863D2B2C /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		98EFE2E954204691A5B2AC94 /* Build configuration list for PBXNativeTarget "boost_container" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				22ADAC8D17B549F1BD10533D /* Debug */,
				6BD15AFA6E7B48009938F0C4 /* Release */,
				E824C20221F34637BEA843C6 /* MinSizeRel */,
				5AB51AF07A2A4FC5BB56E824 /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		D6F36EC70D3E4B088E4AF958 /* Build configuration list for PBXAggregateTarget "ALL_BUILD" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				30AA1D8718564C7D883AFC11 /* Debug */,
				DDE0BF8003D249308ECA10BA /* Release */,
				ADC0B51AF7754864918724D1 /* MinSizeRel */,
				2AA83033FB6947A3A63BCE63 /* RelWithDebInfo */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 9C2DBDD6080A468BA4FFBD0C /* Project object */;
}
