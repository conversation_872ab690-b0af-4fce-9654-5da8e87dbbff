import { _decorator } from 'cc';
import Global from '../../core/data/Global';
import BaseTipMgr from '../../core/tools/BaseTipMgr';
import { Game } from '../GameUI';
import { BusinessOrderCode, ErrorCode } from './Enum';
import GameData from './GameData';
import { BilliardSocket } from '../net/BilliardSocket';
import BilliardProto from '../../core/net/BilliardProto';
import { GameUtil } from '../../core/tools/GameUtil';
import { Match } from '../../match/Match';
import { dialogManager } from '../../common/dialog/DialogManager';
import { MatchMessageManager } from './MatchMessageManager';
import AudioMgr from '../../core/tools/AudioMgr';
import { uiManager } from '../../core/ui/UIManager';
import { UIID } from '../../Main';
import { ReconnectCtrl } from '../../common/reconnect/ReconnectCtrl';
import i18n from '../../core/tools/i18n';
import BallCtrl from '../ball/BallCtrl';
import { JsbBrid } from '../../core/tools/JsbBridge';
import { JsbWebSocket } from '../../core/net/JsbWebSocket';
import HitBallRuleMgr from '../ball/HitBallRuleMgr';
const { ccclass, property } = _decorator;

@ccclass('GameService')
export default class GameService {
    static _instance: GameService;
    private _game: Game;
    private _match: Match;
    private _isGetOpenBall: boolean = false;
    public socket: BilliardSocket;

    private _curSocketClose: boolean = false;//当前是否网络关闭
    private _maxLoginRequestTimes: number = 6;//最大登录请求次数
    private _loginRequestTimes: number = 0;//登录请求次数
    private _loginRequestTimeout: number = 5000;//登录请求超时时间
    private _connectInterval = null;

    constructor() { }
    private _settimeCount: number | null = null;

    /** 单例获取器 */
    static get instance(): GameService {
        return GameService._instance || (GameService._instance = new GameService());
    }

    /** 初始化游戏视图 */
    initView(game: Game): void {
        this._game = game;

        if (Global.gameState == 1 && Global.isconnect) {
            this.LoginRequest();
        }
    }

    /** 初始化匹配视图 */
    initMatchView(match: Match): void {
        this._match = match;
        MatchMessageManager.instance.setMatch(match);
    }

    /**
     * 匹配响应处理
     * @param response 响应数据
     */
    private matchResponse(response: any): void {
        GameUtil.log("====开始匹配响应数据====", JSON.stringify(response));
        MatchMessageManager.instance.enqueueMessage('match', response);
    }

    /**
     * 匹配信息响应处理
     * @param response 响应数据
     */
    private matchInfoResponse(response: any): void {
        GameUtil.log("====游戏匹配信息响应数据====", response);
        MatchMessageManager.instance.enqueueMessage('matchInfo', response);
    }

    /**
     * 匹配开始响应处理
     * @param response 响应数据
     */
    private matchStartResponse(response: any): void {
        GameUtil.log("====匹配成功响应数据====", JSON.stringify(response));
        MatchMessageManager.instance.enqueueMessage('matchStart', response);
    }

    /**
     * 取消匹配响应处理
     * @param response 响应数据
     */
    private matchCancelResponse(response: any): void {
        GameUtil.log("====取消匹配响应数据====", JSON.stringify(response));
        if (this._match) {
            GameUtil.log("====重新初始化匹配信息====");
            this._match.initInfo();
        }
    }

    /**
     * res: { gateWayCmd, businessOrder, command, code, body };
     * socket 链接，如果这里在页面打开后，socket才连上，则直接发起用户票据登陆
     * 如果是在游戏中，则onOpen后发起票据登陆
     */
    public connectSocket(socketCloseCb?): void {
        if (!this.socket) this.socket = new BilliardSocket();
        console.log("=发起socket链接===");
        this.socket.connectSocket(() => {
            GameUtil.log(Global.isReChooseServer + "：isReChooseServer=连上了socket=gameState=" + Global.gameState);
            if (Global.gameState == 1) {
                if (Global.isReChooseServer) {
                    this.ReChooseGameServer();
                } else {
                    this.LoginRequest();//票据登录
                }
            } else if (Global.gameState <= 1) {
                this.betsRequest();
            }
        },(res) => {
            if (!res) return;
            let [gateWayCmd, businessOrder, code] = [res.gateWayCmd, res.businessOrder, res.code];
            // console.log(JSON.stringify(res));
            // console.log(code + ":code===消息响应==businessOrder:" + businessOrder + "=gateWayCmd:" + gateWayCmd);
            if (code > 0) {
                this.handleGatewayError(code);
                return;
            }
            if (gateWayCmd == 4) {//业务逻辑
                this.handleGameResponse(res.body, businessOrder);
            } else if (gateWayCmd == 5) {//网管信封解析UserMessageEnvelope
                res.body && this.handleUserMessageEnvelope(res.body);
            }
        }, (event) => {
            this.handleSocketClose(event);
            socketCloseCb && socketCloseCb();
        });
    }

    /**重新选服 */
    ReChooseGameServer() {
        this.socket.reChooseGameServer();
    }

    // 用户登录请求 ==> command(1)。
    LoginRequest() {
        let isShowGameOver = BallCtrl.isShowGameOver();
        GameUtil.log("===发起票据登陆==isShowGameOver=" + isShowGameOver);
        if (isShowGameOver) return;
        this.socket?.ticketLogin();
        if (!this._curSocketClose) {
            this._connectInterval && clearInterval(this._connectInterval);
            this._connectInterval = setInterval(() => {
                this.onLoginRequestTimeout();
            }, this._loginRequestTimeout);
        }
    }
    private onLoginRequestTimeout(): void {
        this._loginRequestTimes++;
        if (this._loginRequestTimes < this._maxLoginRequestTimes) {
            GameUtil.log("GameService--ticketLogin---登录请求次数:"+ this._loginRequestTimes);
            this.LoginRequest();
            if (this._loginRequestTimes == 1) {
                ReconnectCtrl.ins.connect(true, {}, true, true);
            }
        } else {
            GameUtil.log("GameService--ticketLogin---登录请求次数超过最大次数");
            this._connectInterval && clearInterval(this._connectInterval);
            this._loginRequestTimes = 0;

            ReconnectCtrl.ins.closeView();
            dialogManager.showConfirm({
                tip: i18n.t("dialog_NetLost"),
                confirm: () => {
                    ReconnectCtrl.ins.connect(true, {}, true, true);
                    this.LoginRequest();
                },
                cancel: () => {
                    GameService.instance.backHall(true);
                },
                confirmTx: i18n.t("connect"),
                cancelTx: i18n.t("exit")
            });
        }
    }

    //球杆摆动请求 ==> command(2)。
    DragMoveRequest(dragInfo) {
        this.socket.dragMove(dragInfo);
    }

    //击球请求 ==> command(3)。
    BallHitRequest(hitBallInfo, lineH, lineR) {
        GameData.isSendBallStop = false;
        this.socket.ballHit(hitBallInfo, lineH, lineR);
        GameUtil.log("---击球---isSendBallStop--" + GameData.isSendBallStop);
        //TODO::测试
        // Native.uploadLogMsg(hitBallInfo);

        // let str = JSON.stringify(hitBallInfo);
        // console.log("===ballHitRequest===", str);
        // this._game.testLabel.string = String(str);
    }

    /**击球之后球全部停止请求 ==> command(4)。*/
    AllBallStopRequest(ballInfo) {
        GameUtil.log("=GameService.AllBallStopRequest=");
        GameUtil.log( ballInfo);
        this.socket.allBallStop(ballInfo);
    }

    /** 球停之后的事件处理请求 ==> command(5)。*/
    AllBallStopEventRequest(ballInfo: Object) {
        this.socket.allBallStopEvent(ballInfo);
    }

    CdTimeRequest() {
        this.socket.getCdTime();
    }

    /**主动退出 服务端不会响应，如果100ms未返回，则主动返回大厅 */
    private quitTimeOut = null;
    quitRequest() {
        if (this.quitTimeOut) clearTimeout(this.quitTimeOut);
        this.socket?.quit();
        if (!this.quitTimeOut) {
            this.quitTimeOut = setTimeout(() => {
                GameData.isGameOver = true;
                this.backHall(true);
            }, 100);
        }
    }
    /**下注请求 */
    betsRequest() {
        this.socket.betsRequest();
    }

    /**
     * 带回调的下注请求
     * 请求下注列表，并在获取到结果后执行指定的回调函数
     * @param callback 获取到下注列表后要执行的回调函数
     */
    betsRequestWithCallback(callback?: () => void) {
        // 先记住当前的回调
        const originalBetsResponse = this.betsResponse;

        // 重写betsResponse方法，添加回调执行
        this.betsResponse = (response) => {
            // 首先执行原始的处理逻辑
            originalBetsResponse.call(this, response);

            // 然后执行传入的回调
            if (callback) {
                callback();
            }

            // 恢复原始的betsResponse方法
            this.betsResponse = originalBetsResponse;
        };

        // 发起下注请求
        this.socket.betsRequest();
    }
    /**匹配请求 */
    matchRequest(matchData) {
        this.socket.matchRequest(matchData);
    }
    /**取消匹配请求 */
    matchCancelRequest(matchData) {
        this.socket.matchCancelRequest(matchData);
    }
    /**gatecmd=5 信封解析 */
    private handleUserMessageEnvelope(msg): void {
        if (!msg || !this.socket) return;
        let d = BilliardProto.UserMessageEnvelope.decode(msg);
        let senderId = d.senderId;
        // console.log("=信封消息=" + senderId);
        if (senderId && senderId == 'athenamatch#' + Global.gameId) {
            this.handleGameResponse(d.message, d.MessageType);
        } 
    }

    private handleGameResponse(bodyData: any, businessOrder?): void {
        if (BallCtrl.isShowGameOver()) return;   //TODO:;条件需要的，测试阶段先去掉
        if (!bodyData || !this.socket) return;
        // console.log(`[response] [businessOrder:${businessOrder}] `);
        switch (businessOrder) {
            case BusinessOrderCode.billiard_message:
                let d = BilliardProto.BilliardResq.decode(bodyData);
                if (!d) return;
                this.doubleStreamResponse(d);
                break;
            case BusinessOrderCode.billiard_betsRequest://下注
                let betsResponseData = BilliardProto.BetsResponse.decode(bodyData);
                if (!betsResponseData) return;
                this.betsResponse(betsResponseData);
                GameUtil.log(`[response] [businessOrder:${businessOrder}] data=${JSON.stringify(betsResponseData)}`);
                break;
            case BusinessOrderCode.billiard_matchGameRequest://匹配
                let matchGameData400 = BilliardProto.MatchResponse.decode(bodyData);
                if (!matchGameData400) return;
                this.matchResponse(matchGameData400);
                GameUtil.log(`[response] [businessOrder:${businessOrder}] data=${JSON.stringify(matchGameData400)}`);
                break;
            case BusinessOrderCode.billiard_matchResponse://匹配广播
                let matchGameData = BilliardProto.MatchResponse.decode(bodyData);
                if (!matchGameData) return;
                this.matchResponse(matchGameData);
                GameUtil.log(`[response] [businessOrder:${businessOrder}] data=${JSON.stringify(matchGameData)}`);
                break;
            case BusinessOrderCode.billiard_matchCancelResponse://取消匹配广播
                let matchCancelData = BilliardProto.MatchCancelResponse.decode(bodyData);
                if (!matchCancelData) return;
                this.matchCancelResponse(matchCancelData);
                GameUtil.log(`[response] [businessOrder:${businessOrder}] data=${JSON.stringify(matchCancelData)}`);
                break;
            case BusinessOrderCode.billiard_matchGameCancelRequest://取消匹配（暂时收不到服务端的回调）

                // let matchCancelData401 = BilliardProto.MatchCancelResponse.decode(bodyData);
                // if (!matchCancelData401) return;
                // this.matchCancelResponse(matchCancelData401);
                // GameUtil.log(`[response] [businessOrder:${businessOrder}] data=${JSON.stringify(matchCancelData401)}`);
                break;
            case BusinessOrderCode.billiard_matchStartResponse://匹配成功广播
                let matchStartData = BilliardProto.MatchStartResponse.decode(bodyData);
                if (!matchStartData) return;
                this.matchStartResponse(matchStartData);
                GameUtil.log(`[response] [businessOrder:${businessOrder}] data=${JSON.stringify(matchStartData)}`);
                break;
            case BusinessOrderCode.billiard_matchInfoResponse://匹配信息广播
                let matchInfoData = BilliardProto.MatchInfoResponse.decode(bodyData);
                if (!matchInfoData) return;
                this.matchInfoResponse(matchInfoData);
                GameUtil.log(`[response] [businessOrder:${businessOrder}] data=${JSON.stringify(matchInfoData)}`);
                break;
            case BusinessOrderCode.billiard_reChooseGameServer://重新选服务
                let data = BilliardProto.ReChooseGameServerResponse.decode(bodyData);
                if (!data) return;
                GameUtil.log(`[response] [businessOrder:${businessOrder}] data=${JSON.stringify(data)}`);
                if (data.code == 1) {
                    Global.gameInfo.instanceId = data.newInstanceId;
                    Global.isReChooseServer = false;
                    GameUtil.log("===发起重新选服务=====");
                    this.LoginRequest();
                }
                break;
        }
    }

    private doubleStreamResponse(data): void {
        if (data?.openBallResponse) {
            if (data && data.roomId && data.roomId != Global.gameInfo.roomId) return;
            this._handleLoginResponse(data.openBallResponse);

        } else if (data?.dragMoveResponse) {
            this._handleDragMoveResponse(data.dragMoveResponse);

        } else if (data?.ballHitResponse) {
            this._handleBallHitResponse(data.ballHitResponse);

        } else if (data?.allBallStopResponse) {
            this._handleAllBallStopResponse(data.allBallStopResponse);

        } else if (data?.gameOverResponse) {
            this._handleGameOverResponse(data.gameOverResponse);

        } else if (data?.playerChangeResponse) {
            this._handlePlayerChangeResponse(data.playerChangeResponse);

        } else if (data?.cdTimeResponse) {
            this._handleCdTimeResponse(data.cdTimeResponse);

        } else if (data?.offlineResponse) {
            this._handleOfflineResponse(data.offlineResponse);

        } else if (data?.quitResponse) {
            GameUtil.log("用户主动退出====");
            this.backHall(true);
        }
    }
    //下注
    private betsResponse(a) {
        try {
            GameUtil.log("====betsResponse====", JSON.stringify(a));

            if (a?.bets && a.bets.length > 0) {
                const bet = a.bets[0];
                if (bet?.gameBet) {
                    Global.betsList = bet.gameBet;
                }
                if (bet?.goldFloorsList) {
                    Global.goldFloorsList = bet.goldFloorsList;
                }
                if (a?.recycleBonus) {
                    Global.recycleBonus = Number(a.recycleBonus);
                }
            }

            // 设置已获取服务端下注信息的标记
            Global.setBetsInfoFetched();

            if (this._match) {
                // 如果Match界面已经初始化，刷新下注控制器的UI
                if (this._match.matchBetCtrl) {
                    this._match.matchBetCtrl.refreshAfterServerUpdate();
                } else {
                    this._match.initInfo();
                }
            }
        } catch (error) {
            GameUtil.log("Error in betsResponse:", error);
        }
    }

    private _handleLoginResponse(msg) {
        this._connectInterval && clearInterval(this._connectInterval);
        this._curSocketClose = false;
        this._loginRequestTimes = 0;

        GameUtil.log("GameService._handleLoginResponse");
        GameUtil.log(msg);
        if (msg.errorCode && msg.errorCode > 0) {
            BaseTipMgr.showTip(msg.errorCode);
            // SceneMgr.open(SceneMgr.scenes.match);
            return;
        }
        //TODO::检测是否上一局异常结束，重新开局了
        this._handleOpenBallResponse(msg);
        if (!GameData.openBallIsRestart) {
            // let self = this;
            // if (Global.isResume) {
            //     self._settimeCount = setTimeout(() => {
            //         self._game.OpenBall();
            //     }, 1000);
            // } else {
            //     self._game.OpenBall();
            // }
            this._game.OpenBall();
        }
    }

    private _handleOpenBallResponse(msg) {
        GameUtil.log(msg);
        if (msg.errorCode && msg.errorCode > 0) {
            BaseTipMgr.showTip(msg.errorCode);
            // SceneMgr.open(SceneMgr.scenes.match);
            return;
        }
        //TODO::检测是否上一局异常结束，重新开局了
        if (GameData.playBall) {
            // Native.uploadLogMsg({ "gameLog": "OpenBallResponse isrestart:true" })
            msg.isRestart = true;
        }
        this._isGetOpenBall = true;
        Global.seatsInfo = msg.players;
        GameData.initData(msg);
        GameData.initObjectBall();
        GameData.isRestart = false;
        Global.cost = msg.cost;
        GameData.updateBilliardsStatus();
        GameUtil.log("GameService._handleOpenBallResponse" + GameData.openBallIsRestart);
        if (GameData.openBallIsRestart) this._game.OpenBall();
    }

    private _handleDragMoveResponse(msg) {
        if (!this._isGetOpenBall) return;
        GameData.updateData(msg);
        this._game.dragMoveResponse(msg);
    }

    private _handleBallHitResponse(msg) {
        if (!this._isGetOpenBall) return;
        if (msg.errorCode && msg.errorCode > 0) {
            BaseTipMgr.showTip(msg.errorCode);
            return;
        }

        GameData.updateData(msg);
        GameData.noHit = false;
        GameData.updateBilliardsStatus();
        this._game.ballHitResponse(GameData.hitBall,msg);
    }

    private _handleAllBallStopResponse(msg) {
        if (!this._isGetOpenBall) return;
        let hasObject = GameData.hasObjectBall(msg.currentOperate);
        GameData.updateData(msg);
        if (msg.ballType >= 0) {
            GameData.setObjectBall(msg.currentOperate, msg.ballType);
            if (!hasObject) {
                GameData.roundSetObjectBall = true;
                GameData.updateBilliardsStatus();
            }
        }
        this._game.allBallStopResponse(msg);
    }
    /**
     * 切换用户；检测上一局是否异常结束
     * @param msg
     * @returns
     */
    public isErrorPro = false;//是否异常操作
    private _handlePlayerChangeResponse(msg) {
        this.isErrorPro = false;
        if (!this._isGetOpenBall) return;
        let preIdx = GameData.currentOperate;
        if (!msg.noHit && msg.noStopBallEvent) {
            HitBallRuleMgr.checkFoul();
            GameUtil.log(GameData.isRestart + "=GameService异常结束=" + GameData.isGameOver);
            if (GameData.isRestart || GameData.isGameOver) {
                let ballInfo = BallCtrl.checkGameOver();
                GameUtil.log(ballInfo)
                if (ballInfo.hasOwnProperty('event')) {
                    if (GameData.isRestart) {
                        BallCtrl.restartGame();
                        GameData.restartGame();
                        this._game.restartGame();
                    }
                    this.AllBallStopEventRequest(ballInfo);
                    BallCtrl.clearHitList();
                    return;
                }
            } else {
                //TODO::强制让球停
                // BallCtrl.stopAllBall();
                GameUtil.log("=强制让球停=" + (preIdx == Global.userId));
                if (preIdx == Global.userId) {
                    // this.closeSocket();
                    // return;
                } else {
                    //作为正常的一方同步球桌信息 AllBallStopRequest
                    this.isErrorPro = true;
                    BallCtrl.syncTableBall();
                }
            }
        }

        GameData.playerChange(msg, preIdx);
        this._game.playerChangeResponse(msg, preIdx);
    }

    private _handleGameOverResponse(msg) {
        GameData.gameOver(msg);
        this._game.gameOverResponse(msg);
        JsbBrid.setGameState();
    }

    private _handleCdTimeResponse(msg) {
        if (!this._isGetOpenBall) return;
        msg.cdTime && (GameData.cdTime = Math.floor(msg.cdTime / 1000));
        this._game.cdTimeResponse(msg);
    }

    private _handleOfflineResponse(msg) {
        // GameUtil.log("对手掉线 GameService.offlineResponse", !this._isGetOpenBall, GameData.isGameOver, GameData.currentOperate);
        // GameUtil.log(msg);
        if (!this._isGetOpenBall || GameData.isGameOver) return;
        this._game.offlineResponse(msg);
    }

    private handleGatewayError(code: number) {
        GameUtil.log("====网关错误===code=" + code);
        // this.resetData_gameOver();
        dialogManager.showGatewayError(code);
    }
    /**
     * socket close
     */
    private handleSocketClose(event) {
        this._connectInterval && clearInterval(this._connectInterval);
        this._loginRequestTimes = 0;
        this._curSocketClose = true;

        GameUtil.log(event);
        if (event && event.code == ErrorCode.TARGET_SERCER_NOT_EXISTS) {
            Global.isReChooseServer = true;
        }
        GameUtil.log("===桌球socket close==");
    }

    closeSocket() {
        this.socket?.closeSocket();

    }
    backToGameMode() {
        uiManager.closeAllDialog();
        Global.gameState = 0;
        uiManager.open(UIID.Match);
    }
    /**返回大厅
     * isQuit 是否主动退出
     * params {to,roomId}
    */
    backHall(isQuit: boolean = false, params = null, isBackHall = true) {
        BallCtrl.clear();
        AudioMgr.clear();
        if (isBackHall) {
            if (isQuit || !params) {
                JsbBrid.backHall();
            } else {
                JsbBrid.backHall(params);
            }
        }
        JsbBrid.resetAccount();
        JsbBrid.removeAllJsbView();
        this.clear();
        uiManager.closeAll();
    }
    restartGame() {
        GameUtil.log("====Gameservice.restartGame 重新开局====")
        this._isGetOpenBall = false;
    }

    resetGameData() {
        GameUtil.log("====Gameservice.resetGameData 清理====")
        if (this._settimeCount > 0) clearTimeout(this._settimeCount);
        if (this.quitTimeOut > 0) clearTimeout(this.quitTimeOut);
        this._isGetOpenBall = false;
        Global.isReChooseServer = false;
        GameData.clear();
    }

    clear() {
        GameUtil.log("====Gameservice.clear 清理====")
        if (this._settimeCount > 0) clearTimeout(this._settimeCount);
        if (this.quitTimeOut > 0) clearTimeout(this.quitTimeOut);
        this.socket?.cleanSocket();
        this._isGetOpenBall = false;
        Global.isReChooseServer = false;
        Global.betsList = [];

        GameData.clear();
        MatchMessageManager.instance.clear();
        MatchMessageManager._instance = null;
        GameService._instance = null;
    }
}

