import { color, v3, Vec3 } from "cc";
import Global from "../../core/data/Global";
import Utils from "../../core/tools/Utils";
import { AllBallStopEvent, BallType, BilliardsStatus } from "./Enum";
import LocalStorage from "../../core/tools/LocalStorage";
import { GameUtil } from "../../core/tools/GameUtil";

export enum TweenTags {
    game
}

class GameData {
    /**射线距白球的中心偏离值 */
    rayOffsetY = 22;
    /**球袋半径 */
    packetRadius = 25;
    /**球半径 */
    ballRadius = 16;
    /**球桌库边宽度 */
    tableSideWid = 84.5;//70.5 + 14
    ballCounts = 8;
    ballIndex = 4;        //所有球的层级
    whiteBallIndex = 5;   //白球的层级
    ballCircleIndex = 3;  //犯规后重新放置白球 提示球层级
    ballHandIndex = 5;    //犯规后重新放置白球 手型层级
    ballEnterScale = 1;//球入袋后，缩放
    /**洞口中心点 tag 1-6 */
    packetPosList = [[-256, -5], [-245, -480], [245, -480], [256, -5], [245, 480], [-245, 480]];
    // packetPosList = [[260, 0], [250, 480], [-250, 480], [-260, 0], [-250, -480], [250, -480]];
    /**如果母球是无效的位置，则重新尝试设置有效位置，这里定死几个位置 */
    whiteSavePosList = [[0, -32], [-32, -64], [32, -96], [64, -128]];
    
    playerNameColor = color(255, 255, 255, 122);
    /**球桌有效区域范围  左 右 下 上 */
    public TABLE_POS = [-240, 240, -470, 470];//[-280, 280, -495, 495];
    public BeginPos_White: Vec3 = v3(0, -230, 0);
    

    minHitPower: number = 40; //最小击球力度
    maxHitPower: number = 60; //最大击球力度
    ballLinearDamp: number = 0.4; //白球速度衰减系数;
    ballFriction: number = 0.1; //白球摩擦力系数;   
    tableLinearDamp: number = 0.4; //库边速度衰减系数
    CuePowerV: number = 0.8;           //设球杆力量值为X，影响系数为Y，X与Y根据球杆单独设置，不同球杆值不同。默认球杆X=1，Y=5。设力度表刻度为m ，m根据玩家在力度表拖动的距离决定，取值范围根据距离换算为0~100。
    CuePowerInfluence: number = 0;    //影响系数为Y
    PowerRange: number = 100;
    Restitution_White: number = 0.5;  //母球反弹力
    LineMaxLen: number = 100;
    LineValue: number = 80;             //辅助线系数默认50

    /**初始球位置 */
    ballList: Array<number> = null;
    /**球桌上球信息 */
    playBall: any = null;
    /**白球信息 */
    whiteBall: any = null;
    /**ballType我方球花色类型 */
    ballType: number;
    /**球杆方向 */
    cue: any = null;
    /**玩家id列表 */
    players: Array<PlayerInfo> = null;
    /**当前操作玩家id。 */
    currentOperate: number;
    /**当前操作玩家连杆数。 */
    combox: number = 0;
    /**noHit回合未击球 */
    noHit: boolean;
    /**倒计时总时间。 */
    totalTime: number;
    /**等待倒计时 */
    cdTime: number;
    /**击球 */
    hitBall: any = null;
    /**进球列表 */
    enterBallList: Array<number> = [];
    event: AllBallStopEvent = null;
    /** true 由于犯规导致换人， false 非返回导致换人*/
    isFoul: boolean;

    isInited = false;
    isGameOver: boolean = false;
    isRestart: boolean = false;
    /**获得自由球的玩家id */
    freeBallIdx: number = 0;

    /**玩家对应目标球 */
    playerObjectBallDic: Object = {};
    /**FistOpen = 1, //开球局(第一杆) Open = 2, 开放球局（非第一杆，有击球，但是未确定目标球）  Object = 3  击球（已确定目标球）游戏开局，还未进行开局（如果一局未操作，下一局还能否放大白色球放置位置？） */
    public billiardsStatus = 1;
    /**当前回合是否设置了目标球 */
    public roundSetObjectBall = false;

    /**openBall 是否重新开局 */
    public openBallIsRestart: boolean = false;

    public isSendBallStop: boolean = false;
    public lineH: number = 0;
    public lineR: number = 0;
    /**是否观战 */
    public isWatch: boolean = false;
    /**累计总回合不操作，游戏结束 */
    public maxRound = 3;
    /**winGold胜利获得 */
    public winGold = 0;

    public ballColor = ['E7E7E7', 'DCB946', '1817A2', 'C22F22', '694CA3', 'D47A37', '66B25E',
        '81242B', '211712', 'DCB946', '1817A2', 'C22F22', '694CA3', 'D47A37', '66B25E','81242B'
    ];
    initData(msg: any) {
        this.openBallIsRestart = false;
        if (msg.isRestart) {
            this.restartGame();
        }
        if (!this.enterBallList) this.enterBallList = [];
        msg.hasOwnProperty('ballList') && (this.ballList = msg.ballList);
        msg.hasOwnProperty('whiteBall') && (this.whiteBall = JSON.parse(msg.whiteBall));
        msg.hasOwnProperty('cue') && (this.cue = JSON.parse(msg.cue));
        msg.hasOwnProperty('playBall') && (this.playBall = JSON.parse(msg.playBall));
        msg.hasOwnProperty('hitBall') && (this.hitBall = JSON.parse(msg.hitBall));
        msg.hasOwnProperty('players') && (this.players = msg.players);
        msg.hasOwnProperty('currentOperate') && (this.currentOperate = msg.currentOperate);
        msg.hasOwnProperty('combox') && (this.combox = msg.combox || 0);
        msg.hasOwnProperty('noHit') && (this.noHit = msg.noHit);
        msg.hasOwnProperty('totalTime') && (this.totalTime = Math.ceil(msg.totalTime / 1000));
        msg.hasOwnProperty('cdTime') && (this.cdTime = Math.floor(msg.cdTime / 1000));
        msg.hasOwnProperty('enterBallList') && (this.enterBallList = JSON.parse(msg.enterBallList) || []);
        msg.hasOwnProperty('maxRound') && (this.maxRound = msg.maxRound);
        msg.hasOwnProperty('winGold') && (this.winGold = msg.winGold);

        if (this.players) {//断线重连前，一个玩家击球入袋 并确定球色（另一方玩家球色未提交服务端）；断线重连，出现一方玩家有目标球，一方没有
            let sureBallType = -1;
            let noBallTypePlayerId;
            this.isWatch = true;
            this.players.forEach(element => {
                if (element.ballType > -1) sureBallType = element.ballType;
                else noBallTypePlayerId = element.playerId;

                if (element.playerId == Global.userId) this.isWatch = false;
            });
            if (sureBallType > -1 && noBallTypePlayerId) {
                this.players.forEach(element => {
                    if (element.playerId == noBallTypePlayerId) element.ballType = 5 - sureBallType;
                });
            }
        }
    }

    updateData(msg) {
        if (!this.enterBallList) this.enterBallList = [];
        msg.hasOwnProperty('ballList') && (this.ballList = msg.ballList);
        msg.hasOwnProperty('whiteBall') && (this.whiteBall = JSON.parse(msg.whiteBall));
        msg.hasOwnProperty('playBall') && (this.playBall = JSON.parse(msg.playBall));
        msg.hasOwnProperty('hitBall') && (this.hitBall = JSON.parse(msg.hitBall));
        msg.hasOwnProperty('cue') && (this.cue = JSON.parse(msg.cue));
        msg.hasOwnProperty('players') && (this.players = msg.players);
        msg.hasOwnProperty('currentOperate') && (this.currentOperate = msg.currentOperate);
        msg.hasOwnProperty('combox') && (this.combox = msg.combox || 0);
        msg.hasOwnProperty('noHit') && (this.noHit = msg.noHit);
        msg.hasOwnProperty('totalTime') && (this.totalTime = Math.ceil(msg.totalTime / 1000));
        msg.hasOwnProperty('cdTime') && (this.cdTime = Math.ceil(msg.cdTime / 1000));
        msg.hasOwnProperty('enterBallList') && (this.enterBallList = JSON.parse(msg.enterBallList) || []);
    }

    updateBilliardsStatus() {
        if (!this.playBall) {
            this.billiardsStatus = BilliardsStatus.FirstOpen;
            if (!this.noHit) {
                this.billiardsStatus = BilliardsStatus.Open;
            }
        } else {
            if (!this.noHit && this.billiardsStatus == BilliardsStatus.FirstOpen) {
                this.billiardsStatus = BilliardsStatus.Open;
            } else {
                if (this.myBallType == BallType.Solid || this.myBallType == BallType.Stripe) {
                    this.billiardsStatus = BilliardsStatus.Object;
                } else {
                    this.billiardsStatus = BilliardsStatus.Open;
                }
            }
        }
    }

    initObjectBall() {
        // console.log('===0==GameData.initObjectBall====', this.enterBallList);
        if (!this.enterBallList || this.enterBallList.length < 1) return;
        if (!this.playerObjectBallDic) this.playerObjectBallDic = {};

        let idList1 = [1, 2, 3, 4, 5, 6, 7];
        let idList2 = [9, 10, 11, 12, 13, 14, 15];
        for (let i = 0; i < this.enterBallList.length; i++) {
            let enterId = this.enterBallList[i];
            let index1 = idList1.indexOf(enterId);
            if (index1 > -1) {
                idList1.splice(index1, 1);
            }
            let index2 = idList2.indexOf(enterId);
            if (index2 > -1) {
                idList2.splice(index2, 1);
            }
        }
        idList1.indexOf(8) < 0 && (idList1.unshift(8));
        idList2.indexOf(8) < 0 && (idList2.unshift(8));

        if (this.players) {
            this.players.forEach(element => {
                if (element.ballType == BallType.Solid) {
                    if (element.playerId == this.currentOperate && idList1.length == 1 && this.enterBallList.indexOf(8) > -1) {
                        idList1 = [];
                    }
                    this.playerObjectBallDic[element.playerId] = idList1;
                }
                else if (element.ballType == BallType.Stripe) {
                    if (element.playerId == this.currentOperate && idList2.length == 1 && this.enterBallList.indexOf(8) > -1) {
                        idList2 = [];
                    }
                    this.playerObjectBallDic[element.playerId] = idList2;
                }
            });
        }
        // console.log('===1==GameData.initObjectBall====', this.players);
        // console.log(this.playerObjectBallDic);
    }

    updateHitBall(msg: any) {
        if (msg.hitBall) {
            this.hitBall = JSON.parse(msg.hitBall);
        }
    }

    /** 当前玩家和上一回合玩家不同，则combox 清空 */
    playerChange(msg: any, preIdx) {
        msg.currentOperate && (this.currentOperate = msg.currentOperate);
        msg.hasOwnProperty('combox') && (this.combox = msg.combox || 0);
        msg.hasOwnProperty('noHit') && (this.noHit = msg.noHit);
        msg.hasOwnProperty('totalTime') && (this.totalTime = Math.ceil(msg.totalTime / 1000));
        msg.hasOwnProperty('cdTime') && (this.cdTime = Math.ceil(msg.cdTime / 1000));
        msg.hasOwnProperty('isFoul') && (this.isFoul = msg.isFoul);
        msg.hasOwnProperty('whiteBall') && (this.whiteBall = JSON.parse(msg.whiteBall));
        msg.hasOwnProperty('playBall') && (this.playBall = JSON.parse(msg.playBall));
        msg.hasOwnProperty('cue') && (this.cue = JSON.parse(msg.cue));
        msg.hasOwnProperty('enterBallList') && (this.enterBallList = JSON.parse(msg.enterBallList) || []);
        if (preIdx != this.currentOperate) this.combox = 0;
    }

    setObjectBall(idx: number, ballType: number) {
        if (this.players) {
            this.players.forEach(element => {
                if (idx == element.playerId) {
                    element.ballType = ballType;
                } else {
                    element.ballType = 5 - ballType;
                }
            });
            // console.log('=11==GameData.setObjectBall===', this.players);
        }
        this.initObjectBall();
    }

    /**
     * 获取对局的用户信息
     * @param playerId 传自己的id
     */
    getPlayerData(playerId): PlayerInfo {
        let playerData = null;
        if (this.players) {
            this.players.forEach(element => {
                if (playerId == element.playerId) {
                    playerData = element;
                    return true;
                }
            })
        }
        return playerData;
    }
    /**
     * 获取对局的他人信息
     * @param playerId 传自己的id
     */
    getOtherPlayerData(playerId) {
        let playerData: PlayerInfo = null;
        this.players.forEach(element => {
            if (playerId != element.playerId) {
                playerData = element;
                return true;
            }
        })
        return playerData;
    }

    removeEnterBall(ballId: number) {
        if (!this.enterBallList) return;
        let index = this.enterBallList.indexOf(ballId);
        if (index >= 0) this.enterBallList.splice(index, 1);
    }

    addEnterBall(ballId: number) {
        this.enterBallList.push(ballId);
    }

    get isMyOperate(): boolean {
        return this.currentOperate == Global.userId;
    }

    get isMyFreeBall(): boolean {
        return this.freeBallIdx == Global.userId;
    }

    get myBallType(): number {
        if (this.isWatch) {
            if (this.players[0]) return this.players[0].ballType;
        } else {
            let playerData = this.getPlayerData(Global.userId);
            if (playerData) return playerData.ballType;
        }
        return -1;
    }

    get isWhiteBallInPacket(): boolean {
        if (!this.enterBallList) return false;
        return this.enterBallList.indexOf(0) >= 0;
    }

    getHitballPower(percent: number) {
        let power = this.CuePowerV * percent * this.PowerRange + this.CuePowerInfluence;
        if (this.billiardsStatus == BilliardsStatus.FirstOpen) {
            const randPower = Math.random() * 20;
            power = this.CuePowerV * percent * (this.PowerRange - 20 + randPower) + this.CuePowerInfluence;
            // console.log(percent, randPower, '=====GameData.getHitballPower=====power:', power);
        }
        return power;
    }

    getBallHitBallVol(whiteBalVel) {
        let len = whiteBalVel.length();
        let vol = len / 85 * 0.5 + 0.5;
        return Math.min(Math.max(vol, 0.5), 1);
    }

    isInPacket(ballId): boolean {
        if (this.enterBallList) return this.enterBallList.indexOf(ballId) > -1;
        return false;
    }

    getBallType(num: number): number {
        if (num == 0) return BallType.White;
        if (num == 8) return BallType.Black;
        if (num > 0 && num < 8) return BallType.Solid;
        return BallType.Stripe;
    }

    objectBallList(idx: number): Array<number> {
        if (!this.playerObjectBallDic) return [];
        return this.playerObjectBallDic[idx] || [];
    }

    /**
     * 根据玩家获取进球数
     * 有本地数据，优先用本地数据
     * 断线上来，用服务端数据，如果是赢的一方，且有进8，则进球数是8
     * @param idx 
     * @returns 
     */
    getEnterBallByIdx(player, enterballList = [], ballType = 0): number {
        if (this.playerObjectBallDic && this.playerObjectBallDic[player?.playerId]) {
            let ballList = this.playerObjectBallDic[player?.playerId];
            return 8 - ballList.length;
        }
        let num = 0;
        if (ballType == BallType.Solid || ballType == BallType.Stripe) {
            for (let i = 0; i < enterballList.length; i++) {
                const id = enterballList[i];
                if (this.getBallType(id) == ballType) {
                    num++;
                }
            }
        }
        if (player?.isWinner && enterballList.indexOf(8) > -1) num = 8;
        return num;
    }
    /**结算进球数，如果一方进球且确定球色 但是另一方没有击球，ballType是-1 */
    checkBallType(playerList: Array<PlayerInfo> = []) {
        for (const key in playerList) {
            const element = playerList[key];
            if (element.ballType == BallType.Solid || element.ballType == BallType.Stripe) {
                return element.ballType;
            }
        }
        return 0;
    }

    /**
     * 获取当前在球桌上的所有球
     * @returns 
     */
    getTableBall(): any {
        let ballDic = {};
        for (const key in this.playBall) {
            if (this.enterBallList.indexOf(Number(key)) < 0) {
                ballDic[key] = this.playBall[key];
            }
        }
        return ballDic;
    }

    hasObjectBall(idx: number) {
        let playerData = this.getPlayerData(idx);
        if (playerData) {
            return playerData.ballType == BallType.Solid || playerData.ballType == BallType.Stripe;
        }
        return false;
    }

    updateCombox(roundEnterBallList: Array<number>, isFoul: boolean) {
        if (this.isGameOver || this.isRestart || !this.isMyOperate || !roundEnterBallList || roundEnterBallList.length < 1) {
            this.combox = 0;
            return this.combox;
        }
        if (!this.hasObjectBall(this.currentOperate) || roundEnterBallList.indexOf(0) >= 0 || isFoul) {
            this.combox = 0;
            return this.combox;
        }

        let ballType = this.myBallType;
        let isMyBallType = false;
        for (let i = 0; i < roundEnterBallList.length; i++) {
            let id = roundEnterBallList[i];
            if (this.getBallType(id) == ballType) {
                isMyBallType = true;
            }
        }
        // console.log(isFoul, ':isFoul====GameData.updateCombox=====isMyBallType:', isMyBallType);
        if (isMyBallType) this.combox += 1;
        else this.combox = 0;

        return this.combox;
    }

    checkValidEnterBall(roundEnterBallList: Array<number>): boolean {
        if (!roundEnterBallList || roundEnterBallList.length < 1) {
            return false;
        }
        if (!this.hasObjectBall(this.currentOperate) || roundEnterBallList.indexOf(0) >= 0) {
            return false;
        }
        let ballType = this.myBallType;
        for (let i = 0; i < roundEnterBallList.length; i++) {
            let id = roundEnterBallList[i];
            if (this.getBallType(id) == ballType) {
                return true;
            }
        }
        return false;
    }

    /**没有目标球则不判定是否合理位置 返回false*/
    checkValidPos(pos: any, diameterPacket = 50): any {
        let diameter = this.ballRadius * 2;
        //TODO::检测位置是否超出4个边框（母球打飞） 左 右 下 上
        // console.log("====目标球则不=====",pos);
        if (pos.x < (this.TABLE_POS[0] - diameter) || pos.x > (this.TABLE_POS[1] + diameter) || pos.y < (this.TABLE_POS[2] - diameter) || pos.y > (this.TABLE_POS[3] + diameter))
            return [true, "packet"];

        //是否靠近球
        let ballDataDic = this.playBall;//{id:{x,y}}
        for (const key in ballDataDic) {
            if (this.enterBallList.indexOf(Number(key)) < 0) {
                let element = ballDataDic[key];
                let dis = Utils.getDistance(pos, element);
                if (dis <= (diameter - 1)) return [true, "table"];
            }
        }
        //袋口有效位置检测
        let len = this.packetPosList.length;
        let diameter1 = diameterPacket;//this.ballRadius + this.packetRadius;//球袋半径30
        for (let i = 0; i < len; i++) {
            let dis = Utils.getDistance(pos, { x: this.packetPosList[i][0], y: this.packetPosList[i][1] });
            // console.log(pos,"=距离袋口位置检测=dis="+dis);
            if (dis <= diameter1) return [true, "packet"];
        }
        return [false, ''];
    }
    /**检测是否有效球袋位置  返回球袋tag*/
    checkInPacket(pos: any) {
        //袋口有效位置检测
        let len = this.packetPosList.length;
        let diameter1 = 29;
        for (let i = 0; i < len; i++) {
            let dis = Utils.getDistance(pos, { x: this.packetPosList[i][0], y: this.packetPosList[i][1] });
            // GameUtil.log(pos+ "=距离袋口位置检测=dis=" + dis);
            if (dis <= diameter1) return (i + 1);
        }
        return 0;
    }

    whiteBallRestPos() {
        if (this.billiardsStatus == BilliardsStatus.Object) {
            return v3(0, 0, 0);
        }
        return this.BeginPos_White;
    }

    restartGame() {
        LocalStorage.saveGameIdentity(true);
        this.openBallIsRestart = true;
        this.billiardsStatus = 1
        this.enterBallList = [];
        this.playBall = null;
        this.hitBall = null;
        this.combox = 0;
        this.cue = null;
        this.noHit = null;

        this.whiteBall = null;
        this.event = null;
        this.isFoul = false;
        this.freeBallIdx = 0;
        this.playerObjectBallDic = {};
        this.roundSetObjectBall = false;
        this.isSendBallStop = false;
        this.isGameOver = false;
    }

    gameOver(msg: any) {
        this.isGameOver = true;
    }

    clear() {
        this.billiardsStatus = 1
        this.ballList = null;
        this.playBall = null;
        this.whiteBall = null;
        this.cue = null;
        // this.players = null;
        this.currentOperate = null;
        this.combox = null;
        this.noHit = null;
        this.hitBall = null;
        this.cdTime = 0;
        this.enterBallList = null;
        this.event = null;
        this.isFoul = false;
        this.freeBallIdx = 0;
        this.playerObjectBallDic = {};
        this.isGameOver = false;
        this.isRestart = false;
        this.roundSetObjectBall = false;
        this.isSendBallStop = false;
        this.openBallIsRestart = false;
    }
}

export default new GameData();