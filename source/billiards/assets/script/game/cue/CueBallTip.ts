import { _decorator, Component, Node, RaycastResult2D, v3, Vec2, v2, UITransform, macro, PhysicsSystem2D, ERaycast2DType, PhysicsGroup } from 'cc';
import Utils from '../../core/tools/Utils';
import { BallType } from '../data/Enum';
import GameData from '../data/GameData';
const { ccclass } = _decorator;

@ccclass('CueBallTip')
export class CueBallTip extends Component {
    /**从白球射出的射线 */
    private _tfRay: UITransform = null;
    /**白球撞击位置 */
    private _ndCircle: Node = null;
    /**白球撞击线 */
    private _ndLineH: Node = null;
    private _tfLinH: UITransform = null;
    /**白球反弹线 蓝色线*/
    private _ndLineR: Node = null;
    private _tfLinR: UITransform = null;
    /**非法球 */
    private _forbid: Node = null;

    onLoad() {
        this._tfRay = this.node.getChildByName('ray').getComponent(UITransform);
        this._ndCircle = this.node.getChildByName('circle');
        this._ndLineH = this.node.getChildByName('lineH');
        this._tfLinH = this._ndLineH.getComponent(UITransform);
        this._ndLineR = this.node.getChildByName('lineR');
        this._tfLinR = this._ndLineR.getComponent(UITransform);
        this._forbid = this.node.getChildByName('forbidSet');
    }

    updateUI(startPos: Vec2, result: RaycastResult2D, distance: number) {
        if (!result) return;

        let type = result.collider.TYPE, hitPoint = result.point, hitTargetPos = result.collider.node.worldPosition;
        /**射线方向 */
        let dirI = hitPoint.clone().subtract(startPos).normalize();
        if (result.collider.group == PhysicsGroup['balls']) {
            this._tfRay.height = distance - GameData.rayOffsetY;
            let nodeName = result.collider.node.name;
            let isForbid = false;
            if (nodeName != 'table') {
                let ballId = Number(nodeName);
                isForbid = this.checkForbidBall(ballId);
            }
            if (isForbid) {
                this._forbid.setPosition(0, distance);
            } else {
                this._ndLineH.active = this._ndLineR.active = this._ndCircle.active = true;//result.collider.tag != 1;
                this._ndCircle.setPosition(0, distance);

                /**碰撞方向（法线方向）*/
                let dirH = v2(hitTargetPos.x, hitTargetPos.y).subtract(hitPoint).normalize();
                let angleHDisplay = Math.atan2(dirH.y, dirH.x) * macro.DEG - 90;
                this._ndLineH.setWorldRotationFromEuler(0, 0, angleHDisplay);
                let pointH = dirH.clone().multiplyScalar(30).add(hitPoint);
                this._ndLineH.setWorldPosition(v3(pointH.x, pointH.y, 0));

                /**使用正确的反射公式计算反弹方向 */
                // 反射公式: R = I - 2 * (I · N) * N
                // 其中 I 是入射方向，N 是法线方向，R 是反射方向
                let dotProduct = dirI.dot(dirH); // 入射方向与法线的点积
                let dirR = dirI.subtract(dirH.clone().multiplyScalar(2 * dotProduct)).normalize();

                // 计算投影长度用于辅助线显示
                let projectLen = Math.abs(dotProduct); // 使用点积的绝对值作为投影长度

                // 调试信息：检查反射计算
                let angleI = Math.atan2(dirI.y, dirI.x) * macro.DEG;
                let angleH = Math.atan2(dirH.y, dirH.x) * macro.DEG;
                let angleR = Math.atan2(dirR.y, dirR.x) * macro.DEG;
                let angleDiff = Math.abs(angleR - angleI);
                if (angleDiff > 180) angleDiff = 360 - angleDiff;

                console.log(`[CueBallTip] 反射计算: 入射角=${angleI.toFixed(1)}°, 法线角=${angleH.toFixed(1)}°, 反射角=${angleR.toFixed(1)}°, 角度差=${angleDiff.toFixed(1)}°`);

                // 设置反射线的角度和位置
                let angleRDisplay = angleR - 90; // 显示角度调整
                this._ndLineR.setWorldRotationFromEuler(0, 0, angleRDisplay);
                let pointR = dirR.multiplyScalar(0).add(hitPoint);
                this._ndLineR.setWorldPosition(v3(pointR.x, pointR.y, 0));

                /**根据投影大小设置碰撞线及反弹线长度 */
                GameData.lineH = this._tfLinH.height = GameData.LineValue * projectLen;
                GameData.lineR = this._tfLinR.height = GameData.LineValue * (1 - projectLen);
                console.log(`[CueBallTip] 反弹方向: (${dirR.x.toFixed(3)}, ${dirR.y.toFixed(3)}), 投影长度=${projectLen.toFixed(3)}, 反弹线长度=${this._tfLinR.height.toFixed(1)}`);
            }
        } else {
            this._ndCircle.active = true;
            this._forbid.active = this._ndLineH.active = this._ndLineR.active = false;
            this._ndCircle.setPosition(0, distance - GameData.ballRadius);
            this._tfRay.height = distance - GameData.ballRadius * 2 - GameData.rayOffsetY;
        }
    }

    private checkForbidBall(ballId: number): boolean {
        let ballType = GameData.getBallType(ballId);
        let playerData = GameData.getPlayerData(GameData.currentOperate);
        let isForbid = false;
        if (playerData) {
            let curOperateBallType = playerData.ballType;
            if (curOperateBallType < 0) {
                if (ballId == 8) isForbid = true;
            } else {
                let objectNums = GameData.objectBallList(GameData.currentOperate).length;
                if (objectNums >= 1) {
                    if (objectNums == 1 && ballId == 8) isForbid = false;
                    else {
                        isForbid = (ballType != curOperateBallType) && (curOperateBallType == BallType.Solid || curOperateBallType == BallType.Stripe);
                    }
                }
            }
        }
        this._ndCircle.active = this._ndLineH.active = this._ndLineR.active = !isForbid;
        this._forbid.active = isForbid;
        return isForbid;
    }

    /**
        1.未确定目标球：击球回合开始时，球杆的击球角度为母球与离母球最近的目标球的中点连线的延长线上。 优先离球近

        2.确定目标球：击球回合开始时，球杆尾部放置在里母球最近的那条库边，为并且击球角度为母球与离母球最近的合法目标球的中点连线的延长线上。
                     如果辅助线无法直接触碰到合法目标球，则选取离母球最近的能触碰到合法目标球。如下图所示。
                     如果都无法触碰到合法目标球，则球杆尾部垂直于离母球最近的那条库边。
     */

    /**
     * 开发球局，未确定目标球，回合开始时更新球杆角度
     */
    cueAngleOpenStatus(ballPos, ballDic) {
        let ballkey;
        let minDis = 99999;
        for (const key in ballDic) {
            if (key != '8') {
                let dis = Utils.getDistance(ballPos, ballDic[key]);
                if (dis < minDis) {
                    minDis = dis;
                    ballkey = key;
                }
                // console.log(ballPos, '===cueAngleOpenStatus====', key, dis);
            }
        }
        // console.log(ballkey, ballDic[ballkey], '====cueBallTip.cueAngleOpenStatus===');
        if (ballkey) {
            let targetPos = ballDic[ballkey];
            let rad = Math.atan2(ballPos.y - targetPos.y, ballPos.x - targetPos.x);
            let angle = rad * macro.DEG + 90;
            return [rad, angle];
        }
    }

    /**
     * 击球回合
     * cueBallType 当前玩家目标球类型，也可能是最后一颗黑球类型
     */
    cueAngleHitStatus(whiteBall, ballDic, cueBallType) {
        let ballkey, targetPos, rad, angle;
        let ballPos = whiteBall.node.position;
        let ballWorldPos = whiteBall.node.worldPosition;
        let minDis = 99999;
        for (const key in ballDic) {
            let bType = GameData.getBallType(Number(key))
            if (bType == cueBallType) {
                let dis = Utils.getDistance(ballPos, ballDic[key]);
                if (dis < minDis) {
                    let targetPos = ballDic[key];

                    const rad = Math.atan2(ballPos.y - targetPos.y, ballPos.x - targetPos.x);
                    const dir = v2(Math.cos(rad), Math.sin(rad)).negative();
                    let p1 = v2(ballWorldPos.x, ballWorldPos.y);
                    let p2 = dir.clone().multiplyScalar(1000).add(p1);
                    let results = PhysicsSystem2D.instance.raycast(p1, p2, ERaycast2DType.Closest);

                    // const results = PhysicsSystem2D.instance.raycast(v2(ballPos.x, ballPos.y), v2(targetPos.x, targetPos.y), ERaycast2DType.Closest);
                    // console.log(dis, '--*--', key, '==111==results:', results);
                    // console.log(p1, p2);
                    if (!results || results.length < 1 || (results[0].collider.node && results[0].collider.node.name == key)) {
                        minDis = dis;
                        ballkey = key;
                    }
                }
            }
        }
        // console.log(minDis, cueBallType, '==minDis====ballkey=', ballkey);
        if (!ballkey) {//TODO：：返回的角度可能又问题，导致cue 的 handleCue 直接return 未绘制drawRay
            minDis = 99999;
            //左 右 下 上
            let sidePosList = [
                { x: GameData.TABLE_POS[0], y: ballPos.y },
                { x: GameData.TABLE_POS[1], y: ballPos.y },
                { x: ballPos.x, y: GameData.TABLE_POS[2] },
                { x: ballPos.x, y: GameData.TABLE_POS[3] },
            ];
            sidePosList.forEach(element => {
                let dis = Utils.getDistance(ballPos, element);//Vec3.distance(ballPos, v3(element.x, element.y, 0));
                if (dis < minDis) {
                    minDis = dis;
                    targetPos = element;
                }
            });
            rad = Math.atan2(targetPos.y - ballPos.y, targetPos.x - ballPos.x);

        } else {
            targetPos = ballDic[ballkey];
            rad = Math.atan2(ballPos.y - targetPos.y, ballPos.x - targetPos.x);
        }

        angle = rad * macro.DEG + 90;
        return [rad, angle];
    }
}

