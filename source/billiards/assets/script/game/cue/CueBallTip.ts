import { _decorator, Component, Node, RaycastResult2D, v3, Vec2, v2, UITransform, macro, PhysicsSystem2D, ERaycast2DType, PhysicsGroup } from 'cc';
import Utils from '../../core/tools/Utils';
import { BallType, BilliardsStatus } from '../data/Enum';
import GameData from '../data/GameData';
const { ccclass } = _decorator;

@ccclass('CueBallTip')
export class CueBallTip extends Component {
    /**从白球射出的射线 */
    private _tfRay: UITransform = null;
    /**白球撞击位置 */
    private _ndCircle: Node = null;
    /**白球撞击线 */
    private _ndLineH: Node = null;
    private _tfLinH: UITransform = null;
    /**白球反弹线 蓝色线*/
    private _ndLineR: Node = null;
    private _tfLinR: UITransform = null;
    /**非法球 */
    private _forbid: Node = null;

    onLoad() {
        this._tfRay = this.node.getChildByName('ray').getComponent(UITransform);
        this._ndCircle = this.node.getChildByName('circle');
        this._ndLineH = this.node.getChildByName('lineH');
        this._tfLinH = this._ndLineH.getComponent(UITransform);
        this._ndLineR = this.node.getChildByName('lineR');
        this._tfLinR = this._ndLineR.getComponent(UITransform);
        this._forbid = this.node.getChildByName('forbidSet');
    }

    updateUI(startPos: Vec2, result: RaycastResult2D, distance: number) {
        if (!result) return;
        let type = result.collider.TYPE, hitPoint = result.point, hitTargetPos = result.collider.node.worldPosition;
        /**射线方向 */
        let dirI = hitPoint.clone().subtract(startPos).normalize();

        // 获取当前击球力度，用于轨迹预测修正
        let currentPower = this.getCurrentHitPower();
        let physicsCorrection = this.calculatePhysicsCorrection(currentPower);
        if (result.collider.group == PhysicsGroup['balls']) {
            this._tfRay.height = distance - GameData.rayOffsetY;
            let nodeName = result.collider.node.name;
            let isForbid = false;
            if (nodeName != 'table') {
                let ballId = Number(nodeName);
                isForbid = this.checkForbidBall(ballId);
            }
            if (isForbid) {
                this._forbid.setPosition(0, distance);
            } else {
                this._ndLineH.active = this._ndLineR.active = this._ndCircle.active = true;//result.collider.tag != 1;
                this._ndCircle.setPosition(0, distance);

                /**增强的碰撞方向和反射计算 */
                let ballCenter2D = v2(hitTargetPos.x, hitTargetPos.y);
                let dirH = ballCenter2D.subtract(hitPoint).normalize(); // 法线：从碰撞点指向球心

                // // 验证法线方向
                let dotProduct = dirI.dot(dirH);
                if (dotProduct > 0) {
                    console.warn(`[CueBallTip] 法线方向错误，反转法线`);
                    dirH = dirH.negative();
                    dotProduct = dirI.dot(dirH);
                }

                // 使用正确的反射公式: R = I - 2 * (I · N) * N
                let dirR = dirI.subtract(dirH.clone().multiplyScalar(2 * dotProduct)).normalize();

                // 应用物理修正到反射方向
                dirR = dirR.multiplyScalar(physicsCorrection.restitution);

                let angleH = Math.atan2(dirH.y, dirH.x) * macro.DEG - 90;
                this._ndLineH.setWorldRotationFromEuler(0, 0, angleH);
                let pointH = dirH.clone().multiplyScalar(30).add(hitPoint);
                this._ndLineH.setWorldPosition(v3(pointH.x, pointH.y, 0));

                // 计算投影长度（用于辅助线长度）
                let projectLen = Math.abs(dotProduct);

                let angleI = Math.atan2(dirI.y, dirI.x) * macro.DEG;
                let angleHCalc = Math.atan2(dirH.y, dirH.x) * macro.DEG;
                let angleRCalc = Math.atan2(dirR.y, dirR.x) * macro.DEG;
                console.log(`[CueBallTip] 反射计算: 入射角=${angleI.toFixed(1)}°, 法线角=${angleHCalc.toFixed(1)}°, 反射角=${angleRCalc.toFixed(1)}°`);

                let angleR = Math.atan2(dirR.y, dirR.x) * macro.DEG - 90;
                this._ndLineR.setWorldRotationFromEuler(0, 0, angleR);
                let pointR = dirR.multiplyScalar(0).add(hitPoint);
                this._ndLineR.setWorldPosition(v3(pointR.x, pointR.y, 0));

                /**根据投影大小设置碰撞线及反弹线长度 - 考虑物理修正 */
                // 应用物理修正因子，使辅助线更接近实际轨迹
                let correctedProjectLen = projectLen * physicsCorrection.energyLoss;
                let correctedReflectionLen = (1 - projectLen) * physicsCorrection.restitution;

                GameData.lineH = this._tfLinH.height = GameData.LineValue * correctedProjectLen;
                GameData.lineR = this._tfLinR.height = GameData.LineValue * correctedReflectionLen;

                console.log(`[CueBallTip] 辅助线长度修正: 原始投影=${projectLen.toFixed(3)}, 修正后=${correctedProjectLen.toFixed(3)}, 反射长度=${correctedReflectionLen.toFixed(3)}`);
            }
        } else {
            this._ndCircle.active = true;
            this._forbid.active = this._ndLineH.active = this._ndLineR.active = false;
            this._ndCircle.setPosition(0, distance - GameData.ballRadius);
            this._tfRay.height = distance - GameData.ballRadius * 2 - GameData.rayOffsetY;
        }
    }

    private checkForbidBall(ballId: number): boolean {
        let ballType = GameData.getBallType(ballId);
        let playerData = GameData.getPlayerData(GameData.currentOperate);
        let isForbid = false;
        if (playerData) {
            let curOperateBallType = playerData.ballType;
            if (curOperateBallType < 0) {
                if (ballId == 8) isForbid = true;
            } else {
                let objectNums = GameData.objectBallList(GameData.currentOperate).length;
                if (objectNums >= 1) {
                    if (objectNums == 1 && ballId == 8) isForbid = false;
                    else {
                        isForbid = (ballType != curOperateBallType) && (curOperateBallType == BallType.Solid || curOperateBallType == BallType.Stripe);
                    }
                }
            }
        }
        this._ndCircle.active = this._ndLineH.active = this._ndLineR.active = !isForbid;
        this._forbid.active = isForbid;
        return isForbid;
    }

    /**
        1.未确定目标球：击球回合开始时，球杆的击球角度为母球与离母球最近的目标球的中点连线的延长线上。 优先离球近

        2.确定目标球：击球回合开始时，球杆尾部放置在里母球最近的那条库边，为并且击球角度为母球与离母球最近的合法目标球的中点连线的延长线上。
                     如果辅助线无法直接触碰到合法目标球，则选取离母球最近的能触碰到合法目标球。如下图所示。
                     如果都无法触碰到合法目标球，则球杆尾部垂直于离母球最近的那条库边。
     */

    /**
     * 开球局，未确定目标球，回合开始时更新球杆角度 - 增强版
     */
    cueAngleOpenStatus(ballPos, ballDic) {
        // console.log(`[CueBallTip] 开球状态目标球选择开始`);

        let ballkey;
        let minDis = 99999;
        let validTargets = [];

        // 收集所有有效的目标球（排除黑球8）
        for (const key in ballDic) {
            let ballId = Number(key);
            if (ballId !== 8) { // 开球状态不能瞄准黑球
                let dis = Utils.getDistance(ballPos, ballDic[key]);
                validTargets.push({
                    ballId: ballId,
                    key: key,
                    distance: dis,
                    position: ballDic[key]
                });
                // console.log(`[CueBallTip] 开球目标候选: 球${ballId}, 距离=${dis.toFixed(1)}`);
            }
        }

        // 按距离排序，选择最近的球
        if (validTargets.length > 0) {
            validTargets.sort((a, b) => a.distance - b.distance);

            let bestTarget = validTargets[0];
            ballkey = bestTarget.key;
            minDis = bestTarget.distance;

            // console.log(`[CueBallTip] 开球选择目标球: 球${bestTarget.ballId}, 距离=${minDis.toFixed(1)}`);

            let targetPos = ballDic[ballkey];
            let rad = Math.atan2(ballPos.y - targetPos.y, ballPos.x - targetPos.x);
            let angle = rad * macro.DEG + 90;

            // console.log(`[CueBallTip] 开球角度计算: rad=${rad.toFixed(3)}, angle=${angle.toFixed(1)}°`);
            return [rad, angle];
        } else {
            // console.warn(`[CueBallTip] 开球状态未找到有效目标球！`);
            return null;
        }
    }

    /**
     * 击球回合 - 增强版目标球选择
     * cueBallType 当前玩家目标球类型，也可能是最后一颗黑球类型
     */
    cueAngleHitStatus(whiteBall, ballDic, cueBallType) {
        // console.log(`[CueBallTip] 开始目标球选择: cueBallType=${cueBallType}, 游戏状态=${GameData.billiardsStatus}`);

        let ballkey, targetPos, rad, angle;
        let ballPos = whiteBall.node.position;
        let ballWorldPos = whiteBall.node.worldPosition;
        let minDis = 99999;

        // 获取当前玩家的目标球列表
        let playerObjectBalls = GameData.objectBallList(GameData.currentOperate);
        let objNums = playerObjectBalls.length;
        // console.log(`[CueBallTip] 当前玩家目标球列表:`, playerObjectBalls);

        // 增强的目标球选择逻辑
        let validTargets = [];

        for (const key in ballDic) {
            let ballId = Number(key);
            let bType = GameData.getBallType(ballId);

            // 检查是否是有效的目标球
            let isValidTarget = false;

            if (GameData.billiardsStatus === BilliardsStatus.Object) {
                // 已确定目标球状态：检查是否是玩家的目标球
                if (playerObjectBalls.indexOf(ballId) >= 0) {
                    if (ballId == 8 && objNums > 1) continue;
                    isValidTarget = true;
                    // console.log(`[CueBallTip] 球${ballId}是玩家目标球`);
                }
            } else {
                // 开放状态：所有非白球、非黑球都是潜在目标
                if (bType === cueBallType && ballId !== 8) {
                    isValidTarget = true;
                    // console.log(`[CueBallTip] 球${ballId}是开放状态的有效目标`);
                }
            }

            if (isValidTarget) {
                let dis = Utils.getDistance(ballPos, ballDic[key]);

                // 射线检测：检查是否可以直接击打
                let targetPos = ballDic[key];
                const rad = Math.atan2(ballPos.y - targetPos.y, ballPos.x - targetPos.x);
                const dir = v2(Math.cos(rad), Math.sin(rad)).negative();
                let p1 = v2(ballWorldPos.x, ballWorldPos.y);
                let p2 = dir.clone().multiplyScalar(1000).add(p1);
                let results = PhysicsSystem2D.instance.raycast(p1, p2, ERaycast2DType.Closest);

                let canDirectHit = false;
                if (!results || results.length < 1) {
                    canDirectHit = true;
                } else if (results[0].collider.node && results[0].collider.node.name == key) {
                    canDirectHit = true;
                }

                validTargets.push({
                    ballId: ballId,
                    key: key,
                    distance: dis,
                    canDirectHit: canDirectHit,
                    ballType: bType,
                    position: targetPos
                });

                // console.log(`[CueBallTip] 目标球${ballId}: 距离=${dis.toFixed(1)}, 可直击=${canDirectHit}`);
            }
        }

        // 选择最佳目标球
        if (validTargets.length > 0) {
            // 优先选择可以直接击打的球，然后按距离排序
            validTargets.sort((a, b) => {
                if (a.canDirectHit && !b.canDirectHit) return -1;
                if (!a.canDirectHit && b.canDirectHit) return 1;
                return a.distance - b.distance;
            });

            let bestTarget = validTargets[0];
            ballkey = bestTarget.key;
            minDis = bestTarget.distance;

            // console.log(`[CueBallTip] 选择最佳目标球: 球${bestTarget.ballId}, 距离=${minDis.toFixed(1)}, 可直击=${bestTarget.canDirectHit}`);
        } else {
            // console.warn(`[CueBallTip] 没有找到有效的目标球！`);
        }
        // console.log(minDis, cueBallType, '==minDis====ballkey=', ballkey);
        if (!ballkey) {//TODO：：返回的角度可能又问题，导致cue 的 handleCue 直接return 未绘制drawRay
            minDis = 99999;
            //左 右 下 上
            let sidePosList = [
                { x: GameData.TABLE_POS[0], y: ballPos.y },
                { x: GameData.TABLE_POS[1], y: ballPos.y },
                { x: ballPos.x, y: GameData.TABLE_POS[2] },
                { x: ballPos.x, y: GameData.TABLE_POS[3] },
            ];
            sidePosList.forEach(element => {
                let dis = Utils.getDistance(ballPos, element);//Vec3.distance(ballPos, v3(element.x, element.y, 0));
                if (dis < minDis) {
                    minDis = dis;
                    targetPos = element;
                }
            });
            rad = Math.atan2(targetPos.y - ballPos.y, targetPos.x - ballPos.x);

        } else {
            targetPos = ballDic[ballkey];
            rad = Math.atan2(ballPos.y - targetPos.y, ballPos.x - targetPos.x);
        }

        angle = rad * macro.DEG + 90;
        return [rad, angle];
    }

    /**
     * 获取当前击球力度
     */
    private getCurrentHitPower(): number {
        // 从游戏数据或滑动条获取当前力度
        // 这里使用默认值，实际应该从力度控制组件获取
        return 50; // 默认中等力度
    }

    /**
     * 计算物理修正因子
     * 用于使辅助线更接近实际物理轨迹
     */
    private calculatePhysicsCorrection(power: number): {energyLoss: number, restitution: number, damping: number} {
        // 基于实际物理参数计算修正因子
        const normalizedPower = Math.max(0, Math.min(100, power)) / 100; // 归一化到0-1

        // 能量损失因子：力度越大，能量保持越好
        const energyLoss = 0.85 + (normalizedPower * 0.1); // 0.85-0.95

        // 反弹系数：考虑实际的反弹力
        const restitution = GameData.Restitution_White * (0.9 + normalizedPower * 0.1); // 基于实际反弹力参数

        // 阻尼因子：考虑线性阻尼和摩擦力
        const damping = 1 - (GameData.ballLinearDamp * 0.5 + GameData.ballFriction * 0.3);

        console.log(`[CueBallTip] 物理修正计算: 力度=${power}, 能量损失=${energyLoss.toFixed(3)}, 反弹系数=${restitution.toFixed(3)}, 阻尼=${damping.toFixed(3)}`);

        return {
            energyLoss: energyLoss,
            restitution: restitution,
            damping: damping
        };
    }
}

