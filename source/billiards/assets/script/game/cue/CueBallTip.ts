import { _decorator, Component, Node, RaycastResult2D, v3, Vec2, v2, UITransform, macro, PhysicsSystem2D, ERaycast2DType, PhysicsGroup } from 'cc';
import Utils from '../../core/tools/Utils';
import { BallType, BilliardsStatus } from '../data/Enum';
import GameData from '../data/GameData';
const { ccclass } = _decorator;

@ccclass('CueBallTip')
export class CueBallTip extends Component {
    /**从白球射出的射线 */
    private _tfRay: UITransform = null;
    /**白球撞击位置 */
    private _ndCircle: Node = null;
    /**白球撞击线 */
    private _ndLineH: Node = null;
    private _tfLinH: UITransform = null;
    /**白球反弹线 蓝色线*/
    private _ndLineR: Node = null;
    private _tfLinR: UITransform = null;
    /**非法球 */
    private _forbid: Node = null;

    onLoad() {
        this._tfRay = this.node.getChildByName('ray').getComponent(UITransform);
        this._ndCircle = this.node.getChildByName('circle');
        this._ndLineH = this.node.getChildByName('lineH');
        this._tfLinH = this._ndLineH.getComponent(UITransform);
        this._ndLineR = this.node.getChildByName('lineR');
        this._tfLinR = this._ndLineR.getComponent(UITransform);
        this._forbid = this.node.getChildByName('forbidSet');
    }

    updateUI(startPos: Vec2, result: RaycastResult2D, distance: number) {
        if (!result) return;

        let type = result.collider.TYPE, hitPoint = result.point, hitTargetPos = result.collider.node.worldPosition;
        /**射线方向 */
        let dirI = hitPoint.clone().subtract(startPos).normalize();
        if (result.collider.group == PhysicsGroup['balls']) {
            this._tfRay.height = distance - GameData.rayOffsetY;
            let nodeName = result.collider.node.name;
            let isForbid = false;
            if (nodeName != 'table') {
                let ballId = Number(nodeName);
                isForbid = this.checkForbidBall(ballId);
            }
            if (isForbid) {
                this._forbid.setPosition(0, distance);
            } else {
                this._ndLineH.active = this._ndLineR.active = this._ndCircle.active = true;//result.collider.tag != 1;
                this._ndCircle.setPosition(0, distance);

                /**碰撞方向 */
                let dirH = v2(hitTargetPos.x, hitTargetPos.y).subtract(hitPoint).normalize();
                let angleH = Math.atan2(dirH.y, dirH.x) * macro.DEG - 90;
                this._ndLineH.setWorldRotationFromEuler(0, 0, angleH);
                let pointH = dirH.clone().multiplyScalar(30).add(hitPoint);
                this._ndLineH.setWorldPosition(v3(pointH.x, pointH.y, 0));

                /**射线方向在碰撞方向上的投影 */
                let vecH = dirI.clone().project(dirH);
                /**反弹方向 */
                let dirR = dirI.subtract(vecH).normalize();
                let projectLen = vecH.length();

                let angleR = Math.atan2(dirR.y, dirR.x) * macro.DEG - 90;
                this._ndLineR.setWorldRotationFromEuler(0, 0, angleR);
                let pointR = dirR.multiplyScalar(0).add(hitPoint);
                this._ndLineR.setWorldPosition(v3(pointR.x, pointR.y, 0));

                /**根据投影大小设置碰撞线及反弹线长度 */
                GameData.lineH = this._tfLinH.height = GameData.LineValue * projectLen;
                GameData.lineR = this._tfLinR.height = GameData.LineValue * (1 - projectLen);
            }
        } else {
            this._ndCircle.active = true;
            this._forbid.active = this._ndLineH.active = this._ndLineR.active = false;
            this._ndCircle.setPosition(0, distance - GameData.ballRadius);
            this._tfRay.height = distance - GameData.ballRadius * 2 - GameData.rayOffsetY;
        }
    }

    private checkForbidBall(ballId: number): boolean {
        let ballType = GameData.getBallType(ballId);
        let playerData = GameData.getPlayerData(GameData.currentOperate);
        let isForbid = false;
        if (playerData) {
            let curOperateBallType = playerData.ballType;
            if (curOperateBallType < 0) {
                if (ballId == 8) isForbid = true;
            } else {
                let objectNums = GameData.objectBallList(GameData.currentOperate).length;
                if (objectNums >= 1) {
                    if (objectNums == 1 && ballId == 8) isForbid = false;
                    else {
                        isForbid = (ballType != curOperateBallType) && (curOperateBallType == BallType.Solid || curOperateBallType == BallType.Stripe);
                    }
                }
            }
        }
        this._ndCircle.active = this._ndLineH.active = this._ndLineR.active = !isForbid;
        this._forbid.active = isForbid;
        return isForbid;
    }

    /**
        1.未确定目标球：击球回合开始时，球杆的击球角度为母球与离母球最近的目标球的中点连线的延长线上。 优先离球近

        2.确定目标球：击球回合开始时，球杆尾部放置在里母球最近的那条库边，为并且击球角度为母球与离母球最近的合法目标球的中点连线的延长线上。
                     如果辅助线无法直接触碰到合法目标球，则选取离母球最近的能触碰到合法目标球。如下图所示。
                     如果都无法触碰到合法目标球，则球杆尾部垂直于离母球最近的那条库边。
     */

    /**
     * 开球局，未确定目标球，回合开始时更新球杆角度 - 增强版
     */
    cueAngleOpenStatus(ballPos, ballDic) {
        // console.log(`[CueBallTip] 开球状态目标球选择开始`);

        let ballkey;
        let minDis = 99999;
        let validTargets = [];

        // 收集所有有效的目标球（排除黑球8）
        for (const key in ballDic) {
            let ballId = Number(key);
            if (ballId !== 8) { // 开球状态不能瞄准黑球
                let dis = Utils.getDistance(ballPos, ballDic[key]);
                validTargets.push({
                    ballId: ballId,
                    key: key,
                    distance: dis,
                    position: ballDic[key]
                });
                // console.log(`[CueBallTip] 开球目标候选: 球${ballId}, 距离=${dis.toFixed(1)}`);
            }
        }

        // 按距离排序，选择最近的球
        if (validTargets.length > 0) {
            validTargets.sort((a, b) => a.distance - b.distance);

            let bestTarget = validTargets[0];
            ballkey = bestTarget.key;
            minDis = bestTarget.distance;

            // console.log(`[CueBallTip] 开球选择目标球: 球${bestTarget.ballId}, 距离=${minDis.toFixed(1)}`);

            let targetPos = ballDic[ballkey];
            let rad = Math.atan2(ballPos.y - targetPos.y, ballPos.x - targetPos.x);
            let angle = rad * macro.DEG + 90;

            // console.log(`[CueBallTip] 开球角度计算: rad=${rad.toFixed(3)}, angle=${angle.toFixed(1)}°`);
            return [rad, angle];
        } else {
            // console.warn(`[CueBallTip] 开球状态未找到有效目标球！`);
            return null;
        }
    }

    /**
     * 击球回合 - 增强版目标球选择
     * cueBallType 当前玩家目标球类型，也可能是最后一颗黑球类型
     */
    cueAngleHitStatus(whiteBall, ballDic, cueBallType) {
        // console.log(`[CueBallTip] 开始目标球选择: cueBallType=${cueBallType}, 游戏状态=${GameData.billiardsStatus}`);

        let ballkey, targetPos, rad, angle;
        let ballPos = whiteBall.node.position;
        let ballWorldPos = whiteBall.node.worldPosition;
        let minDis = 99999;

        // 获取当前玩家的目标球列表
        let playerObjectBalls = GameData.objectBallList(GameData.currentOperate);
        let objNums = playerObjectBalls.length;
        // console.log(`[CueBallTip] 当前玩家目标球列表:`, playerObjectBalls);

        // 增强的目标球选择逻辑
        let validTargets = [];

        for (const key in ballDic) {
            let ballId = Number(key);
            let bType = GameData.getBallType(ballId);

            // 检查是否是有效的目标球
            let isValidTarget = false;

            if (GameData.billiardsStatus === BilliardsStatus.Object) {
                // 已确定目标球状态：检查是否是玩家的目标球
                if (playerObjectBalls.indexOf(ballId) >= 0) {
                    if (ballId == 8 && objNums > 1) continue;
                    isValidTarget = true;
                    // console.log(`[CueBallTip] 球${ballId}是玩家目标球`);
                }
            } else {
                // 开放状态：所有非白球、非黑球都是潜在目标
                if (bType === cueBallType && ballId !== 8) {
                    isValidTarget = true;
                    // console.log(`[CueBallTip] 球${ballId}是开放状态的有效目标`);
                }
            }

            if (isValidTarget) {
                let dis = Utils.getDistance(ballPos, ballDic[key]);

                // 射线检测：检查是否可以直接击打
                let targetPos = ballDic[key];
                const rad = Math.atan2(ballPos.y - targetPos.y, ballPos.x - targetPos.x);
                const dir = v2(Math.cos(rad), Math.sin(rad)).negative();
                let p1 = v2(ballWorldPos.x, ballWorldPos.y);
                let p2 = dir.clone().multiplyScalar(1000).add(p1);
                let results = PhysicsSystem2D.instance.raycast(p1, p2, ERaycast2DType.Closest);

                let canDirectHit = false;
                if (!results || results.length < 1) {
                    canDirectHit = true;
                } else if (results[0].collider.node && results[0].collider.node.name == key) {
                    canDirectHit = true;
                }

                validTargets.push({
                    ballId: ballId,
                    key: key,
                    distance: dis,
                    canDirectHit: canDirectHit,
                    ballType: bType,
                    position: targetPos
                });

                // console.log(`[CueBallTip] 目标球${ballId}: 距离=${dis.toFixed(1)}, 可直击=${canDirectHit}`);
            }
        }

        // 选择最佳目标球
        if (validTargets.length > 0) {
            // 优先选择可以直接击打的球，然后按距离排序
            validTargets.sort((a, b) => {
                if (a.canDirectHit && !b.canDirectHit) return -1;
                if (!a.canDirectHit && b.canDirectHit) return 1;
                return a.distance - b.distance;
            });

            let bestTarget = validTargets[0];
            ballkey = bestTarget.key;
            minDis = bestTarget.distance;

            // console.log(`[CueBallTip] 选择最佳目标球: 球${bestTarget.ballId}, 距离=${minDis.toFixed(1)}, 可直击=${bestTarget.canDirectHit}`);
        } else {
            // console.warn(`[CueBallTip] 没有找到有效的目标球！`);
        }
        // console.log(minDis, cueBallType, '==minDis====ballkey=', ballkey);
        if (!ballkey) {//TODO：：返回的角度可能又问题，导致cue 的 handleCue 直接return 未绘制drawRay
            minDis = 99999;
            //左 右 下 上
            let sidePosList = [
                { x: GameData.TABLE_POS[0], y: ballPos.y },
                { x: GameData.TABLE_POS[1], y: ballPos.y },
                { x: ballPos.x, y: GameData.TABLE_POS[2] },
                { x: ballPos.x, y: GameData.TABLE_POS[3] },
            ];
            sidePosList.forEach(element => {
                let dis = Utils.getDistance(ballPos, element);//Vec3.distance(ballPos, v3(element.x, element.y, 0));
                if (dis < minDis) {
                    minDis = dis;
                    targetPos = element;
                }
            });
            rad = Math.atan2(targetPos.y - ballPos.y, targetPos.x - ballPos.x);

        } else {
            targetPos = ballDic[ballkey];
            rad = Math.atan2(ballPos.y - targetPos.y, ballPos.x - targetPos.x);
        }

        angle = rad * macro.DEG + 90;
        return [rad, angle];
    }
}

