import { _decorator, Component, UITransform, EventTouch, Vec2, PhysicsSystem2D, ERaycast2DType, v2, view, Node, RaycastResult2D, macro, Sprite, color, Input } from 'cc';
import BallCtrl from '../ball/BallCtrl';
import GameData from '../data/GameData';
import { WhiteBall } from '../ball/WhiteBall';
import { CueBallTip } from './CueBallTip';
import GameService from '../data/GameService';
import { BilliardsStatus } from '../data/Enum';
import Global from '../../core/data/Global';
import AudioMgr from '../../core/tools/AudioMgr';
import Utils from '../../core/tools/Utils';
import { EnumEvent, EventMgr } from '../../core/event/EventManager';
const { ccclass } = _decorator;

@ccclass('Cue')
export class Cue extends Component {
    private _cueSp: Sprite = null;
    private _cueBall: WhiteBall = null;
    private _transform: UITransform = null;

    private _rayLine: Node = null;
    private _rayLineTransform: UITransform = null;
    private _cueBallTip: CueBallTip = null;
    private _hitBall: Node = null;
    private _hitTimeOut;

    private _dir: Vec2 = null;
    private _rawCueAnchorY = 1.05;
    public _rad: number = 0;
    private _isDrag: boolean = false;
    private _color255 = color(255, 255, 255, 255);
    private _color125 = color(255, 255, 255, 125);
    private _color0 = color(255, 255, 255, 0);

    private touchNode: Node = null;//触摸节点

    private rotationSpeed: number = 4; // 旋转速度系数
    private rotationLimit: number = 360; // 旋转限制角度（0表示无限制）

    private isRotating: boolean = false; // 是否正在旋转
    private currentAngle: number = 0; // 当前旋转角度
    private targetAngle: number = 0; // 目标旋转角度
    private lastMouseAngle: number = 0; // 上次鼠标角度

    onLoad() {
        this._rad = 0;
        this._cueSp = this.getComponent(Sprite);

        this._transform = this.getComponent(UITransform);
        this._cueBallTip = this.node.getComponent(CueBallTip);
        this._rayLine = this.node.getChildByName('ray');
        this._rayLineTransform = this._rayLine.getComponent(UITransform);
        this._hitBall = this.node.getChildByName('hitball');

        this.touchNode = this.node.parent.getChildByName('touchNode');
        this.touchNode.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
        this.touchNode.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.touchNode.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        this.touchNode.on(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);

        EventMgr.addEventListener(EnumEvent.CUSTOM_CUE_POSITION_UPDATE, this.updateCueInfo, this);
    }

    init(cueBall: WhiteBall) {
        this._cueBall = cueBall;
        this.resetCue();
        this.updateCuePos();
        this._transform.anchorY = this._rawCueAnchorY;
        this._rayLine.setPosition(0, GameData.rayOffsetY);
        this.isDrag = GameData.isMyOperate;
        if (!GameData.isMyOperate) {
            this._cueSp.color = this._color125;
        }
    }

    /**滑动 方向杆 */
    fineTurning(deltaAngle) {
        this.node.angle += deltaAngle;
        let rad = (this.angle - 90) * Math.PI / 180;
        this.rayCast(rad, this.angle, true);
    }

    /**
     * TODO::还要结合是否自己操作状态，这里暂时不考虑
     */
    get canOperate(): boolean {
        if (!this._cueBall) return false;
        if (GameData.isGameOver) return false;
        if (this._cueBall.isValidPos) return false;
        return GameData.isMyOperate;
    }

    /**更改球杆锚点 */
    changeCueAnchorY(anchorY: number) {
        this._transform.anchorY = anchorY + this._rawCueAnchorY;
    }

    /**
     * 球杆射击白球
     * @param power 射击力度
     */
    shootCueBall(power: number, percent: number) {
        this.ballHit(power, percent);
    }

    /**开球、移动球杆执行此方法 */
    updateCueInfo(eventName: string, isShow: any) {
        this.clearHitTimeOut();
        this._cueSp.color = isShow ? this._color255 : this._color0;
        this.updateCuePos();
        if (!isShow) return;
        this._transform.anchorY = this._rawCueAnchorY;
        this.setRad();
        this.rayCast(this._rad, this.angle);
    }

    onTouchStart(event: EventTouch) {
        if (!this.canOperate || !this._isDrag) return;
        this._cueSp.color = this._color255;

        const mousePos = event.getLocation();
        const ballPos = this._cueBall.node.worldPosition;
        const distance = this.distance(mousePos, ballPos);
        if (distance < 30) { // 点击范围判断
            this.lastMouseAngle = this.currentAngle; // 记录当前角度作为蓄力起始角度
        } else {
            // 记录当前鼠标位置对应的角度
            this.isRotating = true;
            this.lastMouseAngle = Math.atan2(mousePos.y - ballPos.y, mousePos.x - ballPos.x) * 180 / Math.PI;
        }
    }

    onTouchMove(event) {
        if (!this._cueSp.color.a || !this.canOperate || !this.isRotating) return;

        const x = event.getLocationX() - this._cueBall.node.worldPosition.x;
        const y = event.getLocationY() - this._cueBall.node.worldPosition.y;

        // 计算新的鼠标角度
        const newMouseAngle = Math.atan2(y, x) * 180 / Math.PI;

        // 计算角度变化量
        let angleDelta = newMouseAngle - this.lastMouseAngle;

        // 确保走最短路径
        if (angleDelta > 180) angleDelta -= 360;
        if (angleDelta < -180) angleDelta += 360;

        // 更新目标角度（从当前角度开始增加变化量）
        this.targetAngle = this.currentAngle + angleDelta;

        // 应用旋转限制
        if (this.rotationLimit > 0 && this.rotationLimit < 360) {
            this.targetAngle = this.clampAngle(this.targetAngle, this.rotationLimit);
        }

        // 更新上次鼠标角度
        this.lastMouseAngle = newMouseAngle;
    }

    onTouchEnd(event = null) {
        this.isRotating = false;
        if (!this._cueSp.color.a || !this.canOperate || !this._isDrag) return;
        this.updateCueRotation();
    }

    update(deltaTime: number) {
        if (!this.canOperate || !this._isDrag || !this.isRotating) return;
        // 平滑旋转到目标角度
        if (this.targetAngle !== this.currentAngle) {
            // 计算最短旋转路径
            let deltaAngle = this.targetAngle - this.currentAngle;

            // 确保走最短路径
            if (deltaAngle > 180) deltaAngle -= 360;
            if (deltaAngle < -180) deltaAngle += 360;

            // 应用旋转速度
            const step = Math.min(Math.abs(deltaAngle), this.rotationSpeed * deltaTime * 60);

            // 根据方向增加或减少角度
            this.currentAngle += deltaAngle > 0 ? step : -step;

            // 更新球杆旋转
            this.updateCueRotation();
        }
    }

    // 设置球杆角度
    updateCueRotation() {
        this.node.angle = this.currentAngle;
        this._rad = (this.node.angle - 90) * Math.PI / 180;
        this.rayCast(this._rad, this.angle, true);
    }

    // 角度限制函数
    clampAngle(angle: number, limit: number): number {
        // 将角度归一化到-180到180度
        while (angle > 180) angle -= 360;
        while (angle < -180) angle += 360;

        // 应用限制
        const halfLimit = limit / 2;
        if (angle > halfLimit) angle = halfLimit;
        if (angle < -halfLimit) angle = -halfLimit;

        return angle;
    }
    // 计算两个向量之间的距离
    distance(a, b): number {
        const dx = a.x - b.x;
        const dy = a.y - b.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    rayCast(rad: number, angle: number, isSendMsg = false) {
        this.handleCue(rad, angle);
        if (isSendMsg) {
            GameService.instance.DragMoveRequest({
                cue: JSON.stringify({ rad: rad, angle: angle }),
                whiteBall: JSON.stringify({ x: this.ballPos.x, y: this.ballPos.y })
            })
        }
    }
    drawRay(startPos: Vec2, result: RaycastResult2D) {
        let distance = this.distance(startPos, result.point);
        this._cueBallTip.updateUI(startPos, result, distance);
    }

    setRad(e = null): number {
        const location = e ? Utils.getUILocation(e) : v2(view.getDesignResolutionSize().width * 0.5, 0);
        const cueBallPos = this._cueBall.node.worldPosition;
        const rad = Math.atan2(location.y - cueBallPos.y, location.x - cueBallPos.x);
        const angle = rad * macro.DEG + 90;
        this.angle = angle;
        this._rad = rad;
        return rad;
    }

    stopDrag() {
        this.isRotating = false;
        this._isDrag = false;
    }

    handleCue(rad: number, angle: number) {
        if (!PhysicsSystem2D.instance.enable || GameData.isGameOver || !this._cueBall) return;
        this._rad = rad;
        this.angle = angle;
        this._dir = v2(Math.cos(rad), Math.sin(rad)).negative();
        let cueBallPos = this._cueBall.node.getWorldPosition();
        let p1 = v2(cueBallPos.x, cueBallPos.y);
        let p2 = this._dir.clone().multiplyScalar(1000).add(p1);
        let results = PhysicsSystem2D.instance.raycast(p1, p2, ERaycast2DType.Closest);
        if (!results || results.length < 1) return
        this.drawRay(p1, results[0]);
    }
//hitball.hitImpulse.x, hitball.cue.angle, hitball.cue.rad, hitball.hitImpulse.percent
    handleBallHit(hitball) {
        let power = hitball.hitImpulse.x;
        let angle = hitball.cue.angle;
        let rad = hitball.cue.rad;
        let percent = hitball.hitImpulse.percent;
        let dir = hitball.cue.dir;
        this._rad = rad;
        if (this.angle != angle) this.angle = angle;
        this._dir = v2(dir.x, dir.y);//v2(Math.cos(rad), Math.sin(rad)).negative();
        this.ballHit(power, percent);
    }

    ballHit(power: number, percent: number = 1) {
        this.clearHitTimeOut();
        this.isDrag = false;
        this._transform.anchorY = 1;
        let impulse = this._dir.multiplyScalar(power);
        this._cueBall.rb2D.applyLinearImpulseToCenter(impulse, true);
        BallCtrl.shootCue();

        let self = this;
        if (Global.isResume) {
            this._hitBall.active = true;
            this._hitTimeOut = setTimeout(() => {
                self._hitBall.active = false;
                self.updateActive(false);
            }, 100);
        } else {
            this.updateActive(false);
        }

        AudioMgr.playEffect(AudioMgr.audios.cueHitBall, percent);
    }

    /**球停，需要更新球杆角度 切换到下一个用户 */
    ballStop_updateCue(billiardsStatus, ballDic, cueBallType = '') {
        if (GameData.isGameOver || BallCtrl.isOtherView()) return;
        let result;
        const cueBallPos = this._cueBall?.node.position;
        if (billiardsStatus == BilliardsStatus.Object) {
            result = this._cueBallTip.cueAngleHitStatus(this._cueBall, ballDic, cueBallType);
        } else {
            result = this._cueBallTip.cueAngleOpenStatus(cueBallPos, ballDic);
        }
        // GameUtil.log((this._cueSp.color == this._color255) + ' ' + (this._cueSp.color == this._color255) + "=====Cue 球停=");
        // GameUtil.log(result);
        if (result) {
            //TODO::再开一局，当前自己操作，可是球杆的色值是0，不可见
            this._cueSp.color = GameData.isMyOperate ? this._color255 : this._color125;
            this.handleCue(result[0], result[1]);
        }
    }

    updateActive(isShow: boolean) {
        this._cueSp.color = isShow ? this._color255 : this._color0;
        if (!isShow) this._rayLineTransform.height = 0;
    }

    playerChange(isMyOperate: boolean) {
        this.clearHitTimeOut(); //TODO::手机卡顿情况下，会出现击球后的settimeout执行晚于playerchange，导致球杆不显示
        this._cueSp.color = isMyOperate ? this._color255 : this._color125;
        this.isDrag = isMyOperate;
        this.updateCuePos();
        this._transform.anchorY = this._rawCueAnchorY;
    }

    updateCuePos() {
        this._cueBall && this.node.setPosition(this._cueBall.x, this._cueBall.y, 0);
    }

    set isDrag(v) {
        this._isDrag = v;
    }

    get rad() {
        return this._rad;
    }

    get angle() {
        return this.node.angle;
    }
    set angle(v) {
        this.node.angle = v;
    }

    get ballPos() {
        return this._cueBall.node.position;
    }

    get isVisible() {
        return this.node.active && (this._cueSp.color == this._color255);
    }

    get dir() {
        return this._dir;
    }

    clearHitTimeOut() {
        if (this._hitTimeOut) {
            this.updateActive(false);
            this._hitBall.active = false;
            clearTimeout(this._hitTimeOut);
            //TODO::弱网情况下，发送击球情况下，消息顺序可能不对，先收到切换用户，击球响应可能收不到
            this._hitTimeOut = 0;
        }
    }

    resetCue() {
        this.currentAngle = 0;
        this.targetAngle = 0;
        this.lastMouseAngle = 0;
    }

    clear() {
        clearTimeout(this._hitTimeOut);
        this._hitTimeOut = 0;
        if (this.touchNode?.isValid) {
            this.touchNode.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
            this.touchNode.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
            this.touchNode.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);
            this.touchNode.off(Input.EventType.TOUCH_CANCEL, this.onTouchEnd, this);
        }
        EventMgr.removeEventListener(EnumEvent.CUSTOM_CUE_POSITION_UPDATE, this.updateCueInfo, this);
    }

    onDestroy() {
        this.clear();
    }
}

