import { _decorator, Prefab, instantiate, Label, PhysicsSystem2D, director, Sprite, UITransform, Widget, EditBox, Material, SpriteFrame } from 'cc';
import { Ball } from './ball/Ball';
import { Cue } from './cue/Cue';
import { Table } from './Table';
import BallCtrl from './ball/BallCtrl';
import GameData from './data/GameData';
import GameService from './data/GameService';
import Global from '../core/data/Global';
import { Sliderbar } from './Sliderbar';
import TipManager from './manager/TipManager';
import { AllBallStopEvent, BilliardsStatus } from './data/Enum';
import Utils from '../core/tools/Utils';
import { Random } from '../core/tools/Math';
import LocalStorage from '../core/tools/LocalStorage';
import { FineTurning } from './FineTurning';
import BaseTipMgr from '../core/tools/BaseTipMgr';
import i18n from '../core/tools/i18n';
import { UIView } from '../core/ui/UIView';
import { uiManager } from '../core/ui/UIManager';
import { UIID } from '../Main';
import { PlayerItem } from './player/PlayerItem';
import { GameUtil } from '../core/tools/GameUtil';
import AudioMgr from '../core/tools/AudioMgr';
import { EnumEvent, EventMgr } from '../core/event/EventManager';
import { dialogManager } from '../common/dialog/DialogManager';
import { JsbBrid } from '../core/tools/JsbBridge';
import HitBallRuleMgr from './ball/HitBallRuleMgr';

const { ccclass, property } = _decorator;

@ccclass('Game')
export class Game extends UIView {
    //http://************:7456/?debug=1&userid=100576296&roomid=104126530
    //http://************:7456/?debug=1&userid=100576295&roomid=104126530

    //http://************:7456/?debug=1&userid=104126614&roomid=104126614
    //http://************:7456/?debug=1&userid=104126582&roomid=104126614

    //测试服
    // http://************:7456/?debug=1&userid=100601470&roomid=100601470
    //http://************:7456/?debug=1&userid=100601468&roomid=100601470

    /**
         * 1. 开球玩家可以在开球区中调整母球位置(未击球前，可以继续调整球位置)
         * 2. 调整玩位置后（松开母球），母球缩小在调整的位置上，球杆出现，调整位置的提示手势消失，球杆与辅助线的方向为母球与离母球最近的目标球的连线
         * 3. 球局开放时，玩家将两类球无犯规同时击打入袋，则判定优先入袋的种类为合法目标球
         * 
         * 犯规：开局： 击杆后母球入袋或碰触台边的目标球数少于4颗；
         *      击球： 倒计时结束没有击球；先集中对方球；目标球>1时 集中8号球；母球先碰库再碰球但是母球和目标球都没有弹库；母球入袋
         * 
         * 重新开局：开放球局中，未出现目标球，把黑球入袋 游戏结束； 出现目标球后，目标球>1,黑球入袋 游戏结束
         * 
         * 开局：只要双方都没有击杆，都可以再1/4区域拖动母球
     */

    @property(Prefab)
    private pfBall: Prefab = null;

    @property([Prefab])
    private playerPrefabs = [];

    // @property([Texture2D])
    // private ballTextures = [];

    @property(Cue)
    private cue: Cue = null;

    @property(Table)
    private table: Table = null;

    @property(Sliderbar)
    private sliderbar: Sliderbar = null;

    @property(Label)
    private txtWinGold: Label = null;

    @property(Material)
    b1: Material = null;
    @property(Material)
    b2: Material = null;

    @property([SpriteFrame])
    private textures = [];
    
    // @property([Prefab])
    // private tipPrefabList = [];//[tipItemPreb,tipNoHitItemPreb,comboxPreb,toastPreb]

    // @property(Prefab)
    // private pfVs: Prefab = null;
    @property(Label)
    private testLabel: Label = null;

    private _whiteBall: Ball = null;
    private _playerHash: Object = null;

    public loginMsg;
    /**游戏开始时间戳 */
    private startGameTime = 0;
    // private backgroundTimer: number;
    // private backgroundTime: number;

    onOpen(uid, loginMsg: any) {
        this.loginMsg = loginMsg;
        GameService.instance.initView(this);
        this.startGameTime = GameUtil.getSystemTime();
        Utils.clogDBAmsg('700001');
    }

    onLoad() {
        PhysicsSystem2D.instance.enable = true;
        Random.setSeed(123456);
        director.unregisterSystem(PhysicsSystem2D.instance)
        PhysicsSystem2D.instance.fixedTimeStep = Global.dt;
        
        let scale = Global.visibleHeight / 1334;
        let imgBg = this.node.getChildByName('img_bg');
        GameUtil.setSpriteFrame(imgBg.getComponent(Sprite), 'texture/game/img_bg');
        imgBg.setScale(scale, scale, scale);

        if (!this._playerHash) this._playerHash = {};
        Global.isGaming = true;
        TipManager.initTip(this.node);
        this.initView();

        // HitBallRuleMgr.setTestLabel(this.testLabel);
        // window.onerror = function (msg, url, line, column, detail) {
        //     console.log('====Game.onerror===', msg);
        // }
        // window.alert = function (msg, url, line, column, detail) {
        //     console.log('====alert===', msg);
        // }

        let mView = uiManager.getUI(UIID.Match)
        mView && uiManager.close(mView);
        JsbBrid.removeGameRulesView();
        JsbBrid.setGameState(1);

    }

    protected lateUpdate(dt: number): void {
        if(GameData.isGameOver) return;
        BallCtrl.update(dt);
    }

    // update(dt: number): void {
    //     if (GameData.isGameOver) return;
    //     BallCtrl.update(dt);
    // }

    public initView() {
        // this.initTopBar();
        let img_topBg = this.node.getChildByName('img_topBg');
        let topLayer = this.node.getChildByName('topLayer');
        let img_topBgUITransform = img_topBg.getComponent(UITransform);
        let topLayWidget = topLayer.getComponent(Widget);
        let btnSetWidget = topLayer.getChildByName('icon_set').getComponent(Widget);
        GameUtil.changAriboStyle(topLayer.getChildByName('node'));

        //TODO:: ios 没有刘海屏，耶返回了一个状态栏高度
        let hairHeight = (Global.systemInfo.hairHeight && Global.systemInfo.hairHeight > 44) ? (Utils.getHairHeight() - 5) : 0;
        //设置topPrefab位置 考虑刘海屏
        img_topBgUITransform.height += (hairHeight + 10);
        topLayWidget.top = hairHeight;
        if (Global.isArabic) {
            btnSetWidget.isAlignRight = false;
            btnSetWidget.isAlignLeft = true;
            btnSetWidget.left = 19;
        }
        EventMgr.addEventListener(EnumEvent.HIDE, this.onHide, this);
        EventMgr.addEventListener(EnumEvent.SHOW, this.onShow, this);
        EventMgr.addEventListener(EnumEvent.EXIT_GAME, this.onClickExit, this);
    }

    removeEvent() {
        EventMgr.removeEventListener(EnumEvent.HIDE, this.onHide, this);
        EventMgr.removeEventListener(EnumEvent.SHOW, this.onShow, this);
        EventMgr.removeEventListener(EnumEvent.EXIT_GAME, this.onClickExit, this);
    }
    /**
    * 切后台
    */
    private onHide(): void {
        // this.backgroundTime = GameUtil.getSystemTime();
        // let dt = game.frameTime;
        // console.log("==切后台=dt=" + dt);
        // this.backgroundTimer = setInterval(() => {
        //     console.log("===更新球====");
        //     BallCtrl.update();
        // }, dt);
        GameUtil.log("=切后台=");
        // PhysicsSystem2D.instance.enable = false; // 关闭物理引擎
        // if (BallCtrl.isShowGameOver()) GameService.instance.backHall();
    }

    /**
     * 界面显示处理
     * @description 当界面重新显示时的处理逻辑
     * @remarks
     * - 计算界面隐藏期间经过的时间
     * - 更新倒计时剩余时间
     * - 更新显示状态
     */
    private onShow(): void {
        // clearInterval(this.backgroundTimer);
        // const elapsed = (GameUtil.getSystemTime() - this.backgroundTime);
        // const steps = Math.floor(elapsed / game.frameTime);
        // console.log(elapsed + "=切前台的总步长=" + steps);
        // for (let i = 0; i < steps; i++) {
        //     BallCtrl.update();
        // }
        GameUtil.log("=切前台=");
        // PhysicsSystem2D.instance.enable = true; // 开启物理引擎
        this.scheduleOnce(() => {
            if(this.cue?.isVisible) BallCtrl.ballStop_updateCue();
        }, 1);
    }

    private initTopBar() {
        if(this.txtWinGold) this.txtWinGold.string = Utils.formatNum(GameData.winGold);
    }
    private onClickSet(e) {
        e.propagationStopped = true;
        uiManager.open(UIID.Settings, { isFormMatch: false });
        // Native.openMusic(AudioMgr.music);
        // WebViewJsBridge.openMusic(AudioMgr.music);
    }
    /** 二次确认退出游戏弹框 */
    private onClickExit() {
        dialogManager.showConfirm({
            tip: i18n.t("dialog_leaveSeat"),
            confirm: () => {
                GameService.instance.quitRequest();
            }
        });
    }

    /**
     * 如果游戏唯一标识符不一致，则清理localstorage
     * 更新桌球信息
     * 检测是否有上一次击球信息未完成; 如果击杆，击杆后，需要再次更新ballBilliardsStatus
     * 检测球杆角度
     * 飘提示
     */
    OpenBall() {
        LocalStorage.saveGameIdentity();
        this.initOpenBall();

        let hitball = GameData.hitBall;
        if (hitball) {
            BallCtrl.shootCue();
            GameData.updateBilliardsStatus();
            BallCtrl.doBallsSync(hitball);
            this.cue.handleBallHit(hitball);
            this.sliderbar.resetSlider();
            BallCtrl.onBallsSyncFinish();

        } else {//球杆角度
            // let isValidPos = GameData.checkValidPos(this._whiteBall.node.position)[0];
            // this.table.updateDragMoveActive(false, isValidPos);
            // if (isValidPos) {
            //     this.cue.updateCueInfo(false);
            // } else {
            //     if (!GameData.playBall) {
            //         if (GameData.cue && GameData.cue.hasOwnProperty('rad')) this.cue.handleCue(GameData.cue.rad, GameData.cue.angle);//避免跟下面ballStop_updateCue 重复处理球杆
            //             // else if (GameData.whiteBall && !this._whiteBall.isValidPos) this.cue.updateCueInfo(true);
            //             //TODO::这里调整，如果被顶号，没有whiteBall信息，但是白球存在，这里就要根据球再场景中的实际情况展示
            //         else if (this._whiteBall?.isValid && !this._whiteBall.isValidPos) this.cue.updateCueInfo(true);
            //     } else {
            //         BallCtrl.ballStop_updateCue();//'', GameData.getTableBall()
            //     }
            // }
            this.scheduleOnce(() => {
                GameUtil.log("=GameUI.openball ballStop_updateCue===");
                BallCtrl.ballStop_updateCue();
            }, 0);
        }

        let isBreaking = LocalStorage.saveBallBreaking('1');
        if (!isBreaking) {
            TipManager.showBreakingTip(GameData.currentOperate);
        }
    }

    private initOpenBall() {
        this.initTopBar();
        let fineTurning = this.node.getChildByPath('tableLayer/fineTurning');
        this._initBalls();
        BallCtrl.init(this._whiteBall, this.cue, this.table, this.sliderbar, fineTurning.getComponent(FineTurning));
        this.table.init(this._whiteBall);
        this.cue.init(this._whiteBall);
        this.initPlayer();
        AudioMgr.playMusic(AudioMgr.audios.bg, 1);
    }


    private _initBalls() {
        //初始化除白球外的15个球
        const gap = 32, startY = 170;
        let index = 0, isOpenBall = !GameData.playBall;
        for (let i = 0; i < 5; i++) {
            for (let j = 0; j <= i; j++) {
                let id = GameData.ballList[index++];
                let ball = BallCtrl.getBallById(id);
                if (!ball) {
                    ball = this._initBall(id);
                    BallCtrl.addBallToTable(ball);
                }
                // console.log(id + "=初始化球==" + isOpenBall);
                if (isOpenBall) {
                    let xx = j * gap - i * gap * 0.5;
                    if (i % 2 == 0) xx += 1;
                    else xx -= 0.5;
                    ball.updatePos(xx, i * (gap - 2) + startY);
                }
                ball.resetBall();
                // if (id == 8) {
                //     ball.updatePos(-222.6, 471);
                    // ball.updatePos(-222.6,471);
                // }
                // if (id == 2) {
                //     ball.updatePos(220, -76);
                // }
            }
        }
        //初始化白球
        if (!this._whiteBall) {
            this._whiteBall = this._initBall(0);
        }
        this._whiteBall.resetBall();
        if (isOpenBall && !GameData.whiteBall) {
            this._whiteBall.updatePos(GameData.BeginPos_White.x, GameData.BeginPos_White.y);
        } else if (GameData.whiteBall) {
            this._whiteBall.updatePos(GameData.whiteBall.x, GameData.whiteBall.y)
        }
        // this._whiteBall.updatePos(221.7,-467.3)
    }

    private _initBall(num: number) {
        let node = instantiate(this.pfBall);
        this.table.node.insertChild(node, 2);
        let ball = node.getComponent(Ball);
        // ball.init(num, this.ballTextures[num]);
        ball.init(num, this.textures[num], num > 8 ? this.b2 : this.b1);
        return ball;
    }

    /**初始化对局用户 */
    initPlayer() {
        if (!this._playerHash) this._playerHash = {};
        let players = GameData.players;
        let isreverse = i18n._language == 'ar';
        for (let i = 0; i < players.length; i++) {
            let playerId = players[i].playerId;
            let playerItem: PlayerItem = this._playerHash[playerId];
            if (!playerItem) {
                let node;
                if (playerId == Global.userId) node = instantiate(isreverse ? this.playerPrefabs[1] : this.playerPrefabs[0]);
                else node = instantiate(isreverse ? this.playerPrefabs[0] : this.playerPrefabs[1]);
                playerItem = node.getComponent(PlayerItem);
                this.node.getChildByName('playerNode').addChild(node);
                playerItem.setPlayer(players[i]);
                playerItem.initObjectBallData(GameData.openBallIsRestart);
                this._playerHash[playerId] = playerItem;
            }
            playerItem.stopTimer();
            if (playerId == GameData.currentOperate) {
                BallCtrl.beginTimer(playerItem);
            }
        }
    }

    /**
     * 移动球杆，拖动球杆力度
     * @returns 
     */
    dragMoveResponse(msg: any) {
        if (GameData.isMyOperate || !GameData.players) return;
        if (msg.hasOwnProperty('cue')) {
            let cueInfo = JSON.parse(msg.cue);
            if (cueInfo.hasOwnProperty('powerPercent')) {//对方力度表已经置灰，这里逻辑可以不执行
                const percent = cueInfo.powerPercent, moveY = cueInfo.moveY;
                this.sliderbar.handleCuePower(percent, moveY);
            } else {
                this.cue.handleCue(cueInfo.rad, cueInfo.angle);
            }
            if (cueInfo.hasOwnProperty('event')) {
                this.table.updateHandActive(false);
            }
        }
        if (msg.hasOwnProperty('whiteBall')) {
            let whiteBallInfo = JSON.parse(msg.whiteBall);
            if (whiteBallInfo.from) {
                this.table.dragMovePos(whiteBallInfo.x, whiteBallInfo.y, whiteBallInfo.isValidPos);
                // console.log(whiteBallInfo, '==== isValidPos ===', whiteBallInfo.isValidPos);
                if (whiteBallInfo.hasOwnProperty('event')) {
                    let isStart = whiteBallInfo.event == 'start';
                    this.table.updateDragMoveActive(isStart, whiteBallInfo.isValidPos);
                    if (!whiteBallInfo.isValidPos) this.cue.updateCueInfo('', !isStart);
                }
            }
        }
    }
    /**
     * 击球响应
     * @param hitball 
     * @returns 
     */
    ballHitResponse(hitball: any, msg: any) {
        let playerHead: PlayerItem = this._playerHash[GameData.currentOperate];
        if (playerHead) {
            this.table.stopCdTime();
        }
        if (GameData.currentOperate == Global.userId) return;

        BallCtrl.doBallsSync(hitball);
        this.cue.handleBallHit(hitball);
        this.sliderbar.resetSlider();
        BallCtrl.onBallsSyncFinish();
    }

    /**
     * 同步球杆信息;如果母球进袋，需要重置位置,母球位置更改，球杆也需要更新
     * TODO::8-20 球停先不同步球杆，等到切换用户再更新（检测母球是否有效位置）
     * @param msg 
     */
    allBallStopResponse(msg: any) {
        if (msg.restartPlayerId && msg.restartPlayerId > 0) {
            TipManager.showRestartTip();
        }
        let playerItem: PlayerItem = this._playerHash[msg.currentOperate];
        if (playerItem) {
            BallCtrl.updateBall(JSON.parse(msg.playBall));
            // this.table && this.table.updateObjectBallData();
        }
        GameUtil.log(GameData.isMyOperate + " GameUI allBallStopResponse " + GameData.noHit);
        GameUtil.log(msg);
        for (let k in this._playerHash) {
            this._playerHash[k].updateObjectBallData();
        }
        if (GameData.isMyOperate && !GameData.noHit) {
            BallCtrl.ballStopEventRequest();
        } else {
            if (GameService.instance.isErrorPro) {
                GameService.instance.isErrorPro = false;
                //TODO::异常结束，这里也需要同步球杆位置
                GameUtil.log("GameUI 异常球停更新球杆");
                BallCtrl.ballStop_updateCue();
                if (this._whiteBall?.isValid) {
                    this.table.dragMovePos(this._whiteBall.x, this._whiteBall.y);
                }
            }
        }
        GameUtil.log("==GameUI 母球位置", this._whiteBall.x, this._whiteBall.y);
    }

    /**
    * allBallStopResponse结束中会传event,如果是犯规，则切换的这个用户拥有自由球
    * msg.currentOperate 是获得自由球的玩家
    * @param msg (是否未击球由服务端给)
    */
    playerChangeResponse(msg: any, prevIdx: number) {
        GameData.freeBallIdx = 0;
        GameUtil.log("GameUI 切换用户：==是否有确定球色：" + GameData.roundSetObjectBall + "是否连杆：" + GameData.combox);
        GameUtil.log(msg);
        if (GameData.noHit && Global.userId == prevIdx) {
            let msgResult = 0;
            let playerNoHit = msg.playerNoHitCount;
            for (let k in playerNoHit) {
                if (playerNoHit[k].userId == prevIdx) {
                    msgResult = playerNoHit[k].noHitCount;
                }
            }
            msgResult && TipManager.showNoHitTip(msgResult);
        }
        //TODO::上个回合玩家异常停止
        if (!GameData.noHit && Global.userId != prevIdx && msg.noStopBallEvent) {
            BaseTipMgr.showToast(i18n.t('toast_exception_ballStop'));
        }

        if (GameData.roundSetObjectBall) {
            TipManager.showBallTypeTip();

        } else {
            if (msg.isFoul && GameData.billiardsStatus != BilliardsStatus.FirstOpen) {
                // GameData.whiteBall && (GameData.whiteBall.billiardsStatus = null);//TODO::由于犯规导致换人， false 非返回导致换人 30s
                TipManager.showFoulTip(prevIdx, AllBallStopEvent.FOUL);

            } else {
                if (GameData.combox >= 1 && GameData.isMyOperate) {
                    TipManager.showBallLink(GameData.combox);
                } else {
                    TipManager.showChangePlayerTip(msg.currentOperate);
                }
            }
        }

        if (msg.isFoul && GameData.billiardsStatus != BilliardsStatus.FirstOpen) {
            GameData.freeBallIdx = msg.currentOperate;
            GameData.whiteBall && (GameData.whiteBall.billiardsStatus = null);//TODO::由于犯规导致换人， false 非返回导致换人 30s
        }

        let prePlayerHead: PlayerItem = this._playerHash[prevIdx];
        if (prePlayerHead) prePlayerHead.stopTimer();
        let playerHead: PlayerItem = this._playerHash[msg.currentOperate];
        if (playerHead) {
            BallCtrl.playerChange_goBack(msg);
            BallCtrl.playerChangeResponse(playerHead);
        }
        GameData.roundSetObjectBall = false;
        // TipManager.showBallLink(6);
    }

    cdTimeResponse(msg: any) {
        let playerHead: PlayerItem = this._playerHash[GameData.currentOperate];
        if (playerHead) {
            BallCtrl.cdTimeResponse();
        }
    }

    offlineResponse(msg: any) {
        if (Global.isWatcher) {
            let playerData = GameData.getPlayerData(msg.userId);
            if (playerData) {
                msg = Utils.paramFormat(i18n.t('toast_offline_watch'), playerData.nickName);
                BaseTipMgr.showToast(msg);
            }
        } else {
            if (msg.userId != GameData.currentOperate)
                BaseTipMgr.showTip(BaseTipMgr.codes.offlineOpponent);
        }
    }

    gameOverResponse(msg: any) {
        // if (!Global.isResume) {
        //     GameService.instance.backHall();
        //     return;
        // }
        let resultView = uiManager.getUI(UIID.Result);
        GameUtil.log((!!resultView) + "===结算消息===");
        GameUtil.log(msg);
        if (resultView) {
            return;
        }
        // SceneMgr.open(SceneMgr.scenes.match, Gameover.parseData(msg));
        BallCtrl.gameOver();
        uiManager.open(UIID.Result, msg); 
        if (this.startGameTime > 0) {
            let gameTime = GameUtil.getSystemTime() - this.startGameTime;
            Utils.clogDBAmsg('700002', { gameTime });
            // console.log("=游戏时长埋点=" + gameTime);
        }
        this.startGameTime = 0;
        this.unscheduleAllCallbacks();
        this.cue?.clear();
    }

    /** 重新开始游戏 
     * 清理所有手牌 */
    restartGame() {
        for (let k in this._playerHash) {
            let playerItem: PlayerItem = this._playerHash[k];
            playerItem && playerItem.clearObjectBall();
        }
    }

    // updateTestPanel() {
    //     console.log(GameData.tableLinearDamp, '=====ballHit:  linearDamping:', GameData.ballLinearDamp);
    //     this._whiteBall.rb2D.linearDamping = GameData.ballLinearDamp;
    //     this.table.rb2D.linearDamping = GameData.tableLinearDamp;
    //     this._whiteBall.colliders[0].friction = GameData.ballFriction;
    //     let conlliders = this._whiteBall.colliders;
    //     conlliders.forEach(element => {
    //         element.restitution = GameData.Restitution_White;
    //     });
    // }

    // saveConsoleLog() {
    //     window['consoleOutPutPlugin'].export();
    // }

    onDestroy() {
        this._playerHash = null;
        this.startGameTime = 0;
        AudioMgr.stopMusic();
        BallCtrl.clear();
        // GameService.instance.clear();
        // GameService._instance = null;
        GameService.instance.resetGameData();
        TipManager.clear();
        BallCtrl.node = null;
    }

    // /**测试：直接进黑八 */
    // test8Object() {
    //     BallCtrl.test8Ball();
    // }
    /**测试：制定球进袋 */
    testEnterBall() {
        let inputTxt: EditBox = this.node.getChildByName('inputBallId').getComponent(EditBox);
        let ballIdList = inputTxt.string.split('-');
        GameUtil.log("====测试入口输入球Id：" + ballIdList);
        if (!ballIdList) {
            JsbBrid.showToast("请输入有效球id，1-15之间");
            return;
        }
        HitBallRuleMgr.testEnterBall(ballIdList);
    }
}

