import { _decorator, Component, Node, Input, EventTouch, tween, UIOpacity, UITransform, Vec2, instantiate, RigidBody2D, sp, v3 } from 'cc';
import { BilliardsStatus } from './data/Enum';
import GameData from './data/GameData';
import GameService from './data/GameService';
import { WhiteBall } from './ball/WhiteBall';
import { CdTime } from './subItem/CdTime';
import TipManager from './manager/TipManager';
import Utils from '../core/tools/Utils';
import { GameNodePool } from './manager/GameNodePool';
import { EnumEvent, EventMgr } from '../core/event/EventManager';
import { GameUtil } from '../core/tools/GameUtil';
const { ccclass, property } = _decorator;

@ccclass('Table')
export class Table extends Component {
    public rb2D: RigidBody2D = null;

    private _transform: UITransform = null;
    private _whiteBall: WhiteBall = null;

    private _bigCircle: Node = null;

    private _handContainer: Node = null;
    private _hand: Node = null;
    private _forbid: Node = null;

    private _openningArea: Node = null;
    private _openningAreaTransform: UITransform = null;
    private _openningAreaOpacity: UIOpacity = null;

    private _enterballNode: Node = null;
    private _enterBallSk: sp.Skeleton = null;

    private _cdTime: CdTime = null;
    private _minX = 0;
    private _minY = 0;
    private _maxX = 0;
    private _maxY = 0;
    private _isDrag: boolean = false;
    private _isMove: boolean = false;

    onLoad() {
        this.rb2D = this.getComponent(RigidBody2D);

        this._transform = this.node.getComponent(UITransform);
        this._openningArea = this.node.getChildByName('openningArea');
        this._openningAreaTransform = this._openningArea.getComponent(UITransform);

        this._openningAreaOpacity = this._openningArea.addComponent(UIOpacity);
        this._bigCircle = this.node.getChildByName('bigCircle');

        this._handContainer = this.node.parent.getChildByName('hand');
        this._hand = this._handContainer.getChildByName('hand');
        this._forbid = this._handContainer.getChildByName('forbidSet');

        this._enterballNode = this.node.parent.getChildByName('enterBallSk');
        this._enterBallSk = this._enterballNode.getComponent(sp.Skeleton);

        // this._handContainer.setSiblingIndex(GameData.ballHandIndex);
        // this._bigCircle.setSiblingIndex(GameData.ballCircleIndex);
        this._bigCircle.active = this._handContainer.active = false;
        // this.node.on(NodeEventType.TOUCH_START, this.onClickTable, this);
    }
    // onClickTable(e) {
        // e.propagationStopped = true;
    // }

    start() {
        this._openningAreaOpacity.opacity = 0;
        this.initCdTime();
    }

    async initCdTime() {
        let prefab = await GameNodePool.instance.cdTimePrefab();
        let cdTimeNode = instantiate(prefab)
        this.node.addChild(cdTimeNode);
        cdTimeNode.active = false;
        this._cdTime = cdTimeNode.getComponent(CdTime);
        cdTimeNode.setPosition(8, 501);
    }

    init(whiteBall: WhiteBall) {
        if (!this._whiteBall && whiteBall) {
            this._whiteBall = whiteBall;
            let ballNode = this._whiteBall.node;
            ballNode.on(Input.EventType.TOUCH_START, this._touchStart, this);
            ballNode.on(Input.EventType.TOUCH_CANCEL, this._touchEnd, this);
            ballNode.on(Input.EventType.TOUCH_END, this._touchEnd, this);
    
            this._hand.on(Input.EventType.TOUCH_START, this._touchStart, this);
            this._hand.on(Input.EventType.TOUCH_CANCEL, this._touchEnd, this);
            this._hand.on(Input.EventType.TOUCH_END, this._touchEnd, this);
        }

        // this._bigCircle.on(Input.EventType.TOUCH_START, this._touchStart, this);
        // this._bigCircle.on(Input.EventType.TOUCH_CANCEL, this._touchEnd, this);
        // this._bigCircle.on(Input.EventType.TOUCH_END, this._touchEnd, this);
        this.isDrag = GameData.billiardsStatus == BilliardsStatus.FirstOpen;
    }

    get canOperate(): boolean {
        if (!this._whiteBall) return false;
        if (GameData.isGameOver) return false;
        return GameData.isMyOperate;
    }

    private _touchStart(e: EventTouch) {
        if (!this.canOperate || !this._isDrag) return;
        this._whiteBall.node.on(Input.EventType.TOUCH_MOVE, this._touchMove, this);
        this._hand.on(Input.EventType.TOUCH_MOVE, this._touchMove, this);
        // this._bigCircle.on(Input.EventType.TOUCH_MOVE, this._touchMove, this);
        //限制区域拖动
        this._minX = this._openningArea.position.x - this._openningAreaTransform.width / 2 + GameData.ballRadius * 2 + 10;
        this._maxX = this._openningArea.position.x + this._openningAreaTransform.width / 2 - GameData.ballRadius * 2 - 10;
        this._minY = this._openningArea.position.y - this._openningAreaTransform.height / 2 + GameData.ballRadius * 2 + 10;
        this._maxY = this._openningArea.position.y + this._openningAreaTransform.height / 2;
        // console.log("==球放下==", this._minX, this._maxX, this._minY, this._maxY);
        this._whiteBall.whiteBallOpenning(false);
        EventMgr.raiseEvent(EnumEvent.CUSTOM_CUE_POSITION_UPDATE, false);
        GameUtil.log(GameData.isMyFreeBall+ '==Table.touchStart==billiardsStatus='+ GameData.billiardsStatus);

        if (GameData.billiardsStatus == BilliardsStatus.FirstOpen && !GameData.isMyFreeBall) {
            this._openningArea.active = true;
            this._openningAreaOpacity.opacity = 255;
            this._bigCircle.setPosition(this._whiteBall.node.position);
            this._bigCircle.active = true;
            this._tweenAni(125);

        } else {
            this._bigCircle.active = false;
            this._minX = -this._transform.width / 2 + GameData.tableSideWid;// this._handTransform.width / 2 + 16;
            this._maxX = this._transform.width / 2 - GameData.tableSideWid;//- this._handTransform.width / 2 - 16;
            this._minY = -this._transform.height / 2 + GameData.tableSideWid;//+ this._handTransform.height / 2 + 16;
            this._maxY = this._transform.height / 2 - GameData.tableSideWid;//- this._handTransform.height / 2 - 16;
            this._handContainer.active = true;
        }

        let isValidPos = GameData.checkValidPos(this._whiteBall.node.position)[0];
        this._forbid.active = isValidPos;
        this._whiteBall.isValidPos = isValidPos;

        GameService.instance.DragMoveRequest({
            whiteBall: JSON.stringify({ x: this._whiteBall.node.position.x, y: this._whiteBall.node.position.y, event: "start", from: "table" })
        })
    }

    private _frameCount = 0;
    private _touchMove(e: EventTouch) {
        if (!this.canOperate || !this._isDrag) return;
        this._isMove = true;
        if (this._frameCount % 3 == 0) {//TODO::降低频率
            this._dragWhiteBall(e.touch.getDelta(), Utils.getUILocation(e), GameData.isMyFreeBall);
        }
        this._frameCount += 1;
    }

    private _touchEnd(e: EventTouch) {
        this._frameCount = 0;
        this._isMove = false;
        this._whiteBall.node.off(Input.EventType.TOUCH_MOVE, this._touchMove, this);
        this._hand.off(Input.EventType.TOUCH_MOVE, this._touchMove, this);
        // console.log(!this.canOperate, '====_touchEnd====', !this._isDrag);
        if (!this.canOperate || !this._isDrag) return;

        let whiteBallPos = this._whiteBall.node.position;
        this._whiteBall.whiteBallOpenning(true);

        let isValidPos = GameData.checkValidPos(whiteBallPos)[0];
        this._whiteBall.checkIsValidPos(isValidPos);
        this._forbid.active = isValidPos;
        this._bigCircle.active = false;
        GameUtil.log("=====放下球消息======" + isValidPos);
        if (isValidPos) TipManager.showToast('view_tip_notputball');//TODO::Tip 提示，此处不能放球

        this._tweenAni(0);
        // console.log('=====_touchEnd====isValidPos:', isValidPos);
        if (!isValidPos) EventMgr.raiseEvent(EnumEvent.CUSTOM_CUE_POSITION_UPDATE, true);
        GameService.instance.DragMoveRequest({
            whiteBall: JSON.stringify({ x: whiteBallPos.x, y: whiteBallPos.y, event: "end", from: "table", isValidPos: isValidPos })
        })
    }

    private _tweenAni(opacityV: number) {
        tween(this._openningAreaOpacity).to(0.1, { opacity: opacityV })
            .start();
    }

    private _dragWhiteBall(delta, pos: Vec2, isFreeBoul: boolean = false) {
        let moveX = this._whiteBall.node.position.x + delta.x * 3;
        let moveY = this._whiteBall.node.position.y + delta.y * 3;
        if (moveX < this._minX) {
            moveX = this._minX;
        } else if (moveX > this._maxX) {
            moveX = this._maxX;
        }
        if (moveY < this._minY) {
            moveY = this._minY;
        } else if (moveY > this._maxY) {
            moveY = this._maxY;
        }

        this._bigCircle.setPosition(moveX, moveY);
        this._whiteBall.node.setPosition(moveX, moveY);
        if (isFreeBoul) {
            if ((moveX >= this._minX && moveX <= this._maxX) && (moveY >= this._minY && moveY <= this._maxY)) {
                this.forbidset();
            } else {
                this.forbidset(true);
            }
        }

        let result = GameData.checkValidPos(this._whiteBall.node.position);
        let isValidPos = result[0];
        //开球阶段，如果球靠近球袋，更改位置;其他阶段移动球就是禁止状态
        if (GameData.billiardsStatus == BilliardsStatus.FirstOpen && !GameData.isMyFreeBall) {
            if (result[1] == 'packet') {
                moveY += GameData.packetRadius;
                this._bigCircle.setPosition(moveX, moveY);
                isValidPos = false;
            }
        }
        this.dragMovePos(moveX, moveY, isValidPos);

        GameService.instance.DragMoveRequest({
            whiteBall: JSON.stringify({ x: moveX, y: moveY, from: "table", isValidPos: isValidPos })
        })
    }

    stopDrag() {
        if (this._isMove) {
            this._touchEnd(null);
        }
        this._isMove = false;
        this._isDrag = false;
        this._frameCount = 0;
    }

    updateHandActive(isShow: boolean = false) {
        if (this._handContainer) {
            this._handContainer.active = isShow;
            if (this._whiteBall) this._handContainer.setPosition(this._whiteBall.node.position);
        }
    }

    forbidset(isValidPos: boolean = false) {
        this._forbid.active = isValidPos;
        this._whiteBall.checkIsValidPos(isValidPos);
    }

    /**拖动球 检测是否有效位置 */
    updateDragMoveActive(isShow: boolean = false, isValidPos: boolean = false) {
        this.updateHandActive(isShow || isValidPos);
        this.forbidset(isValidPos);
        if (!isValidPos && this._whiteBall) {
            this._whiteBall.whiteBallOpenning(!isShow);
        }
    }
    /**拖动更改位置 */
    dragMovePos(x, y, isValidPos: boolean = false) {
        this._whiteBall.updatePos(x, y);
        this._handContainer.setPosition(x, y);
        this.forbidset(isValidPos);
    }

    // /**
    //  * 确认目标球，更新目标球列表
    //  * 如果是重新开局需要把原由数据清理
    //  * @param idx 
    //  * @param ballType 
    //  * @param enterBallDic 
    //  */
    // initObjectBallData() {
    //     if (GameData.openBallIsRestart) {
    //         this._myObjectBall.node.removeAllChildren();
    //         this._OtherObjectBall.node.removeAllChildren();
    //     }
    //     for (const key in GameData.playerObjectBallDic) {
    //         const playerId = Number(key);
    //         const ballList = GameData.playerObjectBallDic[key];
    //         let isLeftPlayer;
    //         if (GameData.isWatch) {
    //             isLeftPlayer = (playerId == GameData.players[0].playerId);
    //         } else {
    //             isLeftPlayer = (playerId == Global.userId);
    //         }
    //         if (isLeftPlayer) this._myObjectBall.initObjectBall(ballList);
    //         else this._OtherObjectBall.initObjectBall(ballList);
    //     }
    // }

    // updateObjectBallData() {
    //     this.initObjectBallData();
    //     this.removeObjectBall();
    // }

    // /**
    //  * 进球后更新目标球列表
    //  */
    // removeObjectBall() {
    //     let myEnterBalls = []
    //     let otherEnterBalls = [];
    //     let enterLen = GameData.enterBallList.length;
    //     for (let i = 0; i < enterLen; i++) {
    //         let key = GameData.enterBallList[i];
    //         if (this._myObjectBall.hasObjectBall(key)) myEnterBalls.push(key);
    //         if (this._OtherObjectBall.hasObjectBall(key)) otherEnterBalls.push(key);
    //     }

    //     this._myObjectBall.removeObjectBall(myEnterBalls);
    //     this._OtherObjectBall.removeObjectBall(otherEnterBalls);
    // }

    playCdTime(cdtime: number) {
        if (this._cdTime) {
            this._cdTime.node.active = true;
            this._cdTime.invoke(cdtime);
        }
    }

    stopCdTime() {
        this.updateHandActive(false);
        this._cdTime?.stopTimer();
    }

    playerChange(isMyFreeBall: boolean, isMyOperate: boolean) {
        GameUtil.log(isMyOperate+ '===Table.playerChange==isMyFreeBall='+ isMyFreeBall);
        this.isDrag = isMyOperate && isMyFreeBall;
        // if (!GameData.isWatch) {
        //     if (GameData.isMyOperate) {
        //         this._myObjectBall.setNodeOpacity(true);
        //         this._OtherObjectBall.setNodeOpacity();
        //     } else {
        //         this._myObjectBall.setNodeOpacity();
        //         this._OtherObjectBall.setNodeOpacity(true);
        //     }
        // }
        if (GameData.billiardsStatus != BilliardsStatus.FirstOpen) this.updateHandActive(isMyFreeBall);
        this._whiteBall.whiteBallOpenning(true);
        this._bigCircle.active = this._openningArea.active = false;
        this._frameCount = 0;
    }

    udpateEnterBallSk(isPlay: boolean = false, pos = null) {
        pos && (this._enterballNode.position = v3(pos[0], pos[1], 0));
        this._enterballNode.active = isPlay;
        GameUtil.log("====播放进球袋动画===isPlay=" + isPlay);
        this._enterBallSk.setAnimation(0, '183');
    }

    set isDrag(v) {
        this._isDrag = v;
    }
}

