// import { _decorator, Vec3, SpriteFrame, Material} from 'cc';
// import { Ball } from './Ball';
// import GameData from '../data/GameData';
// const { ccclass, property } = _decorator;

// @ccclass('WhiteBall')
// export class WhiteBall extends Ball {
//     public isValidPos: boolean = false; //母球是否无效位置

//     private _hitBallFirstPos: Vec3 = new Vec3();
//     private _hitBallFirstScale: Vec3 = new Vec3();

//     start() {
//         this.node.getPosition(this._hitBallFirstPos);
//         this.node.getScale(this._hitBallFirstScale);
//     }

//     // update(dt) {
//     //     super.update(dt);
//     //     // this._checkMoveStop();
//     // }

//     // init(num, texture: Texture2D, backNode = null, frontNode = null) {
// //     super.init(num, texture, backNode, frontNode);
//     init(num, texture: SpriteFrame, material: Material) {
//         super.init(num, texture, material);
//         this.node.position = GameData.BeginPos_White;
//     }

//     // beginCue() {//开始击杆
//     // this.state = BallState.Wake;
//     // }

//     // get ballStop(): boolean {
//     //     let vlen = this.rb2D.linearVelocity.length();
//     //     // if (this.state == BallState.Wake && vlen <= 0 && !this.tweenMove) {
//     //     if (vlen <= 0 && !this.tweenMove) {
//     //         return true;
//     //     }
//     //     return false;
//     // }

//     /**
//      * 白球犯规，重置回beginPos
//      * 1. 白球进洞
//      * 2. 先碰撞了对方球
//      * 3. 白球没有碰撞到任何球
//      */
//     foul(pos) {
//         // this.tweenMove = false;
//         this.node.setPosition(pos);
//         this.setPhysicalState(false);
//     }

//     reset() {
//         // this.state = BallState.Sleep;
//         this.resetVelocity();
//         this.setPhysicalState(true);
//     }

//     finishMove() {
//         this.tweenMove = false;
//     }

//     whiteBallOpenning(isShow: boolean) {
//         // this.state = BallState.Sleep;
//         this.setPhysicalState(isShow);
//     }

//     updateFirstData() {
//         this.node.setPosition(this._hitBallFirstPos);
//         this.node.setScale(this._hitBallFirstScale);
//     }

//     checkIsValidPos(isValidPos: boolean) {
//         this.isValidPos = isValidPos;
//         this.node.active = !this.isValidPos;
//         // this.setBackFrontActive(this.node.active);
//     }
// }

