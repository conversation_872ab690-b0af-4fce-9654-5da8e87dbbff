import { Label } from "cc";
import { GameUtil } from "../../core/tools/GameUtil";
import { JsbBrid } from "../../core/tools/JsbBridge";
import { BilliardsStatus } from "../data/Enum";
import GameData from "../data/GameData";
import BallCtrl from "./BallCtrl";

class HitBallRuleMgr {
    /**  击球规则 */
    /**
         * 开球 过程中如果有目标球入袋则开球玩家继续击球。此时不管进袋的是什么花色的球，都不会确定当前玩家的目标球色
         *
         * 所有球停止后，检测是否有犯规
         * 1. 开局（第一杆）
         *  <1-1>击球碰触台边的目标球数少于4颗、母球入袋
         *
         * 2. 开放球局（还没有确定目标球）
         *  <2-1>本回合没有击球
         *  <2-2>没有球碰触台边（含母球）
         *  <2-3>母球入袋
         *  <2-4>母球第一次碰黑球
         *
         * 3. 击球
         *  <3-1>本回合没有击球，对方获得自由球
         *  <3-2>母球最先碰触的必须是本方目标球（本方目标球已全部入袋后，8号球为本方目标球） 违反本项规则犯规
         *  <3-3>母球入袋
         *  <3-4>母球第一次碰黑球
         *  <3-5>母球是先碰库再碰球，但是碰球后母球和目标球都没有弹库的
         *
         * 4. 重新开局
         *  <4-1>开球后 黑球入袋
         *
         * 5. 开放球局 & 击球 黑球入袋 结束游戏
         *  <5-1>开放球局中，未出现目标球，把黑球入袋
         *  <5-2>出现目标球后，目标球>1,黑球入袋 游戏结束
         */
    // /** 单回合内白球发生碰撞列表*/
    // private _hitWhiteList: Array<any> = null;
    // /** 单回合内球袋发生碰撞列表*/
    // private _hitPacketList: Array<any> = null;
    // /** 单回合内库边发生碰撞列表*/
    // private _hitTableSideList: Array<any> = null;

    checkFoul(): boolean {
        let isFoul = false;
        let billiardsStatus = GameData.billiardsStatus;
        let isCheckNoHit = this.checkNoHit();
        let hitPacketList = BallCtrl.hitPacketList;
        let hitWhiteList = BallCtrl.hitWhiteList;
        let hitTableSideList = BallCtrl.hitTableSideList;
        // console.log(billiardsStatus, ':billiardsStatus==checkFoul 2==', isCheckNoHit);
        // console.log(this._hitWhiteList);
        // console.log(this._hitPacketList);
        // console.log(this._hitTableSideList);

        // if (billiardsStatus == BilliardsStatus.FirstOpen) {
        if (!GameData.playBall || billiardsStatus == BilliardsStatus.FirstOpen) {
            if (GameData.noHit) {//开球，本回合没有击球，不算犯规，对方仍然可以1/4区拖动母球
                return true;
            }

            if (isCheckNoHit) {//<1-1>
                GameUtil.log("==0犯规==<1-1>=");
                return true;
            }

            if (hitPacketList.indexOf('8') > -1) {//<4-1>
                //A发起了击球，但是没有球停就断线重连，这是玩家B击球把黑八打进，这里需要根据hitball时存入的currentOperate 是否跟当前操作一致
                if (GameData.hitBall && GameData.hitBall.currentOperate != GameData.currentOperate) {
                    GameUtil.log("==0游戏结束==<5-1>=");
                    let winner = GameData.getOtherPlayerData(GameData.currentOperate);
                    BallCtrl.gameOver(winner.playerId);
                    return;
                } else {
                    GameUtil.log("==0重新开局==<4-1>=");
                    BallCtrl.restartGame(false);
                }
                return;
            }

            if (hitPacketList.indexOf('0') > -1) {//<1-1>
                GameUtil.log("==0犯规1==<1-1>=");
                isFoul = true;
            }
            if (hitTableSideList.length < 4) {//<1-1>
                GameUtil.log("==0犯规2== <1-1> =");
                isFoul = true;
            }
            return isFoul;

        } else if (billiardsStatus == BilliardsStatus.Open) {//没有确定目标球

            if (GameData.noHit) {//<2-1>
                GameUtil.log("==1犯规==<2-1>=");
                return true;
            }
            if (isCheckNoHit) {//<2-2>
                GameUtil.log("==1犯规==<2-2>=");
                return true;
            }

            if (hitPacketList.indexOf('8') > -1) {//<5-1>
                GameUtil.log("==1游戏结束==<5-1>=");
                let winner = GameData.getOtherPlayerData(GameData.currentOperate);
                BallCtrl.gameOver(winner.playerId);
                return;
            }
            let firstHitballId = this.getHitBallIdByWhite(hitWhiteList);
            if (firstHitballId == 8) {//<2-4>
                GameUtil.log("==1犯规==<2-4>=");
                return true;
            }

            if (hitPacketList.indexOf('0') > -1) {//<2-3>
                GameUtil.log("==1犯规1==<2-3>=");
                return true;
            }
            let isValidBall: boolean = false;//是否进入有效球
            for (var i = 0; i < hitPacketList.length; i++) {
                if (Number(hitPacketList[i]) != 0) {
                    isValidBall = true;
                    BallCtrl.setObjectBall(hitPacketList[i]);
                }
            }
            if (!isValidBall && hitTableSideList.length < 1) {//<2-2>
                GameUtil.log("==1犯规==<2-2>=");
                return true;
            }

            if (hitTableSideList.length > 0 && hitWhiteList.length < 1 && hitPacketList.length < 1) {
                GameUtil.log("==2犯规==弹库没碰球=");
                return true;
            }

        } else {//击球
            if (GameData.noHit) {//<3-1>
                GameUtil.log("==2犯规==<3-1>=");
                return true;
            }
            if (isCheckNoHit) {//<3-2>
                GameUtil.log("==2犯规1==<3-2>=");
                return true;
            }

            let firstHitBallType;
            let firstHitballId = this.getHitBallIdByWhite(hitWhiteList);
            GameUtil.log('===Ballctrl.checkFoul====firstHitballId:', firstHitballId);

            let objectNums = GameData.objectBallList(GameData.currentOperate).length;
            if (hitPacketList.indexOf('8') > -1) {//<5-1>
                let winnerId = GameData.currentOperate;
                let winner = null;
                if (objectNums > 1) {
                    winner = GameData.getOtherPlayerData(GameData.currentOperate);
                    winnerId = winner.playerId;
                } else {
                    if (BallCtrl.roundEnterBallList.indexOf(0) > -1 || (firstHitballId > -1 && firstHitballId != 8)) {
                        winner = GameData.getOtherPlayerData(GameData.currentOperate);
                        winnerId = winner.playerId;
                    }
                }
                GameUtil.log(objectNums + " " + GameData.currentOperate + "==2游戏结束1==<5-1>=" + 'winnerId:' + winnerId);
                BallCtrl.gameOver(winnerId);
                return;
            }

            if (hitPacketList.indexOf('0') > -1) {//<3-3>
                GameUtil.log("==2犯规==<3-3>=");
                return true;
            }


            if (firstHitballId > -1) {
                firstHitBallType = GameData.getBallType(firstHitballId);
                if (objectNums > 1 && firstHitBallType != GameData.myBallType) {
                    GameUtil.log("==2犯规2==<3-2>=");
                    return true;
                }
                if (objectNums == 1 && firstHitballId != 8) {//<3-2>
                    GameUtil.log("==2犯规3==<3-2>=");
                    return true;
                }
            }

            // console.log(this._hitPacketList, '==12==击球 目标球数据===objectNums：', objectNums);
            if (hitPacketList.length < 1) {
                if (objectNums == 1 && firstHitballId != 8) {//<3-2>
                    GameUtil.log("==2犯规4==<3-2>=");
                    return true;
                }

                if (objectNums > 1 && firstHitballId == 8) {//<3-4>
                    GameUtil.log("==2犯规==<3-4>=", objectNums);
                    return true;
                }

                if (hitTableSideList.length < 1) {//<3-5>
                    GameUtil.log("==2犯规==<3-5>=");
                    return true;
                }

                if (hitTableSideList.length > 0 && hitWhiteList.length < 1 && hitPacketList.length < 1) {
                    GameUtil.log("==2犯规==弹库没碰球=");
                    return true;
                }
            }
        }
    }
    checkNoHit(): boolean {
        let isWhiteHit = BallCtrl.hitWhiteList.length > 0;
        let isHitPacket = BallCtrl.hitPacketList.length > 0;
        let isHitTableSide = BallCtrl.hitTableSideList.length > 0;
        if (isWhiteHit || isHitPacket || isHitTableSide) return false;
        return true;
    }

    getHitBallIdByWhite(hitWhiteList): number {

        let len = hitWhiteList.length;
        if (len > 0) {
            for (let i = 0; i < len; i++) {
                if (hitWhiteList[i] != 'table') {
                    return Number(hitWhiteList[i]);
                }
            }
        }
        return -1;
    }

    private _testLabel: Label = null;
    setTestLabel(lb) {
        this._testLabel = lb;
    }
    /**测试： 制定球进袋*/
    testEnterBall(ballIdList) {
        if (!GameData.isMyOperate || !BallCtrl.ballDic) {
            JsbBrid.showToast("不是自己回合");
            return;
        }
        let x = 218, y = -452;//196 -431
        for (let i = 0; i < ballIdList.length; i++) {
            let ballId = ballIdList[i];
            let ball = BallCtrl.ballDic[ballId];
            if (ball) {
                ball.updatePos(x - i * 24, y + i * 21);
            }
        }
    }

    showPowerAngle(power, angle, rad) {
        // this._testLabel.string = '-力度：' + power + " \n -角度：" + angle + " \n -rad:" + rad;
    }

    // /**测试：黑8进袋 */
    // test8Ball() {
    //     GameData.noHit = false;
    //     GameData.isSendBallStop = false;
    //     let objcetballList = GameData.objectBallList(GameData.currentOperate);
    //     let objectNums = objcetballList.length;
    //     if (objectNums == 2) {
    //         let index_black = objcetballList.indexOf(8);
    //         let index_other = 1 - index_black;

    //         let ballId_black = 8;
    //         let ballId_other = objcetballList[index_other];
    //         GameData.addEnterBall(ballId_other);
    //         GameData.addEnterBall(ballId_black);
    //         this._roundEnterBallList.push(ballId_black);
    //         this._roundEnterBallList.push(ballId_other);

    //         let ball_black = this.ballDic[ballId_black];
    //         let ball_other = this.ballDic[ballId_other];
    //         this._roundBallList.push(ball_other);
    //         this._roundBallList.push(ball_black);

    //         this._playBallMoveTween();
    //         console.log(index_black, '===test8Object==', index_other);
    //         console.log(this._roundEnterBallList, '===test8Object====', this._roundBallList);
    //     }
    // }

}
export default new HitBallRuleMgr;