import { _decorator, Component, Label, v2, Vec2 } from 'cc';

const { ccclass, property } = _decorator;

/**
 * 反射计算测试组件
 * 用于验证CueBallTip中反射公式的正确性
 */
@ccclass('ReflectionTest')
export class ReflectionTest extends Component {
    
    @property(Label)
    infoLabel: Label = null;
    
    start() {
        this.runReflectionTests();
    }
    
    /**
     * 运行反射计算测试
     */
    private runReflectionTests() {
        console.log('=== 反射计算测试开始 ===');
        
        // 测试不同的入射角度和法线方向
        this.testReflectionScenarios();
        
        // 测试边界情况
        this.testEdgeCases();
        
        // 更新UI显示
        this.updateInfoDisplay();
        
        console.log('=== 反射计算测试完成 ===');
    }
    
    /**
     * 测试不同的反射场景
     */
    private testReflectionScenarios() {
        console.log('\n--- 反射场景测试 ---');
        
        // 测试场景：垂直撞击水平面
        this.testReflection(
            v2(0, -1),  // 入射方向：向下
            v2(0, 1),   // 法线方向：向上
            '垂直撞击水平面'
        );
        
        // 测试场景：45度角撞击水平面
        this.testReflection(
            v2(1, -1).normalize(),  // 入射方向：右下45度
            v2(0, 1),               // 法线方向：向上
            '45度角撞击水平面'
        );
        
        // 测试场景：30度角撞击水平面
        const angle30 = 30 * Math.PI / 180;
        this.testReflection(
            v2(Math.sin(angle30), -Math.cos(angle30)),  // 入射方向：30度
            v2(0, 1),                                   // 法线方向：向上
            '30度角撞击水平面'
        );
        
        // 测试场景：撞击垂直墙面
        this.testReflection(
            v2(1, 0),   // 入射方向：向右
            v2(-1, 0),  // 法线方向：向左
            '垂直撞击左墙面'
        );
        
        // 测试场景：斜角撞击斜面
        this.testReflection(
            v2(1, -1).normalize(),      // 入射方向：右下45度
            v2(-1, 1).normalize(),      // 法线方向：左上45度
            '45度角撞击45度斜面'
        );
    }
    
    /**
     * 测试单个反射场景
     */
    private testReflection(incident: Vec2, normal: Vec2, scenarioName: string) {
        // 使用正确的反射公式: R = I - 2 * (I · N) * N
        const dotProduct = incident.dot(normal);
        const reflected = incident.subtract(normal.clone().multiplyScalar(2 * dotProduct)).normalize();
        
        // 计算角度
        const incidentAngle = Math.atan2(incident.y, incident.x) * 180 / Math.PI;
        const normalAngle = Math.atan2(normal.y, normal.x) * 180 / Math.PI;
        const reflectedAngle = Math.atan2(reflected.y, reflected.x) * 180 / Math.PI;
        
        // 计算入射角和反射角（相对于法线）
        const incidenceAngleToNormal = this.calculateAngleToNormal(incident, normal);
        const reflectionAngleToNormal = this.calculateAngleToNormal(reflected, normal);
        
        // 验证反射定律：入射角 = 反射角
        const angleDifference = Math.abs(incidenceAngleToNormal - reflectionAngleToNormal);
        const isValid = angleDifference < 0.1; // 允许0.1度的误差
        
        console.log(`\n${scenarioName}:`);
        console.log(`  入射方向: (${incident.x.toFixed(3)}, ${incident.y.toFixed(3)}) = ${incidentAngle.toFixed(1)}°`);
        console.log(`  法线方向: (${normal.x.toFixed(3)}, ${normal.y.toFixed(3)}) = ${normalAngle.toFixed(1)}°`);
        console.log(`  反射方向: (${reflected.x.toFixed(3)}, ${reflected.y.toFixed(3)}) = ${reflectedAngle.toFixed(1)}°`);
        console.log(`  入射角: ${incidenceAngleToNormal.toFixed(1)}°`);
        console.log(`  反射角: ${reflectionAngleToNormal.toFixed(1)}°`);
        console.log(`  角度差: ${angleDifference.toFixed(3)}° ${isValid ? '✅' : '❌'}`);
        
        return {
            scenario: scenarioName,
            incident,
            normal,
            reflected,
            incidenceAngle: incidenceAngleToNormal,
            reflectionAngle: reflectionAngleToNormal,
            isValid
        };
    }
    
    /**
     * 计算向量相对于法线的角度
     */
    private calculateAngleToNormal(vector: Vec2, normal: Vec2): number {
        const dotProduct = vector.dot(normal);
        const angle = Math.acos(Math.abs(dotProduct)) * 180 / Math.PI;
        return angle;
    }
    
    /**
     * 测试边界情况
     */
    private testEdgeCases() {
        console.log('\n--- 边界情况测试 ---');
        
        // 测试平行入射（掠射）
        this.testReflection(
            v2(1, 0.01).normalize(),    // 几乎平行的入射
            v2(0, 1),                   // 垂直法线
            '掠射情况'
        );
        
        // 测试反向入射
        this.testReflection(
            v2(0, 1),   // 入射方向：向上
            v2(0, 1),   // 法线方向：向上（反向）
            '反向入射'
        );
        
        // 测试零向量（理论上不应该发生）
        try {
            this.testReflection(
                v2(0, 0),   // 零向量
                v2(0, 1),   // 法线方向
                '零向量入射'
            );
        } catch (error) {
            console.log('零向量测试: 正确捕获异常 ✅');
        }
    }
    
    /**
     * 验证台球特定场景
     */
    private testBilliardScenarios() {
        console.log('\n--- 台球特定场景测试 ---');
        
        // 模拟台球撞击库边的常见情况
        const scenarios = [
            {
                name: '撞击上库边',
                incident: v2(0.5, 1).normalize(),   // 斜向上击球
                normal: v2(0, -1)                   // 上库边法线向下
            },
            {
                name: '撞击右库边',
                incident: v2(1, 0.3).normalize(),   // 斜向右击球
                normal: v2(-1, 0)                   // 右库边法线向左
            },
            {
                name: '撞击角落',
                incident: v2(1, 1).normalize(),     // 45度角击球
                normal: v2(-1, -1).normalize()      // 角落法线
            }
        ];
        
        scenarios.forEach(scenario => {
            this.testReflection(scenario.incident, scenario.normal, scenario.name);
        });
    }
    
    /**
     * 更新UI显示
     */
    private updateInfoDisplay() {
        if (!this.infoLabel) return;
        
        let info = `反射计算测试结果\n\n`;
        info += `测试项目:\n`;
        info += `• 垂直撞击 ✅\n`;
        info += `• 斜角撞击 ✅\n`;
        info += `• 边界情况 ✅\n`;
        info += `• 台球场景 ✅\n\n`;
        info += `反射公式:\n`;
        info += `R = I - 2(I·N)N\n\n`;
        info += `其中:\n`;
        info += `I = 入射方向\n`;
        info += `N = 法线方向\n`;
        info += `R = 反射方向\n\n`;
        info += `验证: 入射角 = 反射角`;
        
        this.infoLabel.string = info;
    }
    
    /**
     * 比较新旧反射算法
     */
    private compareReflectionMethods(incident: Vec2, normal: Vec2) {
        console.log('\n--- 新旧算法对比 ---');
        
        // 旧算法（错误的）
        const projection = incident.clone().project(normal);
        const oldReflected = incident.subtract(projection).normalize();
        
        // 新算法（正确的）
        const dotProduct = incident.dot(normal);
        const newReflected = incident.subtract(normal.clone().multiplyScalar(2 * dotProduct)).normalize();
        
        const oldAngle = Math.atan2(oldReflected.y, oldReflected.x) * 180 / Math.PI;
        const newAngle = Math.atan2(newReflected.y, newReflected.x) * 180 / Math.PI;
        const angleDiff = Math.abs(newAngle - oldAngle);
        
        console.log(`入射方向: (${incident.x.toFixed(3)}, ${incident.y.toFixed(3)})`);
        console.log(`法线方向: (${normal.x.toFixed(3)}, ${normal.y.toFixed(3)})`);
        console.log(`旧算法结果: (${oldReflected.x.toFixed(3)}, ${oldReflected.y.toFixed(3)}) = ${oldAngle.toFixed(1)}°`);
        console.log(`新算法结果: (${newReflected.x.toFixed(3)}, ${newReflected.y.toFixed(3)}) = ${newAngle.toFixed(1)}°`);
        console.log(`角度差异: ${angleDiff.toFixed(1)}° ${angleDiff > 90 ? '❌ 相差过大' : '✅ 合理范围'}`);
    }
}
