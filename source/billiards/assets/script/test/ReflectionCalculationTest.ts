import { _decorator, Component, Label, v2, Vec2 } from 'cc';

const { ccclass, property } = _decorator;

/**
 * 反射计算测试组件
 * 专门测试修复后的反射计算逻辑
 */
@ccclass('ReflectionCalculationTest')
export class ReflectionCalculationTest extends Component {
    
    @property(Label)
    resultLabel: Label = null;
    
    private testResults: Array<{name: string, passed: boolean, details: string}> = [];
    
    start() {
        this.runReflectionTests();
    }
    
    /**
     * 运行反射计算测试
     */
    private runReflectionTests() {
        console.log('=== 反射计算测试开始 ===');
        
        // 测试1: 基本反射定律验证
        this.testBasicReflectionLaw();
        
        // 测试2: 特殊角度反射
        this.testSpecialAngleReflections();
        
        // 测试3: 贴库边球反射
        this.testWallConstrainedReflections();
        
        // 测试4: 边界情况
        this.testEdgeCases();
        
        // 测试5: 实际游戏场景
        this.testGameScenarios();
        
        console.log('=== 反射计算测试完成 ===');
        this.updateResultDisplay();
    }
    
    /**
     * 测试基本反射定律
     */
    private testBasicReflectionLaw() {
        const testName = '基本反射定律验证';
        
        try {
            const testCases = [
                {
                    name: '45度入射',
                    incident: v2(1, 1).normalize(),
                    normal: v2(0, -1),
                    expectedReflected: v2(1, -1).normalize()
                },
                {
                    name: '30度入射',
                    incident: v2(Math.cos(Math.PI/6), Math.sin(Math.PI/6)),
                    normal: v2(0, -1),
                    expectedReflected: v2(Math.cos(Math.PI/6), -Math.sin(Math.PI/6))
                },
                {
                    name: '垂直入射',
                    incident: v2(0, 1),
                    normal: v2(0, -1),
                    expectedReflected: v2(0, -1)
                }
            ];
            
            let passedCases = 0;
            
            for (const testCase of testCases) {
                const reflected = this.calculateReflection(testCase.incident, testCase.normal);
                const error = reflected.subtract(testCase.expectedReflected).length();
                
                if (error < 0.01) {
                    passedCases++;
                    console.log(`  ✅ ${testCase.name}: 通过`);
                } else {
                    console.log(`  ❌ ${testCase.name}: 失败, 误差=${error.toFixed(4)}`);
                    console.log(`    期望: (${testCase.expectedReflected.x.toFixed(3)}, ${testCase.expectedReflected.y.toFixed(3)})`);
                    console.log(`    实际: (${reflected.x.toFixed(3)}, ${reflected.y.toFixed(3)})`);
                }
            }
            
            if (passedCases === testCases.length) {
                this.testResults.push({
                    name: testName,
                    passed: true,
                    details: `所有${testCases.length}个基本反射测试通过`
                });
            } else {
                throw new Error(`${testCases.length - passedCases}个测试失败`);
            }
        } catch (error) {
            this.testResults.push({
                name: testName,
                passed: false,
                details: `错误: ${error.message}`
            });
        }
    }
    
    /**
     * 测试特殊角度反射
     */
    private testSpecialAngleReflections() {
        const testName = '特殊角度反射测试';
        
        try {
            // 测试用户报告的问题场景
            const problemCase = {
                incidentAngle: -154.7 * Math.PI / 180,
                normalAngle: 25.3 * Math.PI / 180
            };
            
            const incident = v2(Math.cos(problemCase.incidentAngle), Math.sin(problemCase.incidentAngle));
            const normal = v2(Math.cos(problemCase.normalAngle), Math.sin(problemCase.normalAngle));
            
            // 确保法线方向正确
            if (incident.dot(normal) > 0) {
                normal.multiplyScalar(-1);
            }
            
            const reflected = this.calculateReflection(incident, normal);
            
            // 验证反射角不等于入射角
            const incidentAngleDeg = Math.atan2(incident.y, incident.x) * 180 / Math.PI;
            const reflectedAngleDeg = Math.atan2(reflected.y, reflected.x) * 180 / Math.PI;
            const angleDifference = Math.abs(reflectedAngleDeg - incidentAngleDeg);
            
            console.log(`[测试] 问题场景重现:`);
            console.log(`  入射角: ${incidentAngleDeg.toFixed(1)}°`);
            console.log(`  法线角: ${Math.atan2(normal.y, normal.x) * 180 / Math.PI.toFixed(1)}°`);
            console.log(`  反射角: ${reflectedAngleDeg.toFixed(1)}°`);
            console.log(`  角度差: ${angleDifference.toFixed(1)}°`);
            
            if (angleDifference > 5) {
                this.testResults.push({
                    name: testName,
                    passed: true,
                    details: `特殊角度反射正确，角度差=${angleDifference.toFixed(1)}°`
                });
            } else {
                throw new Error(`反射角与入射角过于接近，角度差=${angleDifference.toFixed(1)}°`);
            }
        } catch (error) {
            this.testResults.push({
                name: testName,
                passed: false,
                details: `错误: ${error.message}`
            });
        }
    }
    
    /**
     * 测试贴库边球反射
     */
    private testWallConstrainedReflections() {
        const testName = '贴库边球反射测试';
        
        try {
            // 模拟球贴左库边的情况
            const ballCenter = v2(-360, 0); // 贴近左墙
            const hitPoint = v2(-345, 0);   // 碰撞点
            const incident = v2(1, 0.5).normalize(); // 入射方向
            
            // 计算球法线和墙法线
            const ballNormal = ballCenter.subtract(hitPoint).normalize();
            const wallNormal = v2(1, 0); // 左墙法线向右
            
            // 选择合适的法线
            const ballDot = incident.dot(ballNormal);
            const wallDot = incident.dot(wallNormal);
            
            let selectedNormal: Vec2;
            if (Math.abs(ballDot) > 0.1 && ballDot < 0) {
                selectedNormal = ballNormal;
            } else if (Math.abs(wallDot) > 0.1 && wallDot < 0) {
                selectedNormal = wallNormal;
            } else {
                // 混合法线
                selectedNormal = ballNormal.multiplyScalar(0.7).add(wallNormal.multiplyScalar(0.3)).normalize();
                if (incident.dot(selectedNormal) > 0) {
                    selectedNormal = selectedNormal.negative();
                }
            }
            
            const reflected = this.calculateReflection(incident, selectedNormal);
            
            // 验证反射方向远离墙面
            const reflectedDotWall = reflected.dot(wallNormal);
            
            if (reflectedDotWall > 0) {
                this.testResults.push({
                    name: testName,
                    passed: true,
                    details: `贴库边反射正确，球远离墙面 (点积=${reflectedDotWall.toFixed(3)})`
                });
            } else {
                throw new Error(`反射方向朝向墙面，点积=${reflectedDotWall.toFixed(3)}`);
            }
        } catch (error) {
            this.testResults.push({
                name: testName,
                passed: false,
                details: `错误: ${error.message}`
            });
        }
    }
    
    /**
     * 测试边界情况
     */
    private testEdgeCases() {
        const testName = '边界情况测试';
        
        try {
            const edgeCases = [
                {
                    name: '掠射入射',
                    incident: v2(1, 0.01).normalize(),
                    normal: v2(0, 1)
                },
                {
                    name: '接近平行入射',
                    incident: v2(1, 0.001).normalize(),
                    normal: v2(0, 1)
                },
                {
                    name: '大角度入射',
                    incident: v2(0.1, 1).normalize(),
                    normal: v2(1, 0)
                }
            ];
            
            let passedCases = 0;
            
            for (const testCase of edgeCases) {
                try {
                    const reflected = this.calculateReflection(testCase.incident, testCase.normal);
                    
                    // 验证反射向量长度
                    const length = reflected.length();
                    if (Math.abs(length - 1) < 0.01) {
                        passedCases++;
                        console.log(`  ✅ ${testCase.name}: 通过`);
                    } else {
                        console.log(`  ❌ ${testCase.name}: 反射向量长度异常 (${length.toFixed(3)})`);
                    }
                } catch (error) {
                    console.log(`  ❌ ${testCase.name}: 计算错误 - ${error.message}`);
                }
            }
            
            if (passedCases === edgeCases.length) {
                this.testResults.push({
                    name: testName,
                    passed: true,
                    details: `所有${edgeCases.length}个边界情况测试通过`
                });
            } else {
                throw new Error(`${edgeCases.length - passedCases}个边界测试失败`);
            }
        } catch (error) {
            this.testResults.push({
                name: testName,
                passed: false,
                details: `错误: ${error.message}`
            });
        }
    }
    
    /**
     * 测试实际游戏场景
     */
    private testGameScenarios() {
        const testName = '实际游戏场景测试';
        
        try {
            // 模拟台球桌上的实际碰撞场景
            const scenarios = [
                {
                    name: '中心区域碰撞',
                    ballCenter: v2(0, 0),
                    hitPoint: v2(-15, 0),
                    incident: v2(1, 0)
                },
                {
                    name: '角落区域碰撞',
                    ballCenter: v2(300, 500),
                    hitPoint: v2(285, 500),
                    incident: v2(1, 0.2).normalize()
                },
                {
                    name: '库边附近碰撞',
                    ballCenter: v2(-350, 100),
                    hitPoint: v2(-335, 100),
                    incident: v2(1, -0.3).normalize()
                }
            ];
            
            let passedScenarios = 0;
            
            for (const scenario of scenarios) {
                const normal = scenario.ballCenter.subtract(scenario.hitPoint).normalize();
                
                // 确保法线方向正确
                if (scenario.incident.dot(normal) > 0) {
                    normal.multiplyScalar(-1);
                }
                
                const reflected = this.calculateReflection(scenario.incident, normal);
                
                // 验证反射定律
                const incidenceAngle = Math.acos(Math.abs(scenario.incident.dot(normal)));
                const reflectionAngle = Math.acos(Math.abs(reflected.dot(normal)));
                const angleDiff = Math.abs(incidenceAngle - reflectionAngle);
                
                if (angleDiff < 0.01) {
                    passedScenarios++;
                    console.log(`  ✅ ${scenario.name}: 通过`);
                } else {
                    console.log(`  ❌ ${scenario.name}: 反射定律验证失败, 角度差=${angleDiff.toFixed(4)}`);
                }
            }
            
            if (passedScenarios === scenarios.length) {
                this.testResults.push({
                    name: testName,
                    passed: true,
                    details: `所有${scenarios.length}个游戏场景测试通过`
                });
            } else {
                throw new Error(`${scenarios.length - passedScenarios}个场景测试失败`);
            }
        } catch (error) {
            this.testResults.push({
                name: testName,
                passed: false,
                details: `错误: ${error.message}`
            });
        }
    }
    
    /**
     * 计算反射方向
     */
    private calculateReflection(incident: Vec2, normal: Vec2): Vec2 {
        // 使用反射公式: R = I - 2 * (I · N) * N
        const dotProduct = incident.dot(normal);
        return incident.subtract(normal.clone().multiplyScalar(2 * dotProduct)).normalize();
    }
    
    /**
     * 更新结果显示
     */
    private updateResultDisplay() {
        if (!this.resultLabel) return;
        
        const passedTests = this.testResults.filter(r => r.passed).length;
        const totalTests = this.testResults.length;
        
        let resultText = `反射计算测试结果\n\n`;
        resultText += `总测试数: ${totalTests}\n`;
        resultText += `通过: ${passedTests}\n`;
        resultText += `失败: ${totalTests - passedTests}\n\n`;
        
        if (passedTests === totalTests) {
            resultText += `🎉 所有测试通过！\n`;
            resultText += `反射计算系统工作正常\n\n`;
        } else {
            resultText += `⚠️ 存在测试失败\n`;
            resultText += `需要进一步检查和修复\n\n`;
        }
        
        resultText += `详细结果:\n`;
        this.testResults.forEach(result => {
            const icon = result.passed ? '✅' : '❌';
            resultText += `${icon} ${result.name}\n`;
            resultText += `  ${result.details}\n\n`;
        });
        
        this.resultLabel.string = resultText;
        
        // 输出总结到控制台
        console.log(`\n=== 反射计算测试总结 ===`);
        console.log(`通过率: ${passedTests}/${totalTests} (${(passedTests/totalTests*100).toFixed(1)}%)`);
        
        if (passedTests === totalTests) {
            console.log(`🎉 所有反射计算测试通过！系统工作正常。`);
        } else {
            console.warn(`⚠️ ${totalTests - passedTests}个测试失败，需要进一步修复。`);
        }
    }
}
