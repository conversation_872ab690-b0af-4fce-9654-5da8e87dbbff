import { _decorator, Component, Label, v2 } from 'cc';
import { PhysicsCompensator } from '../core/tools/PhysicsCompensator';
import Global from '../core/data/Global';

const { ccclass, property } = _decorator;

/**
 * 物理补偿测试组件
 * 用于验证物理世界缩放补偿的效果
 */
@ccclass('PhysicsCompensationTest')
export class PhysicsCompensationTest extends Component {
    
    @property(Label)
    infoLabel: Label = null;
    
    start() {
        this.runTests();
    }
    
    private runTests() {
        console.log('=== 物理补偿测试开始 ===');
        
        // 模拟不同的分辨率缩放情况
        this.testScenario('标准分辨率 (无缩放)', 1.0, 1.0);
        this.testScenario('宽屏设备 (Y轴拉伸)', 1.0, 1.5);
        this.testScenario('窄屏设备 (Y轴压缩)', 1.0, 0.8);
        this.testScenario('平板设备 (X轴拉伸)', 1.3, 1.0);
        
        console.log('=== 物理补偿测试完成 ===');
    }
    
    private testScenario(scenarioName: string, scaleX: number, scaleY: number) {
        console.log(`\n--- 测试场景: ${scenarioName} ---`);
        
        // 设置模拟的缩放参数
        Global.physicsScaleX = scaleX;
        Global.physicsScaleY = scaleY;
        Global.physicsScaleRatio = scaleY / scaleX;
        
        const compensator = PhysicsCompensator.instance;
        
        // 测试方向补偿
        const originalDirection = v2(1, 1).normalize(); // 45度角
        const compensatedDirection = compensator.compensateDirection(originalDirection);
        
        console.log(`原始方向: (${originalDirection.x.toFixed(3)}, ${originalDirection.y.toFixed(3)})`);
        console.log(`补偿后方向: (${compensatedDirection.x.toFixed(3)}, ${compensatedDirection.y.toFixed(3)})`);
        
        // 测试力度补偿
        const originalPower = 50;
        const compensatedPower = compensator.compensatePower(originalPower);
        
        console.log(`原始力度: ${originalPower}`);
        console.log(`补偿后力度: ${compensatedPower.toFixed(3)}`);
        
        // 测试位置补偿
        const originalPosition = v2(100, 200);
        const compensatedPosition = compensator.compensatePosition(originalPosition);
        const decompensatedPosition = compensator.decompensatePosition(compensatedPosition);
        
        console.log(`原始位置: (${originalPosition.x}, ${originalPosition.y})`);
        console.log(`补偿后位置: (${compensatedPosition.x.toFixed(3)}, ${compensatedPosition.y.toFixed(3)})`);
        console.log(`反向补偿位置: (${decompensatedPosition.x.toFixed(3)}, ${decompensatedPosition.y.toFixed(3)})`);
        
        // 验证补偿的准确性
        const directionError = Math.abs(compensatedDirection.length() - 1.0);
        const positionError = originalPosition.subtract(decompensatedPosition).length();
        
        console.log(`方向向量长度误差: ${directionError.toFixed(6)} (应该接近0)`);
        console.log(`位置往返误差: ${positionError.toFixed(6)} (应该接近0)`);
        
        // 更新UI显示
        if (this.infoLabel) {
            const info = `${scenarioName}\n` +
                        `缩放: X=${scaleX}, Y=${scaleY}\n` +
                        `补偿比例: ${Global.physicsScaleRatio.toFixed(3)}\n` +
                        `方向误差: ${directionError.toFixed(6)}\n` +
                        `位置误差: ${positionError.toFixed(6)}`;
            this.infoLabel.string = info;
        }
    }
    
    /**
     * 测试台球轨迹补偿效果
     */
    private testBilliardTrajectory() {
        console.log('\n--- 台球轨迹补偿测试 ---');
        
        const compensator = PhysicsCompensator.instance;
        
        // 模拟不同角度的击球
        const testAngles = [0, 30, 45, 60, 90, 120, 135, 150, 180];
        
        testAngles.forEach(angle => {
            const rad = angle * Math.PI / 180;
            const direction = v2(Math.cos(rad), Math.sin(rad));
            const compensatedDirection = compensator.compensateDirection(direction);
            
            const angleError = Math.abs(Math.atan2(compensatedDirection.y, compensatedDirection.x) - rad) * 180 / Math.PI;
            
            console.log(`角度 ${angle}°: 补偿误差 ${angleError.toFixed(3)}°`);
        });
    }
    
    /**
     * 性能测试
     */
    private performanceTest() {
        console.log('\n--- 性能测试 ---');
        
        const compensator = PhysicsCompensator.instance;
        const iterations = 10000;
        const testDirection = v2(1, 1).normalize();
        
        // 测试方向补偿性能
        const startTime = performance.now();
        for (let i = 0; i < iterations; i++) {
            compensator.compensateDirection(testDirection);
        }
        const endTime = performance.now();
        
        const avgTime = (endTime - startTime) / iterations;
        console.log(`方向补偿平均耗时: ${avgTime.toFixed(6)}ms`);
        console.log(`每秒可处理: ${(1000 / avgTime).toFixed(0)} 次补偿`);
    }
    
    /**
     * 重置测试环境
     */
    private resetTestEnvironment() {
        Global.physicsScaleX = 1;
        Global.physicsScaleY = 1;
        Global.physicsScaleRatio = 1;
    }
    
    onDestroy() {
        this.resetTestEnvironment();
    }
}
