import { _decorator, Component, Label, v2, Vec2 } from 'cc';
import { BallType, BilliardsStatus } from '../game/data/Enum';
import GameData from '../game/data/GameData';

const { ccclass, property } = _decorator;

/**
 * 目标球选择测试组件
 * 测试回合开始时母球指向目标球的逻辑
 */
@ccclass('TargetBallSelectionTest')
export class TargetBallSelectionTest extends Component {
    
    @property(Label)
    resultLabel: Label = null;
    
    private testResults: Array<{name: string, passed: boolean, details: string}> = [];
    
    start() {
        this.runTargetBallSelectionTests();
    }
    
    /**
     * 运行目标球选择测试
     */
    private runTargetBallSelectionTests() {
        console.log('=== 目标球选择测试开始 ===');
        
        // 测试1: 开球状态目标球选择
        this.testOpenStatusTargetSelection();
        
        // 测试2: 已确定目标球状态的选择
        this.testObjectStatusTargetSelection();
        
        // 测试3: 开放状态目标球选择
        this.testOpenBallStatusTargetSelection();
        
        // 测试4: 特殊情况处理
        this.testSpecialCases();
        
        // 测试5: 射线检测验证
        this.testRaycastValidation();
        
        console.log('=== 目标球选择测试完成 ===');
        this.updateResultDisplay();
    }
    
    /**
     * 测试开球状态目标球选择
     */
    private testOpenStatusTargetSelection() {
        const testName = '开球状态目标球选择';
        
        try {
            // 模拟开球状态的球桌
            const ballDic = {
                '1': { x: 100, y: 0 },    // 最近的球
                '2': { x: 150, y: 50 },
                '3': { x: 200, y: 100 },
                '8': { x: 120, y: 30 },   // 黑球，不应该被选择
                '9': { x: 180, y: 80 },
                '10': { x: 250, y: 150 }
            };
            
            const whiteBallPos = { x: 0, y: 0 };
            
            // 模拟CueBallTip的开球选择逻辑
            let selectedTarget = this.simulateOpenStatusSelection(whiteBallPos, ballDic);
            
            if (selectedTarget && selectedTarget.ballId === 1) {
                this.testResults.push({
                    name: testName,
                    passed: true,
                    details: `正确选择最近的非黑球: 球${selectedTarget.ballId}, 距离=${selectedTarget.distance.toFixed(1)}`
                });
            } else {
                throw new Error(`选择错误，期望球1，实际选择球${selectedTarget?.ballId || '无'}`);
            }
        } catch (error) {
            this.testResults.push({
                name: testName,
                passed: false,
                details: `错误: ${error.message}`
            });
        }
    }
    
    /**
     * 测试已确定目标球状态的选择
     */
    private testObjectStatusTargetSelection() {
        const testName = '已确定目标球状态选择';
        
        try {
            // 模拟已确定目标球的状态
            const ballDic = {
                '1': { x: 100, y: 0 },    // 玩家的目标球
                '2': { x: 80, y: 50 },    // 玩家的目标球，更近
                '9': { x: 50, y: 30 },    // 对手的目标球，最近但不应选择
                '10': { x: 120, y: 80 },  // 对手的目标球
                '8': { x: 200, y: 100 }   // 黑球
            };
            
            const whiteBallPos = { x: 0, y: 0 };
            
            // 模拟玩家目标球类型为实心球(Solid)
            const playerObjectBalls = [1, 2, 3, 4, 5, 6, 7, 8]; // 包含黑球8
            const cueBallType = BallType.Solid;
            
            let selectedTarget = this.simulateObjectStatusSelection(
                whiteBallPos, 
                ballDic, 
                cueBallType, 
                playerObjectBalls
            );
            
            if (selectedTarget && selectedTarget.ballId === 2) {
                this.testResults.push({
                    name: testName,
                    passed: true,
                    details: `正确选择最近的玩家目标球: 球${selectedTarget.ballId}, 距离=${selectedTarget.distance.toFixed(1)}`
                });
            } else {
                throw new Error(`选择错误，期望球2，实际选择球${selectedTarget?.ballId || '无'}`);
            }
        } catch (error) {
            this.testResults.push({
                name: testName,
                passed: false,
                details: `错误: ${error.message}`
            });
        }
    }
    
    /**
     * 测试开放状态目标球选择
     */
    private testOpenBallStatusTargetSelection() {
        const testName = '开放状态目标球选择';
        
        try {
            // 模拟开放状态（已击球但未确定目标球）
            const ballDic = {
                '1': { x: 100, y: 0 },    // 实心球
                '2': { x: 150, y: 50 },   // 实心球
                '9': { x: 80, y: 30 },    // 花色球，最近
                '10': { x: 120, y: 80 },  // 花色球
                '8': { x: 200, y: 100 }   // 黑球，不应选择
            };
            
            const whiteBallPos = { x: 0, y: 0 };
            const cueBallType = BallType.Stripe; // 假设当前瞄准花色球
            
            let selectedTarget = this.simulateOpenBallStatusSelection(
                whiteBallPos, 
                ballDic, 
                cueBallType
            );
            
            if (selectedTarget && selectedTarget.ballId === 9) {
                this.testResults.push({
                    name: testName,
                    passed: true,
                    details: `正确选择最近的花色球: 球${selectedTarget.ballId}, 距离=${selectedTarget.distance.toFixed(1)}`
                });
            } else {
                throw new Error(`选择错误，期望球9，实际选择球${selectedTarget?.ballId || '无'}`);
            }
        } catch (error) {
            this.testResults.push({
                name: testName,
                passed: false,
                details: `错误: ${error.message}`
            });
        }
    }
    
    /**
     * 测试特殊情况处理
     */
    private testSpecialCases() {
        const testName = '特殊情况处理';
        
        try {
            let passedCases = 0;
            let totalCases = 0;
            
            // 情况1: 没有有效目标球
            totalCases++;
            const emptyBallDic = { '8': { x: 100, y: 0 } }; // 只有黑球
            const whiteBallPos = { x: 0, y: 0 };
            
            let result1 = this.simulateOpenStatusSelection(whiteBallPos, emptyBallDic);
            if (!result1) {
                passedCases++;
                console.log(`  ✅ 情况1: 正确处理无有效目标球的情况`);
            } else {
                console.log(`  ❌ 情况1: 应该返回null但返回了球${result1.ballId}`);
            }
            
            // 情况2: 只有黑球作为目标（最后阶段）
            totalCases++;
            const blackBallOnly = { '8': { x: 100, y: 0 } };
            const playerObjectBalls = [8]; // 只剩黑球
            
            let result2 = this.simulateObjectStatusSelection(
                whiteBallPos, 
                blackBallOnly, 
                BallType.Black, 
                playerObjectBalls
            );
            if (result2 && result2.ballId === 8) {
                passedCases++;
                console.log(`  ✅ 情况2: 正确选择黑球作为最后目标`);
            } else {
                console.log(`  ❌ 情况2: 应该选择黑球但选择了球${result2?.ballId || '无'}`);
            }
            
            // 情况3: 所有球都被遮挡
            totalCases++;
            const blockedBalls = {
                '1': { x: 100, y: 0 },
                '2': { x: 150, y: 50 }
            };
            
            // 模拟所有球都被遮挡的情况
            let result3 = this.simulateBlockedBallsSelection(whiteBallPos, blockedBalls);
            if (result3) {
                passedCases++;
                console.log(`  ✅ 情况3: 正确处理被遮挡球的情况，选择球${result3.ballId}`);
            } else {
                console.log(`  ❌ 情况3: 未能处理被遮挡球的情况`);
            }
            
            if (passedCases === totalCases) {
                this.testResults.push({
                    name: testName,
                    passed: true,
                    details: `所有${totalCases}个特殊情况都正确处理`
                });
            } else {
                throw new Error(`${totalCases - passedCases}个特殊情况处理失败`);
            }
        } catch (error) {
            this.testResults.push({
                name: testName,
                passed: false,
                details: `错误: ${error.message}`
            });
        }
    }
    
    /**
     * 测试射线检测验证
     */
    private testRaycastValidation() {
        const testName = '射线检测验证';
        
        try {
            // 模拟射线检测逻辑
            const whiteBallPos = { x: 0, y: 0 };
            const targetPos = { x: 100, y: 0 };
            
            // 计算射线方向
            const rad = Math.atan2(whiteBallPos.y - targetPos.y, whiteBallPos.x - targetPos.x);
            const dir = v2(Math.cos(rad), Math.sin(rad)).negative();
            
            // 验证射线方向计算
            const expectedDir = v2(1, 0); // 应该指向右方
            const dirError = dir.subtract(expectedDir).length();
            
            if (dirError < 0.01) {
                this.testResults.push({
                    name: testName,
                    passed: true,
                    details: `射线方向计算正确: (${dir.x.toFixed(3)}, ${dir.y.toFixed(3)})`
                });
            } else {
                throw new Error(`射线方向计算错误，误差=${dirError.toFixed(4)}`);
            }
        } catch (error) {
            this.testResults.push({
                name: testName,
                passed: false,
                details: `错误: ${error.message}`
            });
        }
    }
    
    /**
     * 模拟开球状态的目标球选择
     */
    private simulateOpenStatusSelection(whiteBallPos: any, ballDic: any): any {
        let validTargets = [];
        
        for (const key in ballDic) {
            let ballId = Number(key);
            if (ballId !== 8) {
                let dis = this.getDistance(whiteBallPos, ballDic[key]);
                validTargets.push({
                    ballId: ballId,
                    key: key,
                    distance: dis,
                    position: ballDic[key]
                });
            }
        }
        
        if (validTargets.length > 0) {
            validTargets.sort((a, b) => a.distance - b.distance);
            return validTargets[0];
        }
        return null;
    }
    
    /**
     * 模拟已确定目标球状态的选择
     */
    private simulateObjectStatusSelection(whiteBallPos: any, ballDic: any, cueBallType: number, playerObjectBalls: number[]): any {
        let validTargets = [];
        
        for (const key in ballDic) {
            let ballId = Number(key);
            
            if (playerObjectBalls.indexOf(ballId) >= 0) {
                let dis = this.getDistance(whiteBallPos, ballDic[key]);
                validTargets.push({
                    ballId: ballId,
                    key: key,
                    distance: dis,
                    position: ballDic[key]
                });
            }
        }
        
        if (validTargets.length > 0) {
            validTargets.sort((a, b) => a.distance - b.distance);
            return validTargets[0];
        }
        return null;
    }
    
    /**
     * 模拟开放状态的目标球选择
     */
    private simulateOpenBallStatusSelection(whiteBallPos: any, ballDic: any, cueBallType: number): any {
        let validTargets = [];
        
        for (const key in ballDic) {
            let ballId = Number(key);
            let bType = this.getBallType(ballId);
            
            if (bType === cueBallType && ballId !== 8) {
                let dis = this.getDistance(whiteBallPos, ballDic[key]);
                validTargets.push({
                    ballId: ballId,
                    key: key,
                    distance: dis,
                    position: ballDic[key]
                });
            }
        }
        
        if (validTargets.length > 0) {
            validTargets.sort((a, b) => a.distance - b.distance);
            return validTargets[0];
        }
        return null;
    }
    
    /**
     * 模拟被遮挡球的选择
     */
    private simulateBlockedBallsSelection(whiteBallPos: any, ballDic: any): any {
        // 简化处理：假设选择距离最近的球
        let validTargets = [];
        
        for (const key in ballDic) {
            let ballId = Number(key);
            let dis = this.getDistance(whiteBallPos, ballDic[key]);
            validTargets.push({
                ballId: ballId,
                key: key,
                distance: dis,
                position: ballDic[key],
                canDirectHit: false // 假设都被遮挡
            });
        }
        
        if (validTargets.length > 0) {
            validTargets.sort((a, b) => a.distance - b.distance);
            return validTargets[0];
        }
        return null;
    }
    
    /**
     * 计算距离
     */
    private getDistance(pos1: any, pos2: any): number {
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    /**
     * 获取球类型
     */
    private getBallType(ballId: number): number {
        if (ballId === 0) return BallType.White;
        if (ballId === 8) return BallType.Black;
        if (ballId > 0 && ballId < 8) return BallType.Solid;
        return BallType.Stripe;
    }
    
    /**
     * 更新结果显示
     */
    private updateResultDisplay() {
        if (!this.resultLabel) return;
        
        const passedTests = this.testResults.filter(r => r.passed).length;
        const totalTests = this.testResults.length;
        
        let resultText = `目标球选择测试结果\n\n`;
        resultText += `总测试数: ${totalTests}\n`;
        resultText += `通过: ${passedTests}\n`;
        resultText += `失败: ${totalTests - passedTests}\n\n`;
        
        if (passedTests === totalTests) {
            resultText += `🎉 所有测试通过！\n`;
            resultText += `目标球选择逻辑正常\n\n`;
        } else {
            resultText += `⚠️ 存在测试失败\n`;
            resultText += `需要检查目标球选择逻辑\n\n`;
        }
        
        resultText += `详细结果:\n`;
        this.testResults.forEach(result => {
            const icon = result.passed ? '✅' : '❌';
            resultText += `${icon} ${result.name}\n`;
            resultText += `  ${result.details}\n\n`;
        });
        
        this.resultLabel.string = resultText;
        
        // 输出总结到控制台
        console.log(`\n=== 目标球选择测试总结 ===`);
        console.log(`通过率: ${passedTests}/${totalTests} (${(passedTests/totalTests*100).toFixed(1)}%)`);
        
        if (passedTests === totalTests) {
            console.log(`🎉 所有目标球选择测试通过！系统工作正常。`);
        } else {
            console.warn(`⚠️ ${totalTests - passedTests}个测试失败，需要检查目标球选择逻辑。`);
        }
    }
}
