import { _decorator, Component, Label, v2, Vec2 } from 'cc';
import GameData from '../game/data/GameData';

const { ccclass, property } = _decorator;

/**
 * 轨迹精度测试组件
 * 测试辅助线预测与实际运动轨迹的一致性
 */
@ccclass('TrajectoryAccuracyTest')
export class TrajectoryAccuracyTest extends Component {
    
    @property(Label)
    resultLabel: Label = null;
    
    private testResults: Array<{name: string, passed: boolean, details: string}> = [];
    
    start() {
        this.runTrajectoryAccuracyTests();
    }
    
    /**
     * 运行轨迹精度测试
     */
    private runTrajectoryAccuracyTests() {
        console.log('=== 轨迹精度测试开始 ===');
        
        // 测试1: 物理参数一致性验证
        this.testPhysicsParameterConsistency();
        
        // 测试2: 反射公式验证
        this.testReflectionFormulaAccuracy();
        
        // 测试3: 能量损失模拟
        this.testEnergyLossSimulation();
        
        // 测试4: 阻尼效应验证
        this.testDampingEffects();
        
        // 测试5: 不同力度下的轨迹预测
        this.testPowerBasedTrajectory();
        
        console.log('=== 轨迹精度测试完成 ===');
        this.updateResultDisplay();
    }
    
    /**
     * 测试物理参数一致性
     */
    private testPhysicsParameterConsistency() {
        const testName = '物理参数一致性验证';
        
        try {
            // 检查关键物理参数是否存在
            const requiredParams = [
                'Restitution_White',
                'ballLinearDamp', 
                'ballFriction',
                'LineValue'
            ];
            
            let missingParams = [];
            let paramValues = {};
            
            for (const param of requiredParams) {
                if (GameData[param] !== undefined) {
                    paramValues[param] = GameData[param];
                } else {
                    missingParams.push(param);
                }
            }
            
            console.log(`[测试] 物理参数检查:`, paramValues);
            
            if (missingParams.length === 0) {
                // 验证参数合理性
                let validParams = true;
                let issues = [];
                
                if (GameData.Restitution_White <= 0 || GameData.Restitution_White > 1) {
                    validParams = false;
                    issues.push(`反弹系数异常: ${GameData.Restitution_White}`);
                }
                
                if (GameData.ballLinearDamp < 0 || GameData.ballLinearDamp > 1) {
                    validParams = false;
                    issues.push(`线性阻尼异常: ${GameData.ballLinearDamp}`);
                }
                
                if (GameData.ballFriction < 0 || GameData.ballFriction > 1) {
                    validParams = false;
                    issues.push(`摩擦力异常: ${GameData.ballFriction}`);
                }
                
                if (validParams) {
                    this.testResults.push({
                        name: testName,
                        passed: true,
                        details: `所有物理参数正常: 反弹=${GameData.Restitution_White}, 阻尼=${GameData.ballLinearDamp}, 摩擦=${GameData.ballFriction}`
                    });
                } else {
                    throw new Error(`参数值异常: ${issues.join(', ')}`);
                }
            } else {
                throw new Error(`缺少关键参数: ${missingParams.join(', ')}`);
            }
        } catch (error) {
            this.testResults.push({
                name: testName,
                passed: false,
                details: `错误: ${error.message}`
            });
        }
    }
    
    /**
     * 测试反射公式精度
     */
    private testReflectionFormulaAccuracy() {
        const testName = '反射公式精度验证';
        
        try {
            const testCases = [
                {
                    name: '45度入射',
                    incident: v2(1, 1).normalize(),
                    normal: v2(0, -1),
                    expectedAngle: 45
                },
                {
                    name: '30度入射',
                    incident: v2(Math.cos(Math.PI/6), Math.sin(Math.PI/6)),
                    normal: v2(0, -1),
                    expectedAngle: 30
                },
                {
                    name: '60度入射',
                    incident: v2(Math.cos(Math.PI/3), Math.sin(Math.PI/3)),
                    normal: v2(0, -1),
                    expectedAngle: 60
                }
            ];
            
            let passedCases = 0;
            let totalError = 0;
            
            for (const testCase of testCases) {
                // 使用反射公式: R = I - 2 * (I · N) * N
                const dotProduct = testCase.incident.dot(testCase.normal);
                const reflected = testCase.incident.subtract(testCase.normal.clone().multiplyScalar(2 * dotProduct)).normalize();
                
                // 计算入射角和反射角
                const incidenceAngle = Math.acos(Math.abs(dotProduct)) * 180 / Math.PI;
                const reflectionAngle = Math.acos(Math.abs(reflected.dot(testCase.normal))) * 180 / Math.PI;
                
                const angleDiff = Math.abs(incidenceAngle - reflectionAngle);
                const expectedDiff = Math.abs(incidenceAngle - testCase.expectedAngle);
                
                totalError += angleDiff;
                
                if (angleDiff < 0.1 && expectedDiff < 1) {
                    passedCases++;
                    console.log(`  ✅ ${testCase.name}: 入射角=${incidenceAngle.toFixed(1)}°, 反射角=${reflectionAngle.toFixed(1)}°, 误差=${angleDiff.toFixed(3)}°`);
                } else {
                    console.log(`  ❌ ${testCase.name}: 入射角=${incidenceAngle.toFixed(1)}°, 反射角=${reflectionAngle.toFixed(1)}°, 误差=${angleDiff.toFixed(3)}°`);
                }
            }
            
            const avgError = totalError / testCases.length;
            
            if (passedCases === testCases.length && avgError < 0.1) {
                this.testResults.push({
                    name: testName,
                    passed: true,
                    details: `反射公式精度良好: 平均误差=${avgError.toFixed(4)}°, 通过率=${passedCases}/${testCases.length}`
                });
            } else {
                throw new Error(`反射公式精度不足: 平均误差=${avgError.toFixed(4)}°, 通过率=${passedCases}/${testCases.length}`);
            }
        } catch (error) {
            this.testResults.push({
                name: testName,
                passed: false,
                details: `错误: ${error.message}`
            });
        }
    }
    
    /**
     * 测试能量损失模拟
     */
    private testEnergyLossSimulation() {
        const testName = '能量损失模拟验证';
        
        try {
            // 模拟不同力度下的能量损失
            const powerLevels = [20, 50, 80];
            let validSimulations = 0;
            
            for (const power of powerLevels) {
                const energyLoss = this.calculateEnergyLoss(power);
                const expectedRange = [0.8, 1.0]; // 能量损失应该在合理范围内
                
                if (energyLoss >= expectedRange[0] && energyLoss <= expectedRange[1]) {
                    validSimulations++;
                    console.log(`  ✅ 力度${power}: 能量保持=${energyLoss.toFixed(3)}`);
                } else {
                    console.log(`  ❌ 力度${power}: 能量保持=${energyLoss.toFixed(3)} (超出范围)`);
                }
            }
            
            if (validSimulations === powerLevels.length) {
                this.testResults.push({
                    name: testName,
                    passed: true,
                    details: `能量损失模拟正确: 所有${powerLevels.length}个力度级别都在合理范围内`
                });
            } else {
                throw new Error(`${powerLevels.length - validSimulations}个力度级别的能量损失超出合理范围`);
            }
        } catch (error) {
            this.testResults.push({
                name: testName,
                passed: false,
                details: `错误: ${error.message}`
            });
        }
    }
    
    /**
     * 测试阻尼效应
     */
    private testDampingEffects() {
        const testName = '阻尼效应验证';
        
        try {
            // 模拟球的速度衰减
            let initialVelocity = 100;
            let velocity = initialVelocity;
            const dampingFactor = this.calculateDampingFactor();
            const timeSteps = 10;
            
            let velocityHistory = [velocity];
            
            for (let step = 0; step < timeSteps; step++) {
                velocity *= dampingFactor;
                velocityHistory.push(velocity);
            }
            
            // 验证速度是否单调递减
            let isMonotonicDecrease = true;
            for (let i = 1; i < velocityHistory.length; i++) {
                if (velocityHistory[i] >= velocityHistory[i-1]) {
                    isMonotonicDecrease = false;
                    break;
                }
            }
            
            // 验证最终速度是否合理衰减
            const finalVelocity = velocityHistory[velocityHistory.length - 1];
            const decayRatio = finalVelocity / initialVelocity;
            
            if (isMonotonicDecrease && decayRatio > 0.1 && decayRatio < 0.8) {
                this.testResults.push({
                    name: testName,
                    passed: true,
                    details: `阻尼效应正常: 初始速度=${initialVelocity}, 最终速度=${finalVelocity.toFixed(1)}, 衰减比=${decayRatio.toFixed(3)}`
                });
            } else {
                throw new Error(`阻尼效应异常: 单调递减=${isMonotonicDecrease}, 衰减比=${decayRatio.toFixed(3)}`);
            }
        } catch (error) {
            this.testResults.push({
                name: testName,
                passed: false,
                details: `错误: ${error.message}`
            });
        }
    }
    
    /**
     * 测试不同力度下的轨迹预测
     */
    private testPowerBasedTrajectory() {
        const testName = '力度相关轨迹预测';
        
        try {
            const powerLevels = [25, 50, 75, 100];
            let validPredictions = 0;
            
            for (const power of powerLevels) {
                const trajectory = this.simulateTrajectory(power);
                
                // 验证轨迹特性
                const isValidTrajectory = this.validateTrajectory(trajectory, power);
                
                if (isValidTrajectory) {
                    validPredictions++;
                    console.log(`  ✅ 力度${power}: 轨迹预测有效, 长度=${trajectory.length}`);
                } else {
                    console.log(`  ❌ 力度${power}: 轨迹预测无效`);
                }
            }
            
            if (validPredictions === powerLevels.length) {
                this.testResults.push({
                    name: testName,
                    passed: true,
                    details: `力度相关轨迹预测正确: 所有${powerLevels.length}个力度级别的预测都有效`
                });
            } else {
                throw new Error(`${powerLevels.length - validPredictions}个力度级别的轨迹预测无效`);
            }
        } catch (error) {
            this.testResults.push({
                name: testName,
                passed: false,
                details: `错误: ${error.message}`
            });
        }
    }
    
    /**
     * 计算能量损失因子
     */
    private calculateEnergyLoss(power: number): number {
        const normalizedPower = Math.max(0, Math.min(100, power)) / 100;
        return 0.85 + (normalizedPower * 0.1); // 0.85-0.95
    }
    
    /**
     * 计算阻尼因子
     */
    private calculateDampingFactor(): number {
        const linearDamp = GameData.ballLinearDamp || 0.1;
        const friction = GameData.ballFriction || 0.1;
        return 1 - (linearDamp * 0.5 + friction * 0.3);
    }
    
    /**
     * 模拟轨迹
     */
    private simulateTrajectory(power: number): Array<Vec2> {
        const trajectory: Array<Vec2> = [];
        const startPos = v2(0, 0);
        const direction = v2(1, 0);
        
        let velocity = direction.clone().multiplyScalar(power * 0.1);
        let currentPos = startPos.clone();
        
        const timeStep = 0.016; // 60fps
        const dampingFactor = this.calculateDampingFactor();
        
        for (let step = 0; step < 100 && velocity.length() > 0.1; step++) {
            velocity.multiplyScalar(dampingFactor);
            currentPos = currentPos.add(velocity.clone().multiplyScalar(timeStep));
            trajectory.push(currentPos.clone());
        }
        
        return trajectory;
    }
    
    /**
     * 验证轨迹有效性
     */
    private validateTrajectory(trajectory: Array<Vec2>, power: number): boolean {
        if (trajectory.length === 0) return false;
        
        // 验证轨迹长度与力度的关系
        const expectedMinLength = Math.floor(power / 10);
        const expectedMaxLength = Math.floor(power / 2);
        
        if (trajectory.length < expectedMinLength || trajectory.length > expectedMaxLength) {
            return false;
        }
        
        // 验证轨迹的连续性
        for (let i = 1; i < trajectory.length; i++) {
            const distance = trajectory[i].subtract(trajectory[i-1]).length();
            if (distance > 50) { // 步长不应该太大
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 更新结果显示
     */
    private updateResultDisplay() {
        if (!this.resultLabel) return;
        
        const passedTests = this.testResults.filter(r => r.passed).length;
        const totalTests = this.testResults.length;
        
        let resultText = `轨迹精度测试结果\n\n`;
        resultText += `总测试数: ${totalTests}\n`;
        resultText += `通过: ${passedTests}\n`;
        resultText += `失败: ${totalTests - passedTests}\n\n`;
        
        if (passedTests === totalTests) {
            resultText += `🎉 所有测试通过！\n`;
            resultText += `辅助线与实际轨迹一致性良好\n\n`;
        } else {
            resultText += `⚠️ 存在轨迹精度问题\n`;
            resultText += `需要调整物理参数或计算公式\n\n`;
        }
        
        resultText += `详细结果:\n`;
        this.testResults.forEach(result => {
            const icon = result.passed ? '✅' : '❌';
            resultText += `${icon} ${result.name}\n`;
            resultText += `  ${result.details}\n\n`;
        });
        
        this.resultLabel.string = resultText;
        
        // 输出总结到控制台
        console.log(`\n=== 轨迹精度测试总结 ===`);
        console.log(`通过率: ${passedTests}/${totalTests} (${(passedTests/totalTests*100).toFixed(1)}%)`);
        
        if (passedTests === totalTests) {
            console.log(`🎉 所有轨迹精度测试通过！辅助线预测准确。`);
        } else {
            console.warn(`⚠️ ${totalTests - passedTests}个测试失败，辅助线精度需要改进。`);
        }
    }
}
