import { Vec2, v2 } from 'cc';
import Global from '../data/Global';

/**
 * 物理世界缩放补偿器
 * 用于解决ResolutionPolicy.FIXED_WIDTH导致的物理世界拉伸问题
 */
export class PhysicsCompensator {
    private static _instance: PhysicsCompensator = null;
    
    public static get instance(): PhysicsCompensator {
        if (!this._instance) {
            this._instance = new PhysicsCompensator();
        }
        return this._instance;
    }
    
    /**
     * 补偿方向向量，修正Y轴拉伸
     * @param direction 原始方向向量
     * @returns 补偿后的方向向量
     */
    compensateDirection(direction: Vec2): Vec2 {
        if (Global.physicsScaleRatio === 1) {
            return direction.clone();
        }
        
        const compensated = direction.clone();
        compensated.y *= Global.physicsScaleRatio;
        return compensated.normalize();
    }
    
    /**
     * 补偿力度，确保不同分辨率下物理行为一致
     * @param power 原始力度
     * @returns 补偿后的力度
     */
    compensatePower(power: number): number {
        if (Global.physicsScaleX === 1 && Global.physicsScaleY === 1) {
            return power;
        }
        
        // 使用平均缩放比例来补偿力度
        const avgScale = (Global.physicsScaleX + Global.physicsScaleY) / 2;
        return power * avgScale;
    }
    
    /**
     * 补偿位置坐标，用于物理世界坐标转换
     * @param position 原始位置
     * @returns 补偿后的位置
     */
    compensatePosition(position: Vec2): Vec2 {
        if (Global.physicsScaleX === 1 && Global.physicsScaleY === 1) {
            return position.clone();
        }
        
        return v2(
            position.x * Global.physicsScaleX,
            position.y * Global.physicsScaleY
        );
    }
    
    /**
     * 反向补偿位置坐标，用于从物理世界坐标转换回显示坐标
     * @param position 物理世界位置
     * @returns 显示坐标位置
     */
    decompensatePosition(position: Vec2): Vec2 {
        if (Global.physicsScaleX === 1 && Global.physicsScaleY === 1) {
            return position.clone();
        }
        
        return v2(
            position.x / Global.physicsScaleX,
            position.y / Global.physicsScaleY
        );
    }
    
    /**
     * 补偿速度向量
     * @param velocity 原始速度
     * @returns 补偿后的速度
     */
    compensateVelocity(velocity: Vec2): Vec2 {
        if (Global.physicsScaleRatio === 1) {
            return velocity.clone();
        }
        
        return v2(
            velocity.x,
            velocity.y * Global.physicsScaleRatio
        );
    }
    
    /**
     * 获取当前的物理补偿信息
     * @returns 补偿信息对象
     */
    getCompensationInfo() {
        return {
            scaleX: Global.physicsScaleX,
            scaleY: Global.physicsScaleY,
            scaleRatio: Global.physicsScaleRatio,
            isCompensationNeeded: Global.physicsScaleRatio !== 1
        };
    }
    
    /**
     * 重置补偿参数
     */
    reset() {
        Global.physicsScaleX = 1;
        Global.physicsScaleY = 1;
        Global.physicsScaleRatio = 1;
    }
    
    /**
     * 调试输出补偿信息
     */
    debugLog() {
        const info = this.getCompensationInfo();
        console.log('[PhysicsCompensator] 补偿信息:', info);
    }
}
