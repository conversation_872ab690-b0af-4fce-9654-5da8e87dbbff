// Learn TypeScript:
//  - [Chinese] https://docs.cocos.com/creator/manual/zh/scripting/typescript.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - [Chinese] https://docs.cocos.com/creator/manual/zh/scripting/reference/attributes.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - [Chinese] https://docs.cocos.com/creator/manual/zh/scripting/life-cycle-callbacks.html
//  - [English] http://www.cocos2d-x.org/docs/creator/manual/en/scripting/life-cycle-callbacks.html

import { _decorator, Component, v2, PhysicsSystem2D, EPhysics2DDrawFlags, Director, director } from "cc";
import { PREVIEW } from "cc/env";
import { EnumEvent, EventMgr } from "../event/EventManager";
const { ccclass, property } = _decorator;

@ccclass
export default class PhysicalManagerCustom extends Component {

    // @property(cc.Label)
    // label: cc.Label = null;

    // @property
    // text: string = 'hello';

    @property({ tooltip: "是否启用物理引擎" })
    active: boolean = true;

    @property({ tooltip: "是否显示包围盒" })
    aabb: boolean = true;

    @property
    pair: boolean = true;

    @property({ tooltip: "是否显示中心点" })
    centerOfMass: boolean = true;

    @property({ tooltip: "是否显示关节连接线" })
    joint: boolean = true;

    @property({ tooltip: "是否填充形状" })
    shape: boolean = true;

    @property({ tooltip: "是否开启鼠标关节，可以拖动动态刚体" })
    mouseJoint: boolean = false;

    @property({ tooltip: "重力" })
    gravity = v2(0, -320);

    public static _subStepCount = 0;
    public static _accumulator = 0;
    public static _postUpdateStepCount = 0;

    start() {

    }

    onEnable() {
        //开启或关闭物理系统
        let physicsSystem2D = PhysicsSystem2D.instance;
        if (physicsSystem2D.enable && this.active) {
            console.warn('The physical system is enabled！');
        }
        physicsSystem2D.enable = this.active;

        if (!this.active) {
            return;
        }
        //设置物理系统的重力属性
        physicsSystem2D.gravity = this.gravity;

        //设置调试标志
        if (PREVIEW) {
            physicsSystem2D.debugDrawFlags = EPhysics2DDrawFlags.Aabb | EPhysics2DDrawFlags.Pair |
                EPhysics2DDrawFlags.CenterOfMass |
                EPhysics2DDrawFlags.Joint |
                EPhysics2DDrawFlags.Shape;
        } else {
            physicsSystem2D.debugDrawFlags = 0;
        }
    }

    onDisable() {
        let physicsSystem2D = PhysicsSystem2D.instance;
        physicsSystem2D.debugDrawFlags = 0;
        physicsSystem2D.enable = false;
    }

    //#region 可动态调度的物理刷新接口
    public static postUpdate(deltaTime: number) {
        // console.log(PhysicalManagerCustom._postUpdateStepCount, '=========PhysicsSystem2D.instance.enable:', PhysicsSystem2D.instance.enable);
        if (!PhysicsSystem2D.instance.physicsWorld) return;
        if (!PhysicsSystem2D.instance.enable) {
            PhysicsSystem2D.instance.physicsWorld.syncSceneToPhysics();
            return;
        }
        PhysicalManagerCustom._subStepCount = 0;
        PhysicalManagerCustom._accumulator += deltaTime;
        director.emit(Director.EVENT_BEFORE_PHYSICS);

        while (PhysicalManagerCustom._subStepCount < PhysicsSystem2D.instance.maxSubSteps) {
            if (PhysicalManagerCustom._accumulator >= PhysicsSystem2D.instance.fixedTimeStep) {
                PhysicsSystem2D.instance.physicsWorld.syncSceneToPhysics();
                PhysicsSystem2D.instance.physicsWorld.step(PhysicsSystem2D.instance.fixedTimeStep);
                // PhysicsSystem2D.instance.physicsWorld.emitEvents();
                PhysicsSystem2D.instance.physicsWorld.syncPhysicsToScene();//syncAfterEvents
                PhysicalManagerCustom._accumulator -= PhysicsSystem2D.instance.fixedTimeStep;
                PhysicalManagerCustom._subStepCount++;
            } else {
                PhysicsSystem2D.instance.physicsWorld.syncSceneToPhysics();
                break;
            }
        }

        // PhysicalManagerCustom._postUpdateStepCount++;
        // // console.log('====PhysicalManagerCustom====PhysicalManagerCustom._postUpdateStepCount=', PhysicalManagerCustom._postUpdateStepCount);
        // if (PhysicalManagerCustom._postUpdateStepCount === 120) {
        //     EventMgr.raiseEvent(EnumEvent.POST_UPDATEEND);
        // }
        director.emit(Director.EVENT_AFTER_PHYSICS);
    }


    // //#region 可动态调度的物理刷新接口
    // public static postUpdate(deltaTime: number) {
    //     console.log(!PhysicsSystem.instance.physicsWorld, '=========PhysicsSystem.instance.enable:', PhysicsSystem.instance.enable);
    //     if (!PhysicsSystem.instance.physicsWorld) return;
    //     if (!PhysicsSystem.instance.enable) {
    //         PhysicsSystem.instance.physicsWorld.syncSceneToPhysics();
    //         return;
    //     }
    //     PhysicalManagerCustom._subStepCount = 0;
    //     PhysicalManagerCustom._accumulator += deltaTime;
    //     director.emit(Director.EVENT_BEFORE_PHYSICS);

    //     while (PhysicalManagerCustom._subStepCount < PhysicsSystem.instance.maxSubSteps) {
    //         if (PhysicalManagerCustom._accumulator >= PhysicsSystem.instance.fixedTimeStep) {
    //             PhysicsSystem.instance.physicsWorld.syncSceneToPhysics();
    //             PhysicsSystem.instance.physicsWorld.step(PhysicsSystem.instance.fixedTimeStep);
    //             PhysicsSystem.instance.physicsWorld.emitEvents();
    //             PhysicsSystem.instance.physicsWorld.syncAfterEvents();
    //             PhysicalManagerCustom._accumulator -= PhysicsSystem.instance.fixedTimeStep;
    //             PhysicalManagerCustom._subStepCount++;
    //         } else {
    //             PhysicsSystem.instance.physicsWorld.syncSceneToPhysics();
    //             break;
    //         }
    //     }
    //     PhysicalManagerCustom._postUpdateStepCount++;
    //     if (PhysicalManagerCustom._postUpdateStepCount === 120) {
    //         EventCenter.emit("postUpdateEnd");
    //     }
    //     director.emit(Director.EVENT_AFTER_PHYSICS);
    // }

    // update (dt) {}
}
