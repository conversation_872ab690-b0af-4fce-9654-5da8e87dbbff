import { IProtocolHelper } from "./IProtocolHelper";
import Global from "../data/Global";
import { ISocket } from "./NetInterface";
import { WebSock } from "./WebSock";
import { ReconnectCtrl } from "../../common/reconnect/ReconnectCtrl";
import { dialogManager } from "../../common/dialog/DialogManager";
import i18n from "../tools/i18n";
import { EventMgr } from "../event/EventManager";
import GameService from "../../game/data/GameService";
import MD5Sign from "../../game/data/MD5Sign";
import { GameUtil } from "../tools/GameUtil";
import Utils from "../tools/Utils";
import { JsbWebSocket, SocketEnumEvent } from "./JsbWebSocket";
import { JsbBrid } from "../tools/JsbBridge";
import { DeviceType } from "../../game/data/Enum";
/**
 * 游戏Socket基类 包含断连 心跳功能 
 * 已经做了区分浏览器｜原生IOS/Android环境 使用的时候直接调用init即可
 */
export class GameSocket {
	protected protocolHelper: IProtocolHelper = null;
	protected webSock: ISocket | null = null;
	protected connectOptions: NetConnectOptions = null;
	protected _heartTime: number = 5000;//心跳包多久发送一次
	protected _receiveTime: number = 8000;//多久没收到消息断开主动连接
	protected _reconnetTimeOut: number = 5000;// 重连间隔
	private _keepAliveTimer = null;//心跳发送定时器
	private _receiveMsgTimer = null;//心跳超时定时器 时间到开始进行断线重连
	private _reconnectTimer = null;//重连间隔定时器
	private autoReconnect: boolean = true;//是否自动重连 为false的时候不回进行自动重连
	protected onMessageCB: Function = null;//收到消息的回调 注意this的指向
	private onOpenCB: Function = null;//连接成功的回调 一般进行票据登录相关处理
	private onCloseCB: Function = null;//连接成功的回调 一般进行票据登录相关处理

	public init(protocolHelper: IProtocolHelper, onOpen: Function, onMessage: Function, onClose: Function = null) {
		this.protocolHelper = protocolHelper;
		this.onMessageCB = onMessage;
		this.onOpenCB = onOpen;
		this.onCloseCB = onClose;
		this.connectOptions = { url: Global.gameInfo.url};
		// this.connect(this.connectOptions);
		this.addEventListener();

		if (Utils.IsBrowser()) {//浏览器登录
			this.browserInit();
		} else {//原生登录
			this.nativeInit();
		}
	}
	private addEventListener() {
		EventMgr.addEventListener(SocketEnumEvent.Socket_Open, this.onOpen, this);
		EventMgr.addEventListener(SocketEnumEvent.Socket_Error, this.onError, this);
		EventMgr.addEventListener(SocketEnumEvent.Socket_Message, this.onMessage, this);
		EventMgr.addEventListener(SocketEnumEvent.Socket_Close, this.onClose, this);
		EventMgr.addEventListener(ReconnectCtrl.Event.TIMEOVER, this.timeOver, this);
	}
	private removeEventListener() {
		EventMgr.removeEventListener(SocketEnumEvent.Socket_Open, this.onOpen, this);
		EventMgr.removeEventListener(SocketEnumEvent.Socket_Error, this.onError, this);
		EventMgr.removeEventListener(SocketEnumEvent.Socket_Message, this.onMessage, this);
		EventMgr.removeEventListener(SocketEnumEvent.Socket_Close, this.onClose, this);
		EventMgr.removeEventListener(ReconnectCtrl.Event.TIMEOVER, this.timeOver, this);
	}

	private nativeInit() {//原生使用
		GameUtil.logCatch("==nativeInit==");
		if (JsbBrid.deviceType == DeviceType.Android) {//Android 需要使用Int8Array IOS默认不变;
			this.send = this.sendAndroid;
		}
		ReconnectCtrl.ins.connect(false);
		JsbWebSocket.instance.connectSocket();
	}

	public browserInit(): boolean {
		if (!this.webSock) {
			this.webSock = new WebSock();
			this.webSock.onOpen = (event) => { this.onOpen() };
			this.webSock.onMessage = (msg) => { this.onMessage('',msg) };
			this.webSock.onError = (event) => { this.onError() };
			this.webSock.onClosed = (event) => { this.onCloseCB(event) };
			this.sendNetReConnect = this.connectUrl.bind(this);
			this.send = this.webSock.send.bind(this.webSock);
		}
		if (this.webSock) {
			this.connectUrl();
			return true;
		}
		return false;
	}
	
	public send(buff: Uint8Array) {
		// this.webSock && this.webSock.send(buff);
		let arr: Array<number> = Array.prototype.slice.call(buff);
		arr && JsbWebSocket.instance.sendNetMsg(arr);
	}
	private sendAndroid(buff: Uint8Array) {//android 特殊处理转换为Int8Array 之后再转换为 number[]
		let arr: Array<number> = Array.prototype.slice.call(new Int8Array(buff));
		arr && JsbWebSocket.instance.sendNetMsg(arr);
	}
	
	public closeSocket() {//关闭socket 
		GameUtil.log("=closeSocket=");
		//TODO::发起socket连接 时才需要通知原生关闭close
		JsbWebSocket.instance.sendNetClose();
		this.onClose();
	}
	public cleanSocket() {//关闭并清理socket
		GameUtil.log("cleanSocket");
		this.autoReconnect = false;
		this.clearAllTimer();
		this.removeEventListener();
		this.closeSocket();
		ReconnectCtrl.ins.closeView();
	}

	public onOpen() {
		GameUtil.log("gamesocket onOpen");
		Global.isconnect = true;
		ReconnectCtrl.ins.connectSucss();
		this.autoReconnect = true;
		this.clearAllTimer();
		this.resetHearbeatTimer();
		this.onOpenCB && this.onOpenCB();
	}
	public onMessage(eventName, msg) {
		let uintArr = new Uint8Array(msg);
		if (this.protocolHelper.checkPackage(uintArr)) {//消息体合法验证
			this.resetReceiveMsgTimer();
			let body = this.protocolHelper.getPackageMsg(uintArr.buffer);
			this.onMessageCB && this.onMessageCB(body);
		}
	}
	public onError() {
		GameUtil.log("gamesocket onError");
		this.onClose();
	}
	public onClose(event?) {
		GameUtil.log(this.autoReconnect + "Gamesocket onClose " + !Global.isconnect);
		if (!Global.isconnect) return;//过滤原生重复调用
		GameUtil.log("Gamesocket onClose run:" + Date.now());
		Global.isconnect = false;
		this.clearAllTimer();
		this.autoReconnect && this.reconnect();
		this.onCloseCB && this.onCloseCB(event);
	}

	private timeOver() {
		this.autoReconnect = false;
		this.clearAllTimer()
		ReconnectCtrl.ins.closeView();
		dialogManager.showConfirm({
			tip: i18n.t("dialog_NetLost"),
			confirm: this.connectAgain.bind(this),
			cancel: () => {
				GameService.instance.backHall(true);
			},
			confirmTx: i18n.t("connect"),
			cancelTx: i18n.t("exit")
		})
	}
	/** 重连弹框中的再次重连按钮 */
	private connectAgain() {
		GameUtil.log("Gamesocket connectAgain");

		this.autoReconnect = true;
		ReconnectCtrl.ins.param.leftTime = 10;
		ReconnectCtrl.ins.isReconnect = false;//TODO::重连，小马动画和确认框都以已经关闭，
		ReconnectCtrl.ins.connect(true, ReconnectCtrl.ins.param);

		this.sendNetReConnect();
		this.resetReconnectTimer();
	}

	private sendNetReConnect() {
		GameUtil.log("Gamesocket sendNetReConnect");
		JsbWebSocket.instance.sendNetReConnect();
		// if (this.webSock) {
		// 	this.webSock.close();
		// 	this.connectUrl()
		// }

	}
	/**
	 * 连socket前要更新签名
	 */
	private connectUrl() {
		let self = this;
		MD5Sign.generateSocketSign((options) => {
			GameUtil.log("=connectUrl=:",options);
			self.webSock.connect(options);
		})
	}
	private reconnect() {//触发断线重连
		this.protocolHelper.showConectUI() && ReconnectCtrl.ins.connect();
		this.sendNetReConnect();
		this.resetReconnectTimer();
		GameUtil.log("Gamesocket reconnect");
	}

	private resetReconnectTimer() {
		GameUtil.log("Gamesocket resetReconnectTimer");
		clearTimeout(this._reconnectTimer);
		this._reconnectTimer = setTimeout(this.reconnect.bind(this), this._reconnetTimeOut);
	}

	private clearAllTimer() {//清理所有的计时器
		clearTimeout(this._receiveMsgTimer);
		clearTimeout(this._reconnectTimer);
		clearInterval(this._keepAliveTimer);
	}
	private resetReceiveMsgTimer() {//重置心跳超时定时器
		clearTimeout(this._receiveMsgTimer);
		this._receiveMsgTimer = setTimeout(() => {
			GameUtil.log("心跳超时");
			this.closeSocket();
		}, this._receiveTime);
	}
	/**
	 * 如果服务端在50s内未接受到心跳信息，则主动断开socket
	 * 重置心跳发送定时器
	 * 心跳5s发送一次，onOpen后就开始发送
	 */
	private resetHearbeatTimer() {
		clearInterval(this._keepAliveTimer);
		this._keepAliveTimer = setInterval(() => {
			this.send(this.protocolHelper.getHearbeat());
		}, this._heartTime);
	}
}

export interface NetConnectOptions {
	host?: string,              // 地址
	port?: number,              // 端口
	url?: string,               // url，与地址+端口二选一
	autoReconnect?: number,     // -1 永久重连，0不自动重连，其他正整数为自动重试次数
}