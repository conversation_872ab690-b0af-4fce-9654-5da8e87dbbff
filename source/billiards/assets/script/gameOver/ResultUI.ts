import { _decorator, sp, Sprite,Node, Prefab, instantiate, v3, tween } from 'cc';
import Global from '../core/data/Global';
import GameData from '../game/data/GameData';
import { ResultListItem } from './ResultListItem';
import { UIView } from '../core/ui/UIView';
import { uiManager } from '../core/ui/UIManager';
import { resLoader } from '../core/res/ResLoader';
import { GameUtil } from '../core/tools/GameUtil';
import { UIID } from '../Main';
import GameService from '../game/data/GameService';
const { ccclass, property } = _decorator;

// export type ItemData = { userId: number, nickName: string, avatar: string, goals: number, winGold: number };
type ParsedParams = { list: PlayerInfo[] }

@ccclass('ResultUI')
export class ResultUI extends UIView {
    @property(Prefab)
    private pfIListtem: Prefab = null;
    @property(Node)
    private btnBack: Node = null;
    @property(Node)
    private btnAgain: Node = null;

    private imgBgSp: Sprite = null;
    private imgItemBgSp: Sprite = null;
    private imgTitle: Sprite = null;
    private skDown: sp.Skeleton = null;
    private skUp: sp.Skeleton = null;

    private resultMsg: ParsedParams = { list: [] };
    /**自己是否winner */
    private isWinner: boolean = false;

    /** 上次点击开始按钮的时间戳 */
    private _lastClickTime: number = 0;

    private _types = {
        win: 1,
        lose: -1,
        gameover: 0
    }

    onOpen(uid, resultMsg: any) {
        GameUtil.log("=open==结算消息===");
        GameUtil.log(resultMsg);
        this.parseData(resultMsg);    
    }
    
    start() {
        this.imgBgSp = this.node.getChildByName('img_bg').getComponent(Sprite);
        this.imgItemBgSp = this.node.getChildByName('img_itemBg').getComponent(Sprite);
        this.imgTitle = this.node.getChildByName('img_title').getComponent(Sprite);
        this.skDown = this.node.getChildByName('sk_down').getComponent(sp.Skeleton);
        this.skUp = this.node.getChildByName('sk_up').getComponent(sp.Skeleton);
        if (Global.isArabic) {
            this.btnBack.setPosition(175, -300);
            this.btnAgain.setPosition(-175, -300);
        }
        this.btnAgain.active = this.btnBack.active = false;
        this._init();
        this._gameReset();
    }

    /**解析数据 */
    parseData(data) {
        this.resultMsg = {
            list: []
        };
        let players = data.players;
        let ballType = GameData.checkBallType(players);
        let enterBallList = (data.enterBallList && typeof data.enterBallList == 'string') ? JSON.parse(data.enterBallList) : [];
        for (let i = 0; i < players.length; i++) {
            const player = players[i];
            player.goals = GameData.getEnterBallByIdx(player, enterBallList, ((player.ballType < 0 && ballType > 0) ? 5 - ballType : player.ballType));
            const id = player.playerId;
            if (player){
                if (player.isWinner) {
                    this.isWinner = id == Global.userId;
                    this.resultMsg.list.unshift(player);
                } else {
                    this.resultMsg.list.push(player);
                }
            }
        }
    }

    private _init() {
        Global.roundNum++;
        let titleFileName, bgFileName, itemBgFileName, skFileName, fileExt = '';

        if (Global.isArabic) fileExt = '_ar';
        if (this.isWinner) {
            skFileName = 'win';
            bgFileName = 'image_winBg';
            titleFileName = 'image_titleWin' + fileExt;
            itemBgFileName = 'image_winItemBg';

        } else {
            skFileName = 'lose';
            bgFileName = 'image_loseBg';
            titleFileName = 'image_titleLose' + fileExt;
            itemBgFileName = 'image_loseItemBg';
        }
        GameUtil.setSpriteFrame(this.imgBgSp, 'texture/gameOver/' + bgFileName);
        GameUtil.setSpriteFrame(this.imgItemBgSp, 'texture/gameOver/' + itemBgFileName);
        
        resLoader.load("spine/result/" + skFileName, sp.SkeletonData, null, (err: any, spineAsset: sp.SkeletonData) => {
            if (!err) {
                this.skDown.skeletonData = spineAsset;
                this.skUp.skeletonData = spineAsset;
                this.skDown.setAnimation(0, 'idle_down', false);
                this.skUp.setAnimation(0, 'idle_up', false);
            } else {
                GameUtil.log(err);
            }

            GameUtil.setSpriteFrame(this.imgTitle, 'texture/gameOver/' + titleFileName);
            this.initResultAni();
        });
    }

    private initResultAni(): void{
        let self = this;
        let list = this.resultMsg?.list, startPos = 91, gap = 150;
        for (let i = 0; i < list.length; i++) {
            let itemData = list[i];
            let isMe = itemData.playerId == Global.userId;
            let node = instantiate(this.pfIListtem);
            this.node.addChild(node);
            node.setPosition(v3(0, startPos - i * gap));
            let listItem = node.getComponent(ResultListItem);
            listItem.init(i == 0, isMe, itemData);
            //TODO::信息栏bg出现时间：0.2秒，持续时长：0.3秒；信息出现时间：0.4秒，持续时长：0.4秒
            listItem.showWithAni(i * 0.1 + 0.4); 

            if (isMe) {
                if (itemData.gold) {
                    Global.currentGoldNum += itemData.gold;
                }
            }
        }

        //TODO::标题
        tween(this.imgTitle.node).set({ scale: v3(2, 1, 1) }).
            to(0.3, { scale: v3(1, 1, 1) }).start();
        //TODO::信息栏bg
        tween(this.imgItemBgSp.node).set({ scale: v3(0, 1, 1) }).delay(0.2).
            to(0.2, { scale: v3(1, 1, 1) },
                {
                    onComplete(target?: any) {
                        self.btnAgain.active = self.btnBack.active = true;
                    }
                }).
            start();
    }

    private _gameReset() {
        Global.seatsInfo = [];
        Global.isGaming = Global.isOnSeat = false;
    }
    /**关闭后回到匹配赌注选择界面 */
    onClickBack() {
        GameService.instance.backToGameMode();
        uiManager.close(this);
    }

    /**关闭后回到匹配开始 */
    onClickPlayAgain() {
        // 防抖处理
        const now = Date.now();
        if (now - this._lastClickTime < 300) {
            return;
        }
        this._lastClickTime = now;

        uiManager.closeAllDialog();

        Global.gameState = 0;
        uiManager.open(UIID.Match, { isMatch: true, coin: Global.cost });
        let gameUI = uiManager.getUI(UIID.BilliardGame);
        GameUtil.log(!!gameUI+"===结算再来一局赌注===" + Global.cost);
        if (gameUI) uiManager.close(gameUI);
        uiManager.close(this);
    }

    onDestroy() { };
}

