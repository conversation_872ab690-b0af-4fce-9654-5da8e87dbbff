import { _decorator, Component, Prefab, instantiate, v2, Node, Label, PhysicsSystem2D, director, profiler, SpriteFrame, Material, Contact2DType, Collider2D, IPhysics2DContact, PhysicsGroup, v3, game, view, ResolutionPolicy, screen } from 'cc';
import { JsbBrid } from './core/tools/JsbBridge';
import { Ball } from './game/ball/Ball';
import { Random } from './core/tools/Math';
import Global from './core/data/Global';
const { ccclass, property } = _decorator;

@ccclass('Test')
export class Test extends Component {
    // @property(Prefab)
    // ball: Prefab = null;
    // @property(Prefab)
    // whiteBall: Prefab = null;
    @property(Material)
    b1: Material = null;
    @property(Material)
    b2: Material = null;
    @property([SpriteFrame])
    private textures = [];
    @property(Prefab)
    testBall: Prefab = null;
    // @property(Prefab)
    // private pfBallFront: Prefab = null;
    // @property(Prefab)
    // private pfBallBack: Prefab = null;
    // @property(Node)
    // private ballBackNode: Node = null;
    // @property(Node)
    // private ballFrontNode: Node = null;

    @property(Label)
    private testLabel = null;
    // @property(RichText)
    // private testRichLabel = null;
    @property(Label)
    private tipLabel = null;

    ballCtrls: Ball[] = [];

    label: Label = null;

    isShoot = false;
    shootCount = 0;
    setpCount = 0;

    private _ballList: Array<Ball> = [];

    onLoad() {
        // this.setWindowSize();
        JsbBrid.initNative();
        profiler.showStats()
        game.frameRate = 60;
        // console.log('WebAssembly: ' + Utils.iswebasm());
        director.unregisterSystem(PhysicsSystem2D.instance);
        PhysicsSystem2D.instance.enable = true;
        Random.setSeed(123456);
        PhysicsSystem2D.instance.fixedTimeStep = Global.dt;
        PhysicsSystem2D.instance.maxSubSteps = 3; // 防止大延迟导致的异常

        // setDisplayStats(true)
        // let camera = find('Canvas/Camera').getComponent(Camera);
        // if (screen.windowSize.height < 1400) {
        //     camera.orthoHeight = 600;
        //     camera.node.setPosition(v3(0, 100, 1000));
        // }
        JsbBrid.gameLoadingProgress(200);
        // JsbBrid.onGameReady();
    }

    // setWindowSize() {
    //     let visibleSize = view.getVisibleSize();
    //     let winsize = screen.windowSize;
    //     let ratio = winsize.width / winsize.height;
    //     let drs = view.getDesignResolutionSize();
    //     let drsRatio = drs.width / drs.height;
    //     let scale = 0;
    //     if (ratio > drsRatio) {
    //         view.setResolutionPolicy(ResolutionPolicy.FIXED_HEIGHT);
    //         scale = winsize.height / drs.height;
    //     } else {
    //         view.setResolutionPolicy(ResolutionPolicy.FIXED_WIDTH);
    //         scale = winsize.width / drs.width;
    //     }
    //     Global.visibleWidth = visibleSize.width;
    //     Global.visibleHeight = visibleSize.height;
    //     Global.screen_scale = scale;
    //     console.log("==屏幕锁放比==" + scale);
    // }
    
    start() {
        JsbBrid.removeMatchView();
        console.log("===Test start===");
        let nums = [1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 15];
        nums.splice(4, 0, 8); //8号球固定在第5个

        //初始化除白球外的15个球
        const gap = 30, startY = 150;
        let index = 0;
        for (let i = 0; i < 5; i++) {
            for (let j = 0; j <= i; j++) {
                let ball = this._initBall(nums[index++]);
                let xx = j * gap - i * gap * 0.5;
                // if (i % 2 == 0) xx += 0.5;
                // else xx -= 0.5;
                ball.node.setPosition(xx, i * (gap - 2) + startY);
                this._ballList.push(ball);
            }
        }

        //初始化白球
        let cueBall = this._initBall(0);
        cueBall.node.setPosition(0, -188);
        let ctrl = cueBall.getComponent(Ball);

        this.node.on(Node.EventType.TOUCH_START, () => {
            if (this.isShoot) return;
            this.shootCount++;
            this.isShoot = true;
            this.setpCount = 0;

            const impulse = this.shootCount % 2 == 0 ? v2(100, 100) : v2(-100, -100);//v2(0, GameData.PowerRange);//v2(0, power);//
            ctrl.rb2D.applyLinearImpulseToCenter(impulse, true);


            // const percent = 0;
            // const impulse = GameData.getHitballPower(1);
            // Vec3.rotateY(impulse, impulse, v3(0, 0, 0), 0.3);
            // ctrl.rb2D.applyLinearImpulseToCenter(v2(0.1, impulse), true);

            // setTimeout(() => {
            //     this._ballList.forEach(element => {
            //         console.log(element.rb2D.linearVelocity, ':linearVelocity===0==ballId:', element.ballId);
            //         if (element.rb2D.linearVelocity.x == 0 && element.rb2D.linearVelocity.y == 0) {
            //             element.rb2D.applyLinearImpulseToCenter(v2(0, impulse.y * Math.random() / 5), true);
            //         }
            //     });
            // }, 300);

            // setTimeout(() => {
            //     this._ballList.forEach(element => {
            //         console.log(element.rb2D.linearVelocity, ':linearVelocity===0==ballId:', element.ballId);
            //     });
            // }, 1200);

        }, this)

        this.label = this.node.getChildByName('Label').getComponent(Label);

        PhysicsSystem2D.instance.on(Contact2DType.BEGIN_CONTACT, this.onEndContactBalls, this);
    }

    update(dt) {
        let isAllStop = true;
        for (let i = 0; i < 16; i++) {
            if (this.ballCtrls[i] && this.ballCtrls[i].speed) isAllStop = false;
        }
        // this.label.string = 'shootCnt1:' + this.shootCount + '\nallstop: ' + (isAllStop ? 'yes' : 'no') + '\ncount: ' + this.setpCount;

        PhysicsSystem2D.instance.postUpdate(PhysicsSystem2D.instance.fixedTimeStep)
        // this.setpCount++;

        if (this.isShoot) {
            this.setpCount++;
            // PhysicsSystem2D.instance.postUpdate(PhysicsSystem2D.instance.fixedTimeStep)
            if (this.setpCount == 360) {
                this.isShoot = false;
                this._stopAll();
            }
        }
    }

    private _initBall(num: number) {
        // let backNode = instantiate(this.pfBallBack);
        // let frontNode = instantiate(this.pfBallFront);
        // this.ballBackNode.addChild(backNode);
        // this.ballFrontNode.addChild(frontNode);

        let ballBox = this.node.getChildByPath('MainGame/gameUI/tableLayer/table');
        let node = instantiate(this.testBall);
        ballBox.insertChild(node, 4);
        let ball = node.getComponent(Ball);
        ball.init(num, this.textures[num], num > 8 ? this.b2 : this.b1);
        // ball.init(num, this.textures[num], backNode, frontNode);
        this.ballCtrls.push(ball.getComponent(Ball));
        return ball;
    }

    private _stopAll() {
        let ctrls = this.ballCtrls;
        for (let i in ctrls) {
            let rb2D = ctrls[i].rb2D;
            // rb2D.sleep();
            rb2D.linearVelocity = v2(0, 0);
            rb2D.angularVelocity = 0;
        }
    }

    private onEndContactBalls(selfCollider: Collider2D, otherCollider: Collider2D, contact: IPhysics2DContact = null) {
            // console.log(selfCollider.group + "=onEndContactBalls==" + otherCollider.group, PhysicsGroup['balls']);
            if (selfCollider.group == PhysicsGroup['balls'] && otherCollider.group == PhysicsGroup['balls']) {
                let selfBall = selfCollider.node.getComponent(Ball);
                let otherBall = otherCollider.node.getComponent(Ball);
                if (selfBall.speed > 0) {
                    // console.log(selfBall.rb2D.isAwake(), "=selfBall=", selfBall.speed);
                    selfBall.scrollSpeed = selfBall.ballId % 4 + 1;
                    let ax = selfBall.ballId % 4;
                    let ay = Math.floor(selfBall.ballId / 4);
                    let az = Math.sqrt(16 - ax * ax - ay * ay);
                    let len = Math.sqrt(16);
                    selfBall.scrollAxis = v3(ax / len, ay / len, az / len);
                }
                if (otherBall.speed > 0) {
                    // console.log(otherBall.rb2D.isAwake(), "=otherBall=", otherBall.speed);
                    otherBall.scrollSpeed = otherBall.ballId % 4 + 1;
                    let ax_o = otherBall.ballId % 4;
                    let ay_o = Math.floor(otherBall.ballId / 4);
                    let az_o = Math.sqrt(16 - ax_o * ax_o - ay_o * ay_o);
                    let len_o = Math.sqrt(16);
                    otherBall.scrollAxis = v3(ax_o / len_o, ay_o / len_o, az_o / len_o);
                }
            }
        }

    test8() {
        // let strName = "💔بحلم بيك💔𝑴🅐𝑿̿ᵗ̿ᵒ̿ᵖ";
        // let result = strName.split(' ').reverse();
        // console.log(result);
        // this.testLabel.string = result;
        // let str = "لقد أخطأ <color=#80BFFF> 💔بحلم بيك💔𝑴🅐𝑿̿ᵗ̿ᵒ̿ᵖ </color>! سيحصل <color=#80BFFF>helllo</color> على كرة حرة. sunriseeeeeee";
        // let str = "<color=#80BFFF> لقد أخطأ <color=#80BFFF> sunrise </color> سيحصل !<color=#80BFFF>helllo</color> على كرة حرة. sunriseeeee </color>";
        // let str = ".على كرة حرة <color=#80BFFF> dearer </color> سيحصل !<color=#80BFFF> Hayyakom_7812244 </color> لقد أخطأ";
        // this.testRichLabel.string = str;
        // let str1 = ".لقد أخطأ hello! سيحصل surniseee على كرة حرة";
        // this.calWidth(str1);

        this.tipLabel.string = ".لقد أخطأ المتنافس! يمكنك وضع الكرة الرئيسية الخاصة بك بحرية";
    }


    public calWidth(str: string): any {
        //清零
        this.tempStrArr.length = 0;


        let length = this.spliteLine(str);



        let maxWidth = this.maxWidth;

        let tempStrArr = this.tempStrArr;

        let oneWidht = tempStrArr.length > 1 ? maxWidth : length;

        let newStr = tempStrArr.length > 1 ? tempStrArr.join('\n') : str;

        let line = null;

        if (newStr) {

            line = newStr.match(/\n/g) || [];

        }

        if (line == null) {

            line = [];

        }

        let chatData: any = {

            isMultiline: tempStrArr.length > 1,

            oneWidth: oneWidht,

            chatStr: newStr,

            line: line.length + 1,

            time: 0

        };

        return chatData;

    }

    private widthS = 14;

    private tempStrArr = [];

    /**最大分割宽度 */

    private maxWidth = 600;

    private maxLandWidth = 600;

    private spliteLine(str: string = "") {

        // this.testRichLabel.string = str;

        // this.testlabel.updateRenderData(true);

        // let actWidth = this.testRichLabel.getComponent(UITransform).width;

        // let max = 420;//loginData.islandscape ? this.maxLandWidth : this.maxWidth;

        // if (actWidth <= max) {

        //     this.tempStrArr.push(str);

        //     return actWidth;

        // }

        // let index = 0;

        // let length = 0;

        // let widthOne = Math.ceil(actWidth / str.length);

        // length = Math.floor(max / widthOne);

        // // let reg = /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/ig;
        // let reg = /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|/ig;

        // let isEmoji = str.search(reg) != -1;

        // if (isEmoji) //存在一个表情，当作表情行处理
        // {
        //     let emoji = str.substring(length - 2, length); //截取末尾的表情字符

        //     if (emoji.search(reg) == -1) {

        //         length--;

        //     }
        //     //只找两个字节的表情
        // }

        // let newStr = str.substring(0, length);

        // let resultSpace = newStr.match((/[\s]/g)) || [];

        // let isSpace = resultSpace.length > 0;

        // for (let i = newStr.length - 1; i >= 0; i--) {
        //     let s = newStr.charAt(i);
        //     if (isSpace) //有空格的文本，按照空格进行最大宽度拆分
        //     {
        //         index = i + 1;
        //         if (s.search(/\s/) == -1) {
        //             continue;
        //         }

        //         resultSpace.pop();
        //         if (resultSpace.length == 0) //不用在find, 开始和中文一样单独的遍历
        //         {
        //             isSpace = false;
        //         }
        //     }

        //     else //如果没有空格的文本 直接从尾部开始每个单词判断拆分位置
        //     {
        //         index = i + 1;
        //     }

        //     let temp = newStr.substring(0, index)
        //     this.testRichLabel.string = temp;

        //     // this.testlabel.updateRenderData(true)//._forceUpdateRenderData(true);
        //     actWidth = this.testRichLabel.getComponent(UITransform).width;

        //     if (actWidth < max) { //满足最大宽度，直接跳出
        //         newStr = temp;
        //         break;

        //     }
        // }

        // if (index == 0) //特殊情况：出现有空格，但是找不大满足最大宽度的文本，则默认不处理
        // {
        //     index = length;
        // }

        // this.tempStrArr.push(newStr);
        // str = str.slice(index);
        // this.spliteLine(str)
    }
}

