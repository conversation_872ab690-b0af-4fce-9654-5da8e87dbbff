import { _decorator, screen, view, game, ResolutionPolicy, Game, director, profiler, find } from 'cc';
import Global from './core/data/Global';
import GameService from './game/data/GameService';
import { UIConf, uiManager } from './core/ui/UIManager';
import { UIView } from './core/ui/UIView';
import { GameUtil } from './core/tools/GameUtil';
import { EnumEvent, EventMgr } from './core/event/EventManager';
import AudioMgr from './core/tools/AudioMgr';
import './libs/WebViewJavascriptBridge.js';
import i18n from './core/tools/i18n';
import { GameNodePool } from './game/manager/GameNodePool';
import { ReconnectCtrl } from './common/reconnect/ReconnectCtrl';
import { JsbBrid, JsbEnumEvent } from './core/tools/JsbBridge';
import Utils from './core/tools/Utils';
export interface UISize {
    width: number;
    height: number;
}
/**必须从1开始 */
export enum UIID {
    Match = 1,
    BilliardGame = 2,
    Result = 3,

    Confirm = 10,
    // ReConnect = 11,
    Settings = 12,
    Rule = 13,
    }

/**
 * isPop: 带有弹出效果（show type 选择UIAddition）
 * isDialog：为弹窗属性标签用于打开关闭管理
 * UIView: 三种打开方式：Add， full， UISingle; 
 * 全屏的进行Full
 * 弹窗进行add
 * UISingle界面暂时没有此UI需求，设置后只出现当前的界面；其它界面会被关闭
 */
export let UIConfig: { [key: number]: UIConf } = {
    [UIID.Match]: { prefab: "prefab/match/matchVs", preventTouch: true },
    [UIID.BilliardGame]: { prefab: "prefab/gameUI", preventTouch: true },
    [UIID.Result]: { prefab: "prefab/gameOver/resultUI", preventTouch: true },
    [UIID.Confirm]: { prefab: "prefab/common/confirmDialog", isPop: false, preventTouch: true, isDialog: true, preventTouchClose: true },
    // [UIID.ReConnect]: { prefab: "prefab/common/reconnectUI", preventTouch: true, isDialog: true },
    [UIID.Settings]: { prefab: "prefab/common/settingDialog", isPop: false, preventTouch: true, isDialog: true, preventTouchClose: true },
    [UIID.Rule]: { prefab: "prefab/common/ruleWebView", preventTouch: false, isDialog: true },
}
const { ccclass, property } = _decorator;
@ccclass('Main')
export class Main extends UIView {
    private _progressBox: HTMLElement = null;

    onLoad() {
        profiler.showStats()

        this.addEvent();
        JsbBrid.initNative();
        this.setWindowSize();
        uiManager.initUIConf(UIConfig);
        //TODO错误日志上报
        // window.onerror = function (msg, url, line, column, detail) {
            // Native.uploadLogMsg({ errorMsg: msg });
        // }
    }

    protected start(): void {
        JsbBrid.gameLoadingProgress();
        
        const scene = director.getScene();
        if (scene) {
            const canvas = scene.getChildByName('Canvas');
            if (canvas) {
                ReconnectCtrl.ins.mainGame = canvas.getChildByName('MainGame');
            }
        }
        if (Utils.IsBrowser()) {
            if (!Global.urlSearch) {
                Global.urlSearch = new URLSearchParams(location.search || '');
            }
            
            if (!!location.search) {
                var search: Array<string> = location.search.slice(1).split("&");
                search.forEach(str => {
                    var arr = str.split("=");
                    Global.userInfo[arr[0]] = arr[1];
                    Global.gameInfo[arr[0]] = arr[1];
                    if (arr[0] == 'language') {
                        Global.isArabic = (Number(arr[1]) == 2);
                    }
                    i18n.init(Global.isArabic ? 'ar' : 'en');
                });
                Global.userId = Global.userInfo.userId;
                // Global.gameInfo.instanceId = '5f684dbd69jgtzq';
                Global.gameInfo.url = `wss://dev-tyr.yalla.games?appId=ludo&packageType=3&version=1040300&userId=${Global.userId}`;
                Global.gameState = 0;
                this.initialized();
            }
        }

        // JsbBrid.getSystemInfo((msg) => {
        //     if (typeof msg == 'string') msg = JSON.parse(msg);
        //     Global.systemInfo = msg;
        //     Global.isArabic = msg.language == 2;
        //     i18n.init(Global.isArabic ? 'ar' : 'en');
        //     if (msg.hasOwnProperty('sound')) {
        //         AudioMgr.sound = msg.sound;
        //     }
        //     if (msg.hasOwnProperty('music')) {
        //         AudioMgr.music = msg.music;
        //     }
        //     if (msg.hasOwnProperty('version')) {
        //         Global.gameInfo.version = msg.version;
        //     }
        // });

    }

    addEvent() {
        EventMgr.addEventListener(JsbEnumEvent.ONLOGIN, this.onLogin, this);
        EventMgr.addEventListener(JsbEnumEvent.KICKEDOUT, this.onKickedOut, this);
        game.on(Game.EVENT_HIDE, () => {
            GameUtil.log("=切后台=");
            Global.isResume = false;
            EventMgr.raiseEvent(EnumEvent.HIDE);
            AudioMgr.playMusic(AudioMgr.audios.bg, 2);
        })
        game.on(Game.EVENT_SHOW, () => {
            GameUtil.log("=切前台=");
            Global.isResume = true;
            EventMgr.raiseEvent(EnumEvent.SHOW);
            AudioMgr.playMusic(AudioMgr.audios.bg, 1);
        })
    }
    /**原生通知游戏 msg */
    onLogin(eventName, msg) {
        if (msg) {
            if (msg.hasOwnProperty('userInfo')) {
                Global.userInfo = msg.userInfo;
                Global.userId = msg.userInfo.id;
                Global.userInfo.nickName = atob(msg.userInfo.name);
                Global.userInfo.faceUrl = msg.userInfo.avatar;
            }
            if (msg.hasOwnProperty('gameInfo')) {
                Global.gameInfo = msg.gameInfo;
                Global.gameState = msg.gameInfo.gameState || 0;
                Global.gameInfo.gameId = Global.gameId;
            }
            this.initialized();
        }
    }

    setWindowSize() {
        let visibleSize = view.getVisibleSize();
        let winsize = screen.windowSize;
        let ratio = winsize.width / winsize.height;
        let drs = view.getDesignResolutionSize();
        let drsRatio = drs.width / drs.height;
        let scale = 0;

        // 使用FIXED_WIDTH策略进行设备适配
        if (ratio > drsRatio) {
            // 宽屏设备：固定高度，宽度自适应
            view.setDesignResolutionSize(750, 1334, ResolutionPolicy.FIXED_HEIGHT);
            scale = winsize.height / drs.height;
        } else {
            // 窄屏设备：固定宽度，高度自适应
            view.setDesignResolutionSize(750, 1334, ResolutionPolicy.FIXED_WIDTH);
            scale = winsize.width / drs.width;
        }

        // 重新获取适配后的可见区域大小
        visibleSize = view.getVisibleSize();

        // 计算物理世界缩放补偿系数
        const physicsScaleY = visibleSize.height / 1334; // Y轴缩放比例
        const physicsScaleX = visibleSize.width / 750;   // X轴缩放比例

        // 存储物理世界缩放信息到Global，供物理系统使用
        Global.physicsScaleX = physicsScaleX;
        Global.physicsScaleY = physicsScaleY;
        Global.physicsScaleRatio = physicsScaleY / physicsScaleX; // 用于补偿Y轴拉伸

        Global.visibleWidth = visibleSize.width;
        Global.visibleHeight = visibleSize.height;
        Global.screen_scale = scale;

        console.log(`屏幕适配信息: 比例=${ratio}:${drsRatio}, 缩放=${scale}, 物理缩放X=${physicsScaleX.toFixed(3)}, Y=${physicsScaleY.toFixed(3)}, 补偿比例=${Global.physicsScaleRatio.toFixed(3)}`);
    }

    /**初始化完成 */
    private initialized() {
        //TODO::预加载重连ui，避免加载失败
        GameNodePool.instance.reconnectUI();
        // Global.gameState = 0;
        GameUtil.log(Global.urlSearch, "=Main=游戏状态GameState=", Global.gameState);
        let uId = Global.gameState < 1 ? UIID.Match : UIID.BilliardGame;
        uiManager.open(uId, null, null, () => {
            //打开界面同事，发起socket链接
            // Global.playerAccount.url = `ws://172.20.44.97:9091?appId=ludo&packageType=3&userId=${Global.playerAccount.idx}`;
            // console.log(Global.playerAccount.url+"===界面=====" + uId + "加载完成");
            if (uId == UIID.BilliardGame) GameService.instance.connectSocket();
            JsbBrid.removeMatchView();
        });
    }

    private onKickedOut() {
        GameService.instance.backHall(false, null, false);
    }

    removeEvent() {
        EventMgr.removeEventListener(JsbEnumEvent.ONLOGIN, this.onLogin, this);
        EventMgr.removeEventListener(JsbEnumEvent.KICKEDOUT, this.onKickedOut, this);
    }
    
    public onDestroy(): void {
        this.removeEvent();
        AudioMgr.clear();
    }
}
